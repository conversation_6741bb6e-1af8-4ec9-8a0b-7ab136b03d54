import logging
import os
import uuid
from datetime import datetime, timezone
from io import Bytes<PERSON>
from typing import Dict, Any, List, Optional

from django.db.models import Q
from django.contrib.auth import get_user_model
from aws_services.s3 import s3_service
from aws_services.sqs import sqs_service
from core.utils.helpers import calculate_file_hash, sanitize_filename
from core.exceptions import FileUploadError, ValidationError
from apps.files.models import UploadedFile
from business.models import BusinessCustomer  # Local import to avoid circular deps
from django.db.models import QuerySet

User = get_user_model()
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)  # ensure DEBUG always emitted for this module


class FileUploadService:
    """Service for handling file uploads.

    *Updated 07‑Jul‑2025 (2nd patch)*
    --------------------------------------------
    • Collect **all** tenant IDs for multi‑company customers, covering every
      join pattern we've encountered so far.
    • Bump tenancy‑resolution log to **INFO** so it shows even when root level
      is INFO.
    • Force module logger level to DEBUG so devs always see granular detail.
    """

    ALLOWED_EXTENSIONS = [
        ".csv",
        ".xlsx",
        ".xls",
        ".pdf",
        ".jpg",
        ".jpeg",
        ".png",
    ]
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50 MB

    def __init__(self):
        self.s3_service = s3_service
        self.sqs_service = sqs_service

    # ------------------------------------------------------------------
    # Public API
    # ------------------------------------------------------------------
    def upload_file(
        self,
        file_obj,
        user: User,
        is_signed: bool = False,
        file_type: str = "other",
        description: str = "",
        skip_duplicates: bool = True,
        update_existing: bool = False,
        customer_uuid: Optional[str] = None,
        category: Optional[str] = None,
    ) -> UploadedFile:
        """Upload *file_obj* to S3 following the ChatBook naming convention."""
        try:
            # 0️⃣  —  Validity checks
            self._validate_file(file_obj)

            file_id = str(uuid.uuid4())
            sanitised_name = sanitize_filename(file_obj.name)
            ext = os.path.splitext(sanitised_name)[1].lower()
            timestamp_utc = datetime.now(timezone.utc).strftime("%Y%m%dT%H%M%SZ")
            unique_filename = f"{user.id}_{timestamp_utc}{ext}"

            # 1️⃣  Resolve tenant IDs (now logged at INFO)
            tenant_ids = self._resolve_tenant_ids(user)
            
            # Convert tenant IDs back to business objects or None for global
            businesses = []
            for tid in tenant_ids:
                if tid == "global":
                    businesses.append(None)
                else:
                    from business.models import Business
                    try:
                        businesses.append(Business.objects.get(id=tid))
                    except Business.DoesNotExist:
                        logger.warning(f"Business with ID {tid} not found")
                        continue

            # 2️⃣  Customer + category segments
            if customer_uuid is None and getattr(user, "customer_profile", None):
                customer_uuid = str(user.id)

            allowed_categories = {"forms", "photos", "notes", "shared", "signatures"}
            category = (category or "forms").lower()
            if category not in allowed_categories:
                category = "forms"

            # 3️⃣  Duplicate detection
            file_hash = calculate_file_hash(file_obj)
            if skip_duplicates:
                existing = self._check_for_duplicate(file_hash, user)
                if existing:
                    logger.info("Duplicate upload → returning existing record %s", existing.file_id)
                    return existing

            # 4️⃣  Read bytes once
            payload: bytes = file_obj.read()

            first_key = None
            for tenant_id in tenant_ids:
                logger.warning("⇢ uploading to tenant_%s", tenant_id)
                tenant_prefix = f"tenant_{tenant_id}"

                # ----------------------------------------------------------
                # Determine S3 key based on category *and* signature status
                # ----------------------------------------------------------
                #  • If the caller explicitly sets category == "shared", we
                #    ALWAYS store under the shared folder – even when a
                #    customer_uuid is provided in the payload.
                #  • Otherwise (e.g. category == "forms"), we save inside the
                #    customer-specific hierarchy.
                if is_signed:
                    s3_key = (
                        f"{tenant_prefix}/customer_{customer_uuid}/{category or 'forms'}/{unique_filename}"
                    )
                else:
                    # generic template
                    s3_key = f"{tenant_prefix}/shared/{unique_filename}"

                if first_key is None:
                    first_key = s3_key

                meta = {
                    "file_id": file_id,
                    "uploaded_by": str(user.id),
                    "original_name": file_obj.name,
                    "file_type": file_type,
                    "signed": str(is_signed).lower(),
                }

                # Tagging – reflect whether this is a shared template or a
                # customer-specific asset so that lifecycle rules & IAM
                # policies can act accordingly
                tags = {
                    "tenant": str(tenant_id),
                    "form": "signed" if is_signed else "template",
                    "status": "uploaded",
                }

                self.s3_service.upload_file(
                    file_obj=BytesIO(payload),
                    key=s3_key,
                    content_type=getattr(file_obj, "content_type", "application/octet-stream"),
                    metadata=meta,
                    tags=tags,
                )
                logger.debug("Uploaded to %s", s3_key)

            # 5️⃣  DB row
            rec = UploadedFile.objects.create(
                file_id=file_id,
                file_name=sanitised_name,
                file_size=len(payload),
                file_type=file_type,
                content_type=getattr(file_obj, "content_type", "application/octet-stream"),
                file_hash=file_hash,
                s3_key=first_key,
                s3_bucket=self.s3_service.bucket_name,
                description=description,
                skip_duplicates=skip_duplicates,
                update_existing=update_existing,
                uploaded_by=user,
                status="uploaded",
            )

            if file_type.endswith("_import"):
                self._send_processing_message(rec)

            logger.info("File %s uploaded to %d tenant(s)", file_id, len(tenant_ids))
            return rec

        except Exception as exc:
            logger.exception("File upload failed → %s", exc)
            raise FileUploadError(f"Failed to upload file: {exc}")

    # ------------------------------------------------------------------
    # Helper methods
    # ------------------------------------------------------------------
    def _resolve_tenant_ids(self, user: User) -> List[str]:
        """
        Helper method to resolve all tenant IDs for a user.
        This is exposed for debugging purposes.
        
        Args:
            user: The user to resolve tenant IDs for
            
        Returns:
            List of tenant IDs (business IDs or "global")
        """
        businesses = []
        business_sources = {}  # business id -> source reason

        # a. BusinessUser membership (admin / staff)
        membership = (
            user.business_memberships.filter(is_active=True, is_primary=True).first()
            or user.business_memberships.filter(is_active=True).first()
        )
        if membership and membership.business:
            businesses.append(membership.business)
            business_sources[membership.business.id] = "membership"

        # b. Employee profile
        if hasattr(user, "employee_profile") and user.employee_profile:
            emp_business = user.employee_profile.business
            if emp_business and emp_business not in businesses:
                businesses.append(emp_business)
                business_sources[emp_business.id] = "employee"

        # c. Business owner (user owns one or more businesses)
        owner_business_qs = getattr(user, "owned_businesses", None)
        if owner_business_qs:
            for ob in owner_business_qs.all():
                if ob not in businesses:
                    businesses.append(ob)
                    business_sources[ob.id] = "owner"

        # d. Customer profile – potentially multiple businesses
        if hasattr(user, "customer_profile") and user.customer_profile:
            bc_qs = (
                BusinessCustomer.objects
                .filter(customer=user.customer_profile)
                .select_related("business")
            )
            for bc in bc_qs:
                if bc.business and bc.business not in businesses:
                    businesses.append(bc.business)
                    business_sources[bc.business.id] = "customer"

        # ----------------------------------------
        # Debug logging for business source mapping
        # ----------------------------------------
        tenant_ids = [str(biz.id) if biz else "global" for biz in businesses]
        if not tenant_ids:
            tenant_ids = ["global"]
            logger.info(f"Resolved tenant IDs for {user.email} → {tenant_ids}")
        else:
            logger.info(
                f"Resolved tenant IDs for {user.email} → {tenant_ids}",
            )

        return tenant_ids

    def _validate_file(self, file_obj):
        if file_obj.size > self.MAX_FILE_SIZE:
            raise ValidationError(
                f"File size exceeds maximum allowed size of {self.MAX_FILE_SIZE} bytes"
            )
        if not any(file_obj.name.lower().endswith(ext) for ext in self.ALLOWED_EXTENSIONS):
            raise ValidationError(
                f"File type not allowed. Allowed types: {', '.join(self.ALLOWED_EXTENSIONS)}"
            )
        if file_obj.size == 0:
            raise ValidationError("File is empty")

    def _check_for_duplicate(self, file_hash: str, user: User):
        return (
            UploadedFile.objects.filter(
                file_hash=file_hash,
                uploaded_by=user,
                status__in=["uploaded", "processing", "processed"],
            ).first()
        )

    def _send_processing_message(self, uploaded_file: UploadedFile):
        try:
            opts = {
                "skip_duplicates": uploaded_file.skip_duplicates,
                "update_existing": uploaded_file.update_existing,
                "file_type": uploaded_file.file_type,
            }
            msg_id = self.sqs_service.send_file_processing_message(
                file_id=uploaded_file.file_id,
                file_key=uploaded_file.s3_key,
                user_id=uploaded_file.uploaded_by.id,
                processing_options=opts,
            )
            uploaded_file.status = "processing"
            uploaded_file.save(update_fields=["status"])
            logger.info("Enqueued processing message %s for %s", msg_id, uploaded_file.file_id)
        except Exception:
            logger.exception("Failed to enqueue processing message – continuing")

    def get_upload_status(self, file_id: str, user: User) -> Dict[str, Any]:
        try:
            uf = UploadedFile.objects.get(file_id=file_id, uploaded_by=user)
            info: Dict[str, Any] = {
                "file_id": uf.file_id,
                "file_name": uf.file_name,
                "status": uf.status,
                "created_at": uf.created_at,
                "processed_at": uf.processed_at,
            }
            if hasattr(uf, "import_result"):
                ir = uf.import_result
                info.update(
                    {
                        "total_rows": ir.total_rows,
                        "successful_imports": ir.successful_imports,
                        "failed_imports": ir.failed_imports,
                        "success_rate": ir.success_rate,
                        "errors": ir.errors,
                        "warnings": ir.warnings,
                    }
                )
            return info
        except UploadedFile.DoesNotExist:
            raise FileUploadError(f"File not found: {file_id}")


file_upload_service = FileUploadService()
