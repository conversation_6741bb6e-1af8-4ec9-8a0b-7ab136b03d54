# Outbox Pattern Implementation

This document provides a comprehensive overview of the outbox pattern implementation for reliable event publishing in the Django notification system.

## 🎯 Overview

The outbox pattern ensures **transactional consistency** between domain operations and event publishing by:

1. **Storing events in the same database transaction** as domain changes
2. **Publishing events asynchronously** via a separate background process
3. **Guaranteeing at-least-once delivery** with proper retry mechanisms
4. **Maintaining event ordering** through sequence numbers

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Domain Event  │───▶│   Outbox Table   │───▶│  SNS Publisher  │
│   (Appointment) │    │  (OutboxEvent)   │    │   (Background)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       ▼
         ▼                       ▼               ┌─────────────────┐
┌─────────────────┐    ┌──────────────────┐    │   AWS SNS Topic │
│   Database      │    │   Same Database  │    │   (Reliable)    │
│   Transaction   │    │   Transaction    │    └─────────────────┘
└─────────────────┘    └──────────────────┘
```

## 📊 Database Schema

### OutboxEvent Model

```sql
CREATE TABLE notifications_outboxevent (
    id UUID PRIMARY KEY,
    sequence_number BIGINT UNIQUE NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    aggregate_type VARCHAR(50) NOT NULL,
    aggregate_id VARCHAR(100) NOT NULL,
    payload JSONB NOT NULL,
    metadata JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'pending',
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    scheduled_at TIMESTAMP NOT NULL,
    processed_at TIMESTAMP NULL,
    last_error TEXT,
    error_details JSONB DEFAULT '{}',
    published_to VARCHAR(200),
    message_id VARCHAR(100)
);
```

### Key Indexes

```sql
-- Processing efficiency
CREATE INDEX outbox_status_scheduled_idx ON notifications_outboxevent (status, scheduled_at);

-- Event type queries
CREATE INDEX outbox_type_created_idx ON notifications_outboxevent (event_type, created_at);

-- Aggregate lookups
CREATE INDEX outbox_aggregate_idx ON notifications_outboxevent (aggregate_type, aggregate_id);

-- Cleanup operations
CREATE INDEX outbox_created_idx ON notifications_outboxevent (created_at);
```

## 🔄 Event Lifecycle

### 1. Event Creation (Transactional)

```python
from django.db import transaction
from notifications.outbox import OutboxService

@transaction.atomic
def create_appointment(data):
    # 1. Save domain object
    appointment = Appointment.objects.create(**data)
    
    # 2. Create outbox event in same transaction
    OutboxService.create_appointment_event(
        appointment=appointment,
        event_type='appointment.created'
    )
    
    return appointment
```

### 2. Event Processing (Asynchronous)

```python
# Background worker polls for pending events
python manage.py process_outbox --daemon --interval=10
```

### 3. Event States

- **`pending`** → Ready for processing
- **`processing`** → Currently being processed
- **`published`** → Successfully published to SNS
- **`failed`** → Failed, will retry
- **`dead_letter`** → Exceeded max retries

## 🚀 Usage Examples

### Creating Events

```python
from notifications.outbox import OutboxService

# Appointment events
OutboxService.create_appointment_event(
    appointment=appointment,
    event_type='appointment.created'
)

# Direct notifications
OutboxService.create_direct_notification_event(
    user=user,
    message="Your appointment is confirmed!",
    additional_data={'appointment_id': 123}
)

# Waitlist events
OutboxService.create_waitlist_event(
    waitlist_entry=entry,
    event_type='waitlist.entry_created'
)
```

### Processing Events

```python
from notifications.publisher import OutboxPublisher

publisher = OutboxPublisher()
stats = publisher.process_pending_events(batch_size=50)
# Returns: {'processed': 10, 'published': 8, 'failed': 2, 'skipped': 0}
```

## 🔧 Management Commands

### Process Outbox Events

```bash
# One-time processing
python manage.py process_outbox

# Daemon mode (continuous processing)
python manage.py process_outbox --daemon --interval=10

# With cleanup
python manage.py process_outbox --cleanup --cleanup-days=7

# Custom batch size
python manage.py process_outbox --batch-size=100
```

### Command Options

- `--daemon`: Run continuously as a daemon
- `--interval=N`: Polling interval in seconds (default: 10)
- `--batch-size=N`: Events per batch (default: 50)
- `--cleanup`: Clean up old processed events
- `--cleanup-days=N`: Retention period (default: 7 days)
- `--max-iterations=N`: Limit iterations (for testing)

## 📈 Monitoring & Observability

### Key Metrics

1. **Event Volume**
   - Events created per minute
   - Events processed per minute
   - Backlog size (pending events)

2. **Processing Performance**
   - Average processing time per event
   - Batch processing time
   - Throughput (events/second)

3. **Error Rates**
   - Failed event percentage
   - Dead letter queue size
   - Retry distribution

4. **Latency**
   - Time from creation to processing
   - End-to-end notification delivery time

### Health Checks

```python
from notifications.outbox import OutboxService

# Check backlog size
pending_count = OutboxEvent.objects.filter(status='pending').count()

# Check failed events
failed_count = OutboxEvent.objects.filter(status='failed').count()

# Check dead letter events
dead_letter_count = OutboxEvent.objects.filter(status='dead_letter').count()

# Check processing lag
oldest_pending = OutboxEvent.objects.filter(
    status='pending'
).order_by('created_at').first()
```

## 🛡️ Error Handling & Resilience

### Retry Strategy

- **Exponential Backoff**: `min(300, 2^retry_count * 10)` seconds
- **Max Retries**: 3 attempts by default
- **Dead Letter**: Events exceeding max retries

### Error Categories

1. **Transient Errors** (Retryable)
   - Network timeouts
   - AWS service throttling
   - Temporary credential issues

2. **Permanent Errors** (Dead Letter)
   - Invalid event data
   - Missing user records
   - Malformed payloads

### Graceful Degradation

- Events are stored even if SNS is unavailable
- Publisher handles AWS credential expiration
- Database transactions ensure consistency

## 🧹 Cleanup Strategy

### Automatic Cleanup

```python
# Clean up processed events older than 7 days
OutboxService.cleanup_processed_events(older_than_days=7)
```

### Retention Policies

- **Published Events**: 7 days (configurable)
- **Failed Events**: Keep until resolved
- **Dead Letter Events**: Manual review required

### Cleanup Scheduling

```bash
# Add to crontab for daily cleanup
0 2 * * * cd /path/to/project && python manage.py process_outbox --cleanup --cleanup-days=7
```

## 🔒 Security Considerations

### Data Protection

- **Sensitive Data**: Avoid storing PII in event payloads
- **Encryption**: Use database-level encryption for sensitive fields
- **Access Control**: Restrict admin access to outbox events

### Event Integrity

- **Immutable Events**: Events should not be modified after creation
- **Audit Trail**: All state changes are logged with timestamps
- **Sequence Integrity**: Sequence numbers prevent event loss

## 🚀 Deployment

### Production Setup

1. **Database Indexes**: Ensure all indexes are created
2. **Background Worker**: Deploy as separate service/container
3. **Monitoring**: Set up alerts for failed events
4. **Scaling**: Multiple workers can process events concurrently

### Docker Deployment

```dockerfile
# Background worker container
FROM python:3.11
COPY . /app
WORKDIR /app
CMD ["python", "manage.py", "process_outbox", "--daemon"]
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: outbox-processor
spec:
  replicas: 2
  selector:
    matchLabels:
      app: outbox-processor
  template:
    metadata:
      labels:
        app: outbox-processor
    spec:
      containers:
      - name: processor
        image: your-app:latest
        command: ["python", "manage.py", "process_outbox", "--daemon"]
        env:
        - name: AWS_PROFILE
          value: "production"
```

## 📊 Performance Tuning

### Database Optimization

- **Connection Pooling**: Use pgbouncer for PostgreSQL
- **Batch Processing**: Process events in batches
- **Index Maintenance**: Regular VACUUM and ANALYZE

### Publisher Optimization

- **Concurrent Processing**: Multiple worker processes
- **Batch SNS Publishing**: Group messages when possible
- **Circuit Breaker**: Pause processing during AWS outages

## 🧪 Testing

### Unit Tests

```python
from notifications.outbox import OutboxService
from notifications.publisher import OutboxPublisher

def test_outbox_event_creation():
    event = OutboxService.create_direct_notification_event(
        user=user,
        message="Test message"
    )
    assert event.status == 'pending'
    assert event.sequence_number > 0

def test_event_processing():
    publisher = OutboxPublisher()
    stats = publisher.process_pending_events()
    assert stats['processed'] > 0
```

### Integration Tests

```bash
# Run comprehensive outbox pattern test
python test_outbox_pattern.py
```

## 🎉 Benefits Achieved

✅ **Transactional Consistency**: Events and domain changes are atomic  
✅ **Reliable Delivery**: At-least-once delivery guarantee  
✅ **Ordered Processing**: Events processed in sequence  
✅ **Fault Tolerance**: Graceful handling of failures  
✅ **Observability**: Complete audit trail and monitoring  
✅ **Scalability**: Horizontal scaling of event processing  
✅ **Maintainability**: Clean separation of concerns  

The outbox pattern implementation provides a robust, scalable, and maintainable solution for reliable event publishing in your Django application.
