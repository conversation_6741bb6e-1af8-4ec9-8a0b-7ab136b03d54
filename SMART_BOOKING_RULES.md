# Smart Booking Rules

This document describes the Smart Booking Rules feature that allows businesses to configure intelligent availability filtering for their online booking system.

## Overview

Smart Booking Rules provide businesses with configurable options to modify how available appointment times are presented to customers. These rules can be enabled/disabled per business and applied individually or in combination.

## Available Rules

### 1. Bookend Slots (Rule ID: 1)
**Description**: Shows only the first and last available time slots of the day per stylist.

**Use Case**: Useful for businesses that want to encourage customers to book either early morning or late afternoon appointments, leaving the middle of the day for walk-ins or other activities.

**Behavior**:
- If multiple time slots are available, only the first and last are returned
- If only one slot is available, that single slot is returned
- If no slots are available, an empty list is returned

### 2. Gapless Booking (Rule ID: 2)
**Description**: Fills gaps in availability by snapping to actual appointment end times rather than fixed interval grids.

**Use Case**: Maximizes booking efficiency by offering time slots that start immediately after existing appointments end, rather than waiting for the next standard interval.

**Behavior**:
- Analyzes existing appointments to find gaps
- Creates additional time slots that start at actual appointment end times
- Helps reduce downtime between appointments

**Note**: Current implementation is simplified and returns original availability. Full implementation would require detailed appointment analysis.

### 3. Tentative Hold (Rule ID: 3)
**Description**: Exposes partial time slots when remaining time is within a configurable tolerance threshold.

**Use Case**: Allows customers to book appointments even when there isn't a full service duration available, if the shortfall is within acceptable limits.

**Configuration**:
- `tolerance_minutes`: Configurable threshold (default: 30 minutes)
- Can be set per business in the admin interface

**Behavior**:
- Checks if remaining time at end of day is within tolerance
- Exposes partial slots with special "tentative" marking
- Includes information about potential overtime

**Note**: Current implementation is simplified. Full implementation would require business hours analysis.

## API Usage

### Endpoint
```
GET /api/v1/businesses/{business_id}/appointments/available-times/
```

**Note**: The old endpoint `/api/v1/appointments/available-times/` is deprecated and returns HTTP 410 GONE.

### Parameters

**Path Parameters**:
- `business_id`: ID of the business (required)

**Required Query Parameters**:
- `date`: Date in YYYY-MM-DD format
- `service_id`: ID of the requested service

**Optional Query Parameters**:
- `employee_id`: ID of specific employee

### Rule ID Parameter

The `rule_id` parameter accepts:
- `0` or omitted: Default behavior (no smart rules)
- `1`: Apply Bookend Slots rule
- `2`: Apply Gapless Booking rule  
- `3`: Apply Tentative Hold rule
- `1,3`: Apply multiple rules (comma-separated)

### Response Format

#### Without Smart Rules (Default)
```json
{
  "John Doe": [
    "2025-06-30T09:00:00+00:00",
    "2025-06-30T09:15:00+00:00",
    "2025-06-30T09:30:00+00:00"
  ]
}
```

#### With Smart Rules
```json
{
  "default": {
    "John Doe": [
      "2025-06-30T09:00:00+00:00",
      "2025-06-30T09:15:00+00:00", 
      "2025-06-30T09:30:00+00:00"
    ]
  },
  "smart": {
    "applied_rules": [
      {
        "rule_id": 1,
        "rule_name": "Bookend Slots"
      }
    ],
    "availability": {
      "John Doe": [
        "2025-06-30T09:00:00+00:00",
        "2025-06-30T09:30:00+00:00"
      ]
    }
  }
}
```

## Admin Configuration

### Configuring Smart Booking Rules

1. Navigate to Django Admin → Business → Online booking rules
2. Select the business you want to configure
3. Go to the "Appointment Settings" tab
4. Configure smart booking rules using the toggle switches:
   - **Bookend Slots**: Toggle to enable/disable (hover over "?" for description)
   - **Gapless Booking**: Toggle to enable/disable (hover over "?" for description)
   - **Tentative Hold**: Toggle to enable/disable (hover over "?" for description)
   - **Tentative Hold Tolerance**: Only visible when Tentative Hold is enabled (default: 30 minutes)

### Rule Management

- Smart booking rules are configured inline within the Online Booking Rules interface
- Each rule can be independently enabled/disabled using toggle switches
- Hover over the "?" icon next to each rule name to see a detailed description
- The tolerance field for Tentative Hold only appears when that rule is enabled
- Only enabled rules are applied when requested via API
- Changes take effect immediately after saving

## Examples

### Example 1: Basic Availability
```bash
curl "http://localhost:8000/api/v1/businesses/1/appointments/available-times/?date=2025-06-30&service_id=1"
```

### Example 2: Employee-Specific Availability
```bash
curl "http://localhost:8000/api/v1/businesses/1/appointments/available-times/?date=2025-06-30&service_id=1&employee_id=5"
```

### Example 3: Different Business
```bash
curl "http://localhost:8000/api/v1/businesses/2/appointments/available-times/?date=2025-06-30&service_id=1"
```

## Implementation Details

### Database Schema

**OnlineBookingRules Model** (Smart booking rules are inline fields):
- `enable_bookend_slots`: Boolean (default: False)
- `enable_gapless_booking`: Boolean (default: False)
- `enable_tentative_hold`: Boolean (default: False)
- `tentative_hold_tolerance`: Integer (default: 30, range: 5-120 minutes)

### Business Integration

The `OnlineBookingRules` model includes helper methods:
- `get_enabled_smart_rules()`: Returns list of enabled rules with metadata
- `get_smart_rule_by_type(rule_type)`: Gets specific rule if enabled
- `has_smart_rule(rule_type)`: Checks if rule type is enabled

### Admin Interface

Custom admin form with:
- Toggle switches for each smart booking rule
- Hover tooltips with predefined descriptions
- Conditional display of tolerance field (only when Tentative Hold is enabled)
- Custom CSS and JavaScript for enhanced user experience

### Error Handling

- Invalid rule IDs are gracefully ignored
- Disabled rules are skipped with warning logs
- Non-existent rules return default behavior
- Malformed rule_id parameters fall back to default behavior

## Testing

Comprehensive test coverage includes:
- Unit tests for each smart booking rule function
- Integration tests for API endpoint behavior
- Test cases for rule combinations
- Edge cases and error conditions
- Backward compatibility verification

## Future Enhancements

1. **Enhanced Gapless Booking**: Full implementation with appointment analysis
2. **Advanced Tentative Hold**: Business hours integration and overtime calculations
3. **Custom Rules**: Allow businesses to define custom availability filters
4. **Rule Scheduling**: Time-based rule activation (e.g., different rules for different days)
5. **Analytics**: Track rule usage and effectiveness metrics
