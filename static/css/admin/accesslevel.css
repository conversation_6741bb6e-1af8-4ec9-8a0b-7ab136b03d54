/* AccessLevel Admin Styles */
.permission-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.permission-section h2 {
  margin-top: 0;
  font-size: 24px;
  color: #333;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.permission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.permission-item:last-child {
  border-bottom: none;
}

.permission-description {
  flex: 1;
}

.permission-description h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: bold;
}

.permission-description p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.permission-controls {
  display: flex;
  gap: 10px;
}

.btn-view, .btn-modify {
  padding: 8px 15px;
  border-radius: 20px;
  border: 1px solid #ccc;
  background: white;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-view {
  color: #333;
}

.btn-modify {
  color: #333;
}

.btn-view.active, .btn-modify.active, .single-btn.active {
  background: #4CAF50 !important;
  color: white !important;
  border-color: #4CAF50 !important;
  font-weight: bold;
}

.btn-view:hover, .btn-modify:hover, .single-btn:hover {
  background-color: #f0f0f0;
  transition: all 0.2s ease;
}

.btn-view.active:hover, .btn-modify.active:hover, .single-btn.active:hover {
  background-color: #43a047 !important;
  color: white !important;
}

.btn-group {
  display: flex;
  overflow: hidden;
  border-radius: 20px;
  border: 1px solid #ccc;
}

.btn-group .btn-view, .btn-group .btn-modify {
  border-radius: 0;
  border: none;
  border-right: 1px solid #ccc;
  margin: 0;
}

.btn-group .btn-modify {
  border-right: none;
}

.single-btn {
  border-radius: 20px;
  border: 1px solid #ccc;
  background: white;
  padding: 8px 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.single-btn.active {
  background: #4CAF50 !important;
  color: white !important;
  border-color: #4CAF50 !important;
  font-weight: bold;
}

/* Hide the original permissions JSON field */
.field-permissions {
  display: none;
} 