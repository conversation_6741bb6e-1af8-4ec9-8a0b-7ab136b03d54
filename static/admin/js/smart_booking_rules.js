/**
 * Smart Booking Rules Admin JavaScript
 * Handles conditional display of tolerance field
 */

(function($) {
    'use strict';

    function initToleranceFieldToggle() {
        console.log('Initializing tolerance field toggle...');

        const tentativeField = $('#id_enable_tentative_hold');
        const toleranceRow = $('#id_tentative_hold_tolerance').closest('.form-row');

        if (!tentativeField.length || !toleranceRow.length) {
            console.log('Fields not found, exiting...');
            return;
        }

        function toggleToleranceField() {
            const isChecked = tentativeField.is(':checked');
            console.log('Toggling tolerance field, tentative checked:', isChecked);
            if (isChecked) {
                toleranceRow.show();
                toleranceRow.css('display', 'block');
            } else {
                toleranceRow.hide();
            }
        }

        // Initial state
        toggleToleranceField();

        // Bind event handler to the actual checkbox (which might be hidden inside the toggle)
        tentativeField.on('change', toggleToleranceField);

        // Also bind to any toggle switches that might control this field
        $(document).on('change', 'input[name="enable_tentative_hold"]', toggleToleranceField);

        console.log('Tolerance field toggle initialized successfully');
    }

    // Initialize when DOM is ready
    $(document).ready(function() {
        console.log('DOM ready, initializing tolerance field toggle...');
        setTimeout(function() {
            initToleranceFieldToggle();
        }, 100);
    });

    // Also try on window load as backup
    $(window).on('load', function() {
        console.log('Window loaded, checking tolerance field toggle...');
        setTimeout(function() {
            initToleranceFieldToggle();
        }, 200);
    });

})(django.jQuery || jQuery);
