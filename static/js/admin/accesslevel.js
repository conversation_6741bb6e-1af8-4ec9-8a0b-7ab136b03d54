document.addEventListener('DOMContentLoaded', function() {
    console.log('External accesslevel.js loaded');
    
    // Only run this script on AccessLevel admin pages
    if (document.getElementById('permission-ui')) {
        if (!window.hasInitializedPermissionUI) {
            console.log('Initializing permissions UI from external script');
            window.hasInitializedPermissionUI = true;
            initializePermissionsUI();
        } else {
            console.log('Permission UI already initialized. Skipping external initialization.');
        }
    }
});

function initializePermissionsUI() {
    // Get existing permissions JSON
    const permissionsField = document.querySelector('#id_permissions');
    let permissions = {};
    
    // Define a custom event to notify about permission changes
    const permissionsChangedEvent = new CustomEvent('permissionsChanged', {
        bubbles: true,
        detail: { source: 'accesslevel.js' }
    });
    
    try {
        if (permissionsField.value) {
            permissions = JSON.parse(permissionsField.value);
            console.log('External JS loaded permissions:', permissions);
        }
    } catch (e) {
        console.error('Error parsing permissions JSON:', e);
        console.log('Raw value:', permissionsField.value);
    }
    
    // Set initial button states based on saved permissions
    setInitialButtonStates(permissions);
    
    // Set initial toggle switch state
    const toggleSwitch = document.querySelector('input[data-permission="login_any_ip"]');
    if (toggleSwitch) {
        toggleSwitch.checked = permissions['login_any_ip'] === true;
        console.log(`External JS: Setting login_any_ip toggle to ${toggleSwitch.checked}`);
        
        // Add event listener for toggle switch
        toggleSwitch.addEventListener('change', function() {
            console.log(`External JS: Toggle switch changed to ${this.checked}`);
            // Update permissions immediately
            updatePermissions();
            updatePermissionsCount();
            
            // Dispatch event to notify other scripts
            document.dispatchEvent(permissionsChangedEvent);
        });
    }
    
    // Function to grant all permissions
    function grantAllPermissions() {
        console.log('External JS: Granting all permissions');
        document.querySelectorAll('.btn-view, .btn-modify, .single-btn').forEach(button => {
            // Apply both class and data attribute for consistency
            button.classList.add('active');
            button.setAttribute('data-active', 'true');
            button.style.backgroundColor = '#4CAF50';
            button.style.color = 'white';
            button.style.borderColor = '#4CAF50';
            button.style.fontWeight = 'bold';
        });
        
        // Also enable the toggle switch
        if (toggleSwitch) {
            toggleSwitch.checked = true;
        }
        
        // Check the full_access checkbox in the form
        const fullAccessCheckbox = document.querySelector('#id_full_access');
        if (fullAccessCheckbox) {
            fullAccessCheckbox.checked = true;
        }
        
        // Update permissions
        updatePermissions();
        updatePermissionsCount();
        
        // Dispatch event to notify other scripts
        document.dispatchEvent(permissionsChangedEvent);
    }
    
    // Function to revoke all permissions
    function revokeAllPermissions() {
        console.log('External JS: Revoking all permissions');
        document.querySelectorAll('.btn-view, .btn-modify, .single-btn').forEach(button => {
            // Ensure inactive state is set properly
            button.classList.remove('active');
            button.setAttribute('data-active', 'false');
            button.style.backgroundColor = 'white';
            button.style.color = '#333';
            button.style.borderColor = '#ccc';
            button.style.fontWeight = '500';
        });
        
        // Also disable the toggle switch
        if (toggleSwitch) {
            toggleSwitch.checked = false;
        }
        
        // Uncheck the full_access checkbox in the form
        const fullAccessCheckbox = document.querySelector('#id_full_access');
        if (fullAccessCheckbox) {
            fullAccessCheckbox.checked = false;
        }
        
        // Update permissions
        updatePermissions();
        updatePermissionsCount();
        
        // Dispatch event to notify other scripts
        document.dispatchEvent(permissionsChangedEvent);
    }
    
    // Add event listeners to Grant All and Revoke All buttons
    const grantAllButton = document.querySelector('#grant-all-permissions');
    if (grantAllButton) {
        grantAllButton.addEventListener('click', grantAllPermissions);
    }
    
    const revokeAllButton = document.querySelector('#revoke-all-permissions');
    if (revokeAllButton) {
        revokeAllButton.addEventListener('click', revokeAllPermissions);
    }
    
    // Add event listener to the full_access checkbox
    const fullAccessCheckbox = document.querySelector('#id_full_access');
    if (fullAccessCheckbox) {
        fullAccessCheckbox.addEventListener('change', function() {
            if (this.checked) {
                grantAllPermissions();
            } else {
                revokeAllPermissions();
            }
        });
    }
    
    // Add event listeners to all buttons
    document.querySelectorAll('.btn-view, .btn-modify, .single-btn').forEach(button => {
        // Remove any existing listeners to avoid duplicates
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
        
        newButton.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent any default behavior
            e.stopPropagation();
            
            const isActive = this.getAttribute('data-active') === 'true';
            console.log(`Button ${this.dataset.permission} clicked, current state: ${isActive}`);
            
            if (isActive) {
                // Toggle to inactive
                this.classList.remove('active');
                this.setAttribute('data-active', 'false');
                this.style.backgroundColor = 'white';
                this.style.color = '#333';
                this.style.borderColor = '#ccc';
                this.style.fontWeight = '500';
                console.log(`External JS: ${this.dataset.permission} toggled OFF`);
            } else {
                // Toggle to active
                this.classList.add('active');
                this.setAttribute('data-active', 'true');
                this.style.backgroundColor = '#4CAF50';
                this.style.color = 'white';
                this.style.borderColor = '#4CAF50'; 
                this.style.fontWeight = 'bold';
                console.log(`External JS: ${this.dataset.permission} toggled ON`);
            }
            
            // Update permissions object
            updatePermissions();
            updatePermissionsCount();
            
            // Dispatch event to notify other scripts
            document.dispatchEvent(permissionsChangedEvent);
        });
    });
    
    // Update permissions JSON when form is submitted
    const form = document.querySelector('form#accesslevel_form') || document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function() {
            updatePermissions();
            console.log('Form submitted, permissions updated by external JS');
            
            // Dispatch event to notify other scripts
            document.dispatchEvent(permissionsChangedEvent);
        });
    } else {
        console.error('Form not found by external JS');
    }
    
    // Update the permissions count display
    function updatePermissionsCount() {
        const activeButtons = document.querySelectorAll('.btn-view[data-active="true"], .btn-modify[data-active="true"], .single-btn[data-active="true"]');
        const toggleSwitch = document.querySelector('input[data-permission="login_any_ip"]');
        let count = activeButtons.length;
        
        // Add toggle switch if checked
        if (toggleSwitch && toggleSwitch.checked) {
            count += 1;
        }
        
        const countElement = document.querySelector('#permissions-count');
        if (countElement) {
            countElement.textContent = count;
        }
    }
    
    // Initial permissions count update
    updatePermissionsCount();
}

function setInitialButtonStates(permissions) {
    // Set initial states for all buttons based on the permissions object
    document.querySelectorAll('.btn-view, .btn-modify, .single-btn').forEach(button => {
        const permName = button.dataset.permission;
        if (permissions[permName] === true) {
            // Apply both class and data attribute for consistency
            button.classList.add('active');
            button.setAttribute('data-active', 'true');
            button.style.backgroundColor = '#4CAF50';
            button.style.color = 'white';
            button.style.borderColor = '#4CAF50';
            button.style.fontWeight = 'bold';
            console.log(`External JS: Setting ${permName} to active based on saved permissions`);
        } else {
            // Ensure inactive state is set properly
            button.classList.remove('active');
            button.setAttribute('data-active', 'false');
            button.style.backgroundColor = 'white';
            button.style.color = '#333';
            button.style.borderColor = '#ccc';
            button.style.fontWeight = '500';
        }
    });
}

function updatePermissions() {
    // Get the permissions field
    const permissionsField = document.querySelector('#id_permissions');
    
    // Collect all permissions from button states
    const newPermissions = {};
    
    // Get button permissions
    document.querySelectorAll('.btn-view, .btn-modify, .single-btn').forEach(button => {
        const permName = button.dataset.permission;
        const isActive = button.getAttribute('data-active') === 'true';
        newPermissions[permName] = isActive;
    });
    
    // Get toggle switch permissions
    const toggleSwitch = document.querySelector('input[data-permission="login_any_ip"]');
    if (toggleSwitch) {
        newPermissions['login_any_ip'] = toggleSwitch.checked;
        console.log(`External JS: Toggle switch value included: ${toggleSwitch.checked}`);
    }
    
    // Update the hidden permissions field with JSON formatted with pretty printing
    permissionsField.value = JSON.stringify(newPermissions, null, 2);
    console.log('External JS: Updated permissions JSON');
    
    // If the JSON display element exists, update it directly
    const jsonDisplay = document.getElementById('json-display');
    if (jsonDisplay) {
        jsonDisplay.textContent = JSON.stringify(newPermissions, null, 2);
    }
} 