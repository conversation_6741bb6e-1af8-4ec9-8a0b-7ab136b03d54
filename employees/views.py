from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin
from django.utils import timezone
from django.db.models import Avg, Count, Q
from django.http import JsonResponse

from services.models import EmployeeService
from .models import Employee
from .forms import EmployeeForm

class EmployeeListView(LoginRequiredMixin, ListView):
    model = Employee
    template_name = 'employees/employee_list.html'
    context_object_name = 'employees'
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Handle search query
        search_query = self.request.GET.get('search', None)
        if search_query:
            queryset = queryset.filter(
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query) |
                Q(stylist_level__icontains=search_query) |
                Q(email__icontains=search_query)
            )
            
        # Handle filtering
        level_filter = self.request.GET.get('level', None)
        if level_filter:
            queryset = queryset.filter(stylist_level=level_filter)
            
        status_filter = self.request.GET.get('status', None)
        if status_filter:
            queryset = queryset.filter(is_active=(status_filter == 'active'))
            
        booking_filter = self.request.GET.get('booking', None)
        if booking_filter == 'yes':
            queryset = queryset.filter(accept_online_bookings=True)
        elif booking_filter == 'no':
            queryset = queryset.filter(accept_online_bookings=False)
            
        # Handle sorting
        sort_by = self.request.GET.get('sort', 'name')
        if sort_by == 'name':
            queryset = queryset.order_by('first_name', 'last_name')
        elif sort_by == 'level':
            queryset = queryset.order_by('stylist_level')
            
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Basic statistics
        employees = self.get_queryset()
        context['active_employees'] = employees.filter(is_active=True).count()
        context['total_employees'] = employees.count()
        
        # Online booking statistics
        context['online_booking_count'] = employees.filter(accept_online_bookings=True).count()
        
        # Add search/filter parameters to context for form persistence
        context['search_query'] = self.request.GET.get('search', '')
        context['level_filter'] = self.request.GET.get('level', '')
        context['status_filter'] = self.request.GET.get('status', '')
        context['booking_filter'] = self.request.GET.get('booking', '')
        context['sort_by'] = self.request.GET.get('sort', 'name')
        
        return context

class EmployeeDetailView(LoginRequiredMixin, DetailView):
    model = Employee
    template_name = 'employees/employee_detail.html'
    context_object_name = 'employee'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['today'] = timezone.now().date()
        return context

class EmployeeCreateView(LoginRequiredMixin, CreateView):
    model = Employee
    form_class = EmployeeForm
    template_name = 'employees/employee_form.html'
    success_url = reverse_lazy('employees:employee_list')
    
    def form_valid(self, form):
        # Handle work days as a list from multiple checkbox inputs
        work_days = self.request.POST.getlist('work_days')
        if work_days:
            form.instance.work_days = ','.join(work_days)
        
        # Process any other special fields as needed
        return super().form_valid(form)

class EmployeeUpdateView(LoginRequiredMixin, UpdateView):
    model = Employee
    form_class = EmployeeForm
    template_name = 'employees/employee_form.html'
    
    def get_success_url(self):
        return reverse_lazy('employees:employee_detail', kwargs={'pk': self.object.pk})
    
    def form_valid(self, form):
        # Handle work days as a list from multiple checkbox inputs
        work_days = self.request.POST.getlist('work_days')
        if work_days:
            form.instance.work_days = ','.join(work_days)
            
        # Process any other special fields as needed
        return super().form_valid(form)
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        # Pre-populate the work days checkboxes
        if self.object.work_days:
            form.initial['work_days'] = self.object.work_days.split(',')
        return form

class EmployeeDeleteView(LoginRequiredMixin, DeleteView):
    model = Employee
    template_name = 'employees/employee_confirm_delete.html'
    success_url = reverse_lazy('employees:employee_list')
    context_object_name = 'employee'

class EmployeeScheduleView(LoginRequiredMixin, DetailView):
    model = Employee
    template_name = 'employees/employee_schedule.html'
    context_object_name = 'employee'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['today'] = timezone.now().date()
        
        # You can add more data here like appointments, time off records, etc.
        # Get employee appointments for the next 2 weeks
        start_date = timezone.now().date()
        end_date = start_date + timezone.timedelta(days=14)
        
        # If you have appointment data, you can load it here
        # context['appointments'] = Appointment.objects.filter(
        #     employee=self.object,
        #     start_time__date__gte=start_date,
        #     start_time__date__lte=end_date
        # ).order_by('start_time')
        
        # Format the dates for the calendar display
        context['start_date'] = start_date.strftime('%Y-%m-%d')
        context['end_date'] = end_date.strftime('%Y-%m-%d')
        
        return context
    
    def post(self, request, *args, **kwargs):
        """Handle AJAX requests for updating employee schedule"""
        employee = self.get_object()
        
        # Handle different types of updates
        action = request.POST.get('action')
        
        if action == 'update_settings':
            # Update employee schedule settings
            work_days = request.POST.getlist('work_days[]', [])
            if work_days:
                employee.work_days = ','.join(work_days)
            
            # Update work hours
            work_hours_start = request.POST.get('work_hours_start')
            work_hours_end = request.POST.get('work_hours_end')
            if work_hours_start:
                employee.work_hours_start = work_hours_start
            if work_hours_end:
                employee.work_hours_end = work_hours_end
            
            # Update other settings
            is_accepting_appointments = request.POST.get('is_accepting_appointments') == 'true'
            employee.accept_online_bookings = is_accepting_appointments
            
            employee.save()
            
            return JsonResponse({'status': 'success', 'message': 'Settings updated successfully'})
        
        elif action == 'add_time_off':
            # Add time off record
            # Implement time off record creation here
            return JsonResponse({'status': 'success', 'message': 'Time off added successfully'})
        
        # Handle other actions as needed
        
        return JsonResponse({'status': 'error', 'message': 'Invalid action'})
