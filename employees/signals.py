from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Employee, EmployeeWorkingHours
from django.db import transaction
from datetime import time
import logging

logger = logging.getLogger(__name__)

@receiver(post_save, sender=Employee, dispatch_uid="create_default_working_hours")
def create_default_working_hours(sender, instance, created, **kwargs):
    """
    Create default working hours for newly created employees.
    This decouples the creation logic from the Employee model.
    """
    if not created:
        return  # Only run for newly created employees
        
    logger.info(f"Creating default working hours for employee: {instance}")
    
    with transaction.atomic():
        days = [
            'monday',
            'tuesday', 
            'wednesday',
            'thursday',
            'friday',
            'saturday',
            'sunday'
        ]
        
        # Check if working hours already exist
        existing_days = set(instance.working_hours.values_list('day', flat=True))
        missing_days = [day for day in days if day not in existing_days]
        
        # Only create working hours for days that don't already exist
        for day in missing_days:
            EmployeeWorkingHours.objects.create(
                employee=instance,
                day=day,
                start_time=time(9, 0),  # 9 AM
                end_time=time(17, 0),   # 5 PM
                is_active=True
            )
            
        if missing_days:
            logger.info(f"Created working hours for days: {', '.join(missing_days)}") 