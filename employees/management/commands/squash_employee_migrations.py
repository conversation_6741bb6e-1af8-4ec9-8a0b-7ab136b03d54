import os
from django.core.management.base import BaseCommand
from django.db import connection
from django.conf import settings
from django.apps import apps
from django.db.migrations.loader import MigrationLoader
from django.core.management import call_command
from shutil import copyfile
from datetime import datetime

class Command(BaseCommand):
    help = 'Squash employee migrations and fix the db schema'

    def handle(self, *args, **options):
        self.stdout.write('Starting employee migrations squash process...')
        
        # 1. First backup the database manually
        self.stdout.write('Creating database backup...')
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"db.sqlite3.bak.{timestamp}"
        try:
            copyfile('db.sqlite3', backup_name)
            self.stdout.write(f"Created backup: {backup_name}")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error creating backup: {e}"))
            return
        
        # 2. Remove all existing employee migrations except __init__.py
        migrations_dir = os.path.join(settings.BASE_DIR, 'employees', 'migrations')
        self.stdout.write(f'Removing existing migrations from {migrations_dir}...')
        
        keep_files = ['__init__.py']
        for filename in os.listdir(migrations_dir):
            file_path = os.path.join(migrations_dir, filename)
            if os.path.isfile(file_path) and filename not in keep_files:
                os.remove(file_path)
                self.stdout.write(f'Removed {filename}')
        
        # 3. Remove migration records from django_migrations table
        self.stdout.write('Removing employee migration records from database...')
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM django_migrations WHERE app = 'employees'")
        
        # 4. Create fresh initial migration
        self.stdout.write('Creating new initial migration...')
        call_command('makemigrations', 'employees')
        
        # 5. Apply the migration
        self.stdout.write('Applying new migration...')
        call_command('migrate', 'employees')
        
        # 6. Fix any remaining issues with a SQL script if needed
        self.stdout.write('Running schema fix script if needed...')
        try:
            # Check if employee table has the correct schema
            with connection.cursor() as cursor:
                cursor.execute("PRAGMA table_info(employees_employee)")
                columns = {col[1] for col in cursor.fetchall()}
                
                if 'user_id' not in columns:
                    self.stdout.write('User ID column missing, applying SQL fix...')
                    sql_path = os.path.join(settings.BASE_DIR, 'fix_employee_schema.sql')
                    with open(sql_path, 'r') as f:
                        sql = f.read()
                        cursor.executescript(sql)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error applying SQL fix: {e}'))
        
        self.stdout.write(self.style.SUCCESS('Successfully squashed employee migrations!')) 