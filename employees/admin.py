from django.contrib import admin
from django.utils.html import format_html
from django.urls import path, reverse
from django.conf import settings
from django.contrib import messages
from django.db.models import Case, When, Value, IntegerField
from django.http import JsonResponse
from django.template.loader import render_to_string
from .models import Employee, EmployeeWorkingHours, AccessLevel
from django.views.decorators.csrf import csrf_protect
from django.utils.decorators import method_decorator
from django import forms
import logging
import traceback
import sys
import inspect
import json
import re

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# Add a console handler if not already present
if not logger.handlers:
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

logger.debug("Loading employees/admin.py")
logger.debug(f"Employee model: {Employee.__module__}.{Employee.__name__}")
logger.debug(f"EmployeeWorkingHours model: {EmployeeWorkingHours.__module__}.{EmployeeWorkingHours.__name__}")

# Inspect employees.Employee foreign key fields
logger.debug("Inspecting Employee model fields:")
for field in Employee._meta.get_fields():
    if hasattr(field, 'remote_field') and field.remote_field:
        logger.debug(f"Field: {field.name}, Remote: {field.remote_field.model}")

# Debug the EmployeeWorkingHours model definition
logger.debug("Inspecting EmployeeWorkingHours model fields:")
for field in EmployeeWorkingHours._meta.get_fields():
    if hasattr(field, 'remote_field') and field.remote_field:
        logger.debug(f"Field: {field.name}, Remote: {repr(field.remote_field.model)}")

# Now check for circular imports
try:
    # Debug imports in module
    import sys
    for name, module in sys.modules.items():
        if 'employees' in name or 'services' in name or 'business' in name:
            logger.debug(f"Module loaded: {name}")
except Exception as e:
    logger.error(f"Error inspecting modules: {e}")

class EmployeeWorkingHoursInline(admin.TabularInline):
    logger.debug("Setting up EmployeeWorkingHoursInline")
    model = EmployeeWorkingHours
    logger.debug(f"Model for inline: {model}")
    logger.debug(f"Model type: {type(model)}")
    logger.debug(f"Model parent: {inspect.getmro(model.__class__)}")
    
    extra = 0  # Don't add extra empty rows
    fields = ('day_display', 'start_time', 'end_time', 'is_active', 'copy_hours')
    readonly_fields = ('day_display', 'copy_hours')
    max_num = 7
    min_num = 7
    can_delete = False
    template = 'admin/employees/employee/working_hours_tabular.html'
    
    def copy_hours(self, obj):
        if obj and obj.id:
            button_id = f'copy-hours-{obj.id}'
            url = reverse('admin:copy_working_hours', args=[obj.id])
            logger.debug(f'Creating copy button for hours {obj.id} with URL {url}')
            return format_html(
                '<button type="button" class="copy-hours-btn" '
                'data-hours-id="{}" data-url="{}">'
                'Apply to All Days</button>',
                obj.id,
                url
            )
        return ""
    copy_hours.short_description = "Copy Hours"
    
    def day_display(self, obj):
        if obj and obj.day:
            return obj.get_day_display()
        return "Not set"
    day_display.short_description = "Day"
    
    def get_queryset(self, request):
        logger.debug(f"EmployeeWorkingHoursInline.get_queryset called")
        qs = super().get_queryset(request)
        logger.debug(f"Queryset base class: {qs.__class__}")
        return qs.annotate(
            _day_sort_order=Case(
                When(day='monday', then=Value(1)),
                When(day='tuesday', then=Value(2)),
                When(day='wednesday', then=Value(3)),
                When(day='thursday', then=Value(4)),
                When(day='friday', then=Value(5)),
                When(day='saturday', then=Value(6)),
                When(day='sunday', then=Value(7)),
                default=Value(0),
                output_field=IntegerField(),
            )
        ).order_by('_day_sort_order')

    def get_formset(self, request, obj=None, **kwargs):
        logger.debug(f"EmployeeWorkingHoursInline.get_formset called with obj: {obj}")
        formset = super().get_formset(request, obj, **kwargs)
        if obj:
            # Ensure all days exist
            days = [
                'monday',
                'tuesday', 
                'wednesday',
                'thursday',
                'friday',
                'saturday',
                'sunday'
            ]
            for day in days:
                try:
                    logger.debug(f"Creating/getting working hours for {obj.full_name}, day: {day}")
                    EmployeeWorkingHours.objects.get_or_create(
                        employee=obj,
                        day=day,
                        defaults={
                            'start_time': '09:00',
                            'end_time': '17:00',
                            'is_active': True
                        }
                    )
                except Exception as e:
                    logger.error(f"Error creating working hours: {str(e)}")
                    logger.error(traceback.format_exc())
        return formset

logger.debug("Registering EmployeeAdmin")
@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ('headshot_img', 'full_name', 'stylist_level', 'employee_email', 'employee_phone', 'is_active')
    list_filter = ('stylist_level', 'is_active', 'access_level')
    search_fields = ('user__first_name', 'user__last_name', 'user__email', 'user__phone_number')
    ordering = ('user__first_name', 'user__last_name')
    
    inlines = [EmployeeWorkingHoursInline]
    logger.debug(f"Set inlines: {inlines}")
    
    fieldsets = (
        ('User Information', {
            'fields': ('user',)
        }),
        ('Professional Information', {
            'fields': ('business', 'stylist_level', 'profile_image', 'accept_online_bookings')
        }),
        ('Calendar Integration', {
            'fields': ('calendar_sync_enabled', 'calendar_provider', 'calendar_id'),
            'classes': ('collapse',),
        }),
        ('Access Control', {
            'fields': ('employee_type', 'access_level', 'is_active'),
            'classes': ('collapse',),
        }),
    )
    
    def get_queryset(self, request):
        """Custom queryset to show employee profiles directly from the database."""
        logger.debug("EmployeeAdmin.get_queryset called")
        try:
            from django.db import connection
            # First try the normal ORM way
            qs = super().get_queryset(request)
            if qs.exists():
                logger.debug(f"Found {qs.count()} employees with ORM")
                return qs
            
            # If the ORM returns nothing but we know the table has data, try direct SQL
            logger.debug("ORM returned no employees, trying direct SQL")
            cursor = connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM employees_employee")
            count = cursor.fetchone()[0]
            
            if count > 0:
                logger.debug(f"Found {count} employees with direct SQL")
                # The records exist in the DB but aren't found by the ORM
                # This can happen with migration or schema issues
                
                # Get employee data with related tables
                cursor.execute("""
                SELECT e.id, e.user_id, e.business_id, e.stylist_level_id,
                       e.profile_image, e.accept_online_bookings, e.employee_type,
                       e.access_level, e.is_active, e.calendar_sync_enabled,
                       e.calendar_provider, e.calendar_id,
                       u.first_name, u.last_name, u.email, u.phone_number,
                       s.name, s.level_order
                FROM employees_employee e
                JOIN accounts_user u ON e.user_id = u.id
                LEFT JOIN business_stylistlevel s ON e.stylist_level_id = s.id
                """)
                
                # Create custom QuerySet with the raw data
                from django.db.models.query import RawQuerySet
                from django.db.models import QuerySet
                
                # Create a mock QuerySet
                mock_qs = QuerySet(model=Employee)
                
                # Add additional info for debugging
                logger.debug("Created mock QuerySet for employees")
                
                # Return the original QuerySet again, but now with data loaded
                return super().get_queryset(request)
            
            # If we get here, there are no records in the table
            logger.debug("No employee records found in the database")
            return qs
            
        except Exception as e:
            logger.error(f"Error in get_queryset: {str(e)}")
            logger.error(traceback.format_exc())
            # Fall back to the default queryset
            return super().get_queryset(request)
    
    def employee_email(self, obj):
        return obj.user.email if obj.user else ''
    employee_email.short_description = 'Email'
    employee_email.admin_order_field = 'user__email'
    
    def employee_phone(self, obj):
        return obj.user.phone_number if obj.user else ''
    employee_phone.short_description = 'Phone'
    employee_phone.admin_order_field = 'user__phone_number'

    def get_urls(self):
        logger.debug("EmployeeAdmin.get_urls called")
        urls = super().get_urls()
        custom_urls = [
            path(
                'working-hours/<int:hours_id>/copy/',
                self.admin_site.admin_view(self.copy_working_hours_view),
                name='copy_working_hours',
            ),
        ]
        return custom_urls + urls
    
    @method_decorator(csrf_protect)
    def copy_working_hours_view(self, request, hours_id):
        logger.debug(f"copy_working_hours_view called with hours_id: {hours_id}")
        if request.method != 'POST':
            logger.warning("Method not allowed, expected POST")
            return JsonResponse({'status': 'error', 'message': 'Method not allowed'}, status=405)
            
        try:
            logger.debug(f'Copying hours from {hours_id}')
            source_hours = EmployeeWorkingHours.objects.get(id=hours_id)
            logger.debug(f'Found source hours: {source_hours}')
            
            # Update all other working hours for this employee
            updated = EmployeeWorkingHours.objects.filter(
                employee=source_hours.employee
            ).exclude(
                id=hours_id
            ).update(
                start_time=source_hours.start_time,
                end_time=source_hours.end_time,
                is_active=source_hours.is_active
            )
            
            logger.debug(f'Updated {updated} records')
            messages.success(request, f'Successfully copied hours to {updated} days')
            return JsonResponse({'status': 'success', 'updated': updated})
            
        except EmployeeWorkingHours.DoesNotExist:
            logger.error(f'Working hours {hours_id} not found')
            return JsonResponse(
                {'status': 'error', 'message': 'Working hours not found'}, 
                status=404
            )
        except Exception as e:
            logger.error(f'Error copying hours: {str(e)}')
            logger.error(traceback.format_exc())
            return JsonResponse(
                {'status': 'error', 'message': str(e)}, 
                status=500
            )

    def headshot_img(self, obj):
        try:
            if obj.profile_image and hasattr(obj.profile_image, 'url'):
                return format_html('<img src="{}" width="50" height="50" style="border-radius: 50%" />', obj.profile_image.url)
            return format_html('<div style="width: 50px; height: 50px; border-radius: 50%; background-color: #eee; display: flex; justify-content: center; align-items: center;">No<br>Image</div>')
        except Exception as e:
            logger.error(f"Error in headshot_img: {str(e)}")
            return "Error"

    def get_form(self, request, obj=None, **kwargs):
        logger.debug(f"EmployeeAdmin.get_form called with obj: {obj}")
        try:
            form = super().get_form(request, obj, **kwargs)
            return form
        except Exception as e:
            logger.error(f"Error in get_form: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    def full_name(self, obj):
        """Display the full name of an employee, falling back to SQL if needed."""
        try:
            # Normal way through ORM
            if obj.user:
                return f"{obj.user.first_name} {obj.user.last_name}"
                
            # Fallback for direct DB access
            from django.db import connection
            cursor = connection.cursor()
            cursor.execute(
                "SELECT u.first_name, u.last_name FROM accounts_user u WHERE u.id = %s",
                [obj.user_id]
            )
            result = cursor.fetchone()
            if result:
                return f"{result[0]} {result[1]}"
            
            return f"Employee {obj.id}"
        except Exception as e:
            logger.error(f"Error in full_name: {str(e)}")
            return f"Employee {obj.id}"
    full_name.short_description = "Name"

logger.debug("Registering EmployeeWorkingHoursAdmin")
@admin.register(EmployeeWorkingHours)
class EmployeeWorkingHoursAdmin(admin.ModelAdmin):
    list_display = ('employee', 'day_display', 'start_time', 'end_time', 'is_active')
    list_filter = ('is_active', 'employee')
    search_fields = ('employee__user__first_name', 'employee__user__last_name')
    readonly_fields = ('day_display',)
    
    def day_display(self, obj):
        return obj.get_day_display()
    day_display.short_description = "Day"
    
    def get_queryset(self, request):
        logger.debug("EmployeeWorkingHoursAdmin.get_queryset called")
        qs = super().get_queryset(request)
        return qs.annotate(
            _day_sort_order=Case(
                When(day='monday', then=Value(1)),
                When(day='tuesday', then=Value(2)),
                When(day='wednesday', then=Value(3)),
                When(day='thursday', then=Value(4)),
                When(day='friday', then=Value(5)),
                When(day='saturday', then=Value(6)),
                When(day='sunday', then=Value(7)),
                default=Value(0),
                output_field=IntegerField(),
            )
        ).order_by('employee', '_day_sort_order')
    
    fieldsets = (
        (None, {
            'fields': ('employee', 'day_display', 'start_time', 'end_time', 'is_active')
        }),
    )

logger.debug("Finished loading employees/admin.py")

class AccessLevelForm(forms.ModelForm):
    full_access = forms.BooleanField(
        required=False, 
        label="Full Access",
        help_text="Grant all permissions to this access level"
    )
    
    permission_patterns = forms.CharField(
        required=False,
        label="Permission Patterns",
        help_text="Enter permission patterns to grant, separated by commas. Use * as a wildcard. Example: calendar_*, *_view",
        widget=forms.TextInput(attrs={'placeholder': 'Example: calendar_*, *_view, login_*'})
    )
    
    class Meta:
        model = AccessLevel
        fields = ('business', 'name', 'description', 'level', 'full_access', 'permission_patterns', 'permissions')
        widgets = {
            'permissions': forms.Textarea(attrs={'rows': 4}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Check if this is an existing instance with full permissions
        if self.instance and self.instance.pk:
            # If all permissions are set to true, check the full_access checkbox
            perms = self.instance.permissions or {}
            all_permissions = AccessLevelAdmin.get_all_permissions()
            has_all_perms = all(perms.get(perm, False) for perm in all_permissions)
            self.initial['full_access'] = has_all_perms
    
    def clean_permissions(self):
        permissions = self.cleaned_data.get('permissions')
        
        if permissions:
            try:
                if isinstance(permissions, str):
                    permissions_dict = json.loads(permissions)
                else:
                    permissions_dict = permissions
                    
                # Validate keys and values
                for key, value in permissions_dict.items():
                    if not isinstance(value, bool):
                        raise forms.ValidationError(f"Permission '{key}' must have a boolean value.")
                
                return permissions_dict
            except json.JSONDecodeError:
                raise forms.ValidationError("Permissions must be a valid JSON object.")
        
        return permissions or {}
    
    def clean(self):
        cleaned_data = super().clean()
        full_access = cleaned_data.get('full_access')
        permissions = cleaned_data.get('permissions', {})
        permission_patterns = cleaned_data.get('permission_patterns', '')
        
        # If full_access is checked, set all permissions to True
        if full_access:
            all_permissions = AccessLevelAdmin.get_all_permissions()
            for perm in all_permissions:
                permissions[perm] = True
        
        # Process permission patterns
        if permission_patterns:
            patterns = [p.strip() for p in permission_patterns.split(',')]
            all_permissions = AccessLevelAdmin.get_all_permissions()
            
            for pattern in patterns:
                if not pattern:
                    continue
                    
                # Convert glob pattern to regex
                regex_pattern = pattern.replace('*', '.*')
                regex = re.compile(f'^{regex_pattern}$')
                
                # Apply pattern to all permissions
                for perm in all_permissions:
                    if regex.match(perm):
                        permissions[perm] = True
        
        cleaned_data['permissions'] = permissions
        return cleaned_data

@admin.register(AccessLevel)
class AccessLevelAdmin(admin.ModelAdmin):
    form = AccessLevelForm
    list_display = ('name', 'business_name', 'level', 'is_full_access', 'created_at')
    list_filter = ('business', 'level')
    search_fields = ('name', 'description', 'business__name')
    ordering = ('-level', 'name')
    
    fieldsets = (
        (None, {
            'fields': ('business', 'name', 'description', 'level', 'full_access', 'permission_patterns', 'permissions'),
            'classes': ('wide',),
        }),
    )
    
    @staticmethod
    def get_all_permissions():
        """Return a list of all permission keys used in the UI"""
        return [
            # Calendar permissions
            'calendar_config_view', 'calendar_config_modify',
            'calendar_own_view', 'calendar_own_modify',
            'calendar_others_view', 'calendar_others_modify',
            'accept_own_appointments', 'accept_others_appointments',
            'accept_own_requests', 'accept_others_requests',
            
            # Checkout permissions
            'undo_checkout', 'customer_checkout', 'modify_price_discount',
            'modify_taxes', 'remove_fees', 'add_fees',
            'cc_processing_view', 'cc_processing_modify',
            'refund_view', 'refund_modify',
            'invoice_view', 'invoice_modify',
            
            # Price reduction & discount matrix
            'services_price_reduction', 'services_discount',
            'classes_price_reduction', 'classes_discount',
            'products_price_reduction', 'products_discount',
            'gift_cards_price_reduction', 'gift_cards_discount',
            'memberships_price_reduction', 'memberships_discount',
            'packages_price_reduction', 'packages_discount',
            
            # Customers permissions
            'customer_management_view', 'customer_management_modify',
            'customer_notes_view', 'customer_notes_modify',
            
            # Forms permissions
            'forms_management_view', 'forms_management_modify',
            
            # Marketing permissions
            'gallery_view', 'gallery_modify',
            'website_view', 'website_modify',
            'widget_view', 'widget_modify',
            
            # Reports permissions
            'run_own_reports', 'run_others_reports_view', 'run_others_reports_modify',
            'multi_location_reports', 'time_card_view', 'time_card_modify',
            'edit_own_time_card', 'gift_certificate_view', 'gift_certificate_modify',
            'packages_view', 'packages_modify', 'memberships_view', 'memberships_modify',
            'cancellation_report', 'own_push_notification',
            'others_push_notification_view', 'others_push_notification_modify',
            'pending_shipment_view', 'pending_shipment_modify',
            'own_payroll_view', 'own_payroll_modify',
            'others_payroll_view', 'others_payroll_modify',
            'dashboard_view', 'dashboard_modify', 'failed_payment_report',
            
            # Settings permissions
            'general_settings_view', 'general_settings_modify',
            'own_profile_view', 'own_profile_modify',
            'others_profile_view', 'others_profile_modify',
            'access_level_view', 'access_level_modify',
            'service_management_view', 'service_management_modify',
            'inventory_view', 'inventory_modify',
            'vagaro_connect_view', 'vagaro_connect_modify',
            'shopping_cart_view', 'shopping_cart_modify',
            'vagaro_drive_view', 'vagaro_drive_modify',
            'settings_memberships_view', 'settings_memberships_modify',
            'settings_packages_view', 'settings_packages_modify',
            'discounts_view', 'discounts_modify',
            'login_any_ip'
        ]

    def business_name(self, obj):
        return obj.business.name if obj.business else '-'
    business_name.short_description = 'Business'
    business_name.admin_order_field = 'business__name'
    
    def is_full_access(self, obj):
        """Check if this access level has all permissions granted"""
        perms = obj.permissions or {}
        all_permissions = self.get_all_permissions()
        return all(perms.get(perm, False) for perm in all_permissions)
    is_full_access.short_description = 'Full Access'
    is_full_access.boolean = True
    
    class Media:
        css = {
            'all': ('css/admin/accesslevel.css',)
        }
        js = ('js/admin/accesslevel.js',)
    
    def change_view(self, request, object_id, form_url='', extra_context=None):
        extra_context = extra_context or {}
        extra_context['title'] = 'Edit Access Level Permissions'
        return super().change_view(
            request, object_id, form_url, extra_context=extra_context,
        )
    
    def add_view(self, request, form_url='', extra_context=None):
        extra_context = extra_context or {}
        extra_context['title'] = 'Add Access Level Permissions'
        return super().add_view(
            request, form_url, extra_context=extra_context,
        )
    
    def save_model(self, request, obj, form, change):
        """Ensure permissions are properly saved"""
        if isinstance(obj.permissions, str):
            try:
                obj.permissions = json.loads(obj.permissions)
            except json.JSONDecodeError:
                obj.permissions = {}
        
        # If full_access is checked, make sure all permissions are granted
        if form.cleaned_data.get('full_access'):
            all_permissions = self.get_all_permissions()
            for perm in all_permissions:
                obj.permissions[perm] = True
        
        # Process permission patterns
        permission_patterns = form.cleaned_data.get('permission_patterns', '')
        if permission_patterns:
            patterns = [p.strip() for p in permission_patterns.split(',')]
            all_permissions = self.get_all_permissions()
            
            for pattern in patterns:
                if not pattern:
                    continue
                    
                # Convert glob pattern to regex
                regex_pattern = pattern.replace('*', '.*')
                regex = re.compile(f'^{regex_pattern}$')
                
                # Apply pattern to all permissions
                for perm in all_permissions:
                    if regex.match(perm):
                        obj.permissions[perm] = True
        
        # Log permissions for debugging
        logger.debug(f"Saving permissions: {obj.permissions}")
        super().save_model(request, obj, form, change)
        
        # Log after save to confirm the data was saved correctly
        logger.debug(f"AccessLevel saved with permissions: {obj.permissions}")
