# Data Script Dependencies and Execution Order

This document outlines the correct order to run the data scripts when setting up a fresh database. It shows the dependencies between scripts and explains what each script requires to run successfully.

## Dependencies Graph

```
create_clement_lash_initial_admin.py
↓
create_clement_lash_business.py
↓
create_clement_lash_employee_profile.py
↓
create_lash_services.py
↓
create_stylist_level_services.py
↓
create_stylist_level_addons.py
↓
create_customer.py
```

## Script Requirements

### 1. create_clement_lash_initial_admin.py
- **Creates**: Admin user and admin role
- **Dependencies**: None
- **Required Tables**: `accounts_user`, `accounts_role`

### 2. create_clement_lash_business.py
- **Creates**: Business, business settings, online booking rules, stylist levels
- **Dependencies**: Admin user
- **Required Tables**: `business_business`, `business_businesssettings`, `business_onlinebookingrules`, `business_stylistlevel`

### 3. create_clement_lash_employee_profile.py
- **Creates**: Employee profiles for the business
- **Dependencies**: Business, stylist levels
- **Required Tables**: `employees_employee`, `employees_schedule`, `employees_employeesettings`

### 4. create_lash_services.py
- **Creates**: Service categories and base services
- **Dependencies**: Business
- **Required Tables**: `services_servicecategory`, `services_service`, `services_addon`

### 5. create_stylist_level_services.py
- **Creates**: Services specific to stylist levels
- **Dependencies**: Services, stylist levels
- **Required Tables**: `services_stylistlevelservice`

### 6. create_employee_services.py
- **Creates**: Services linked to specific employees
- **Dependencies**: Services, employees
- **Required Tables**: `services_employeeservice`

### 7. create_stylist_level_addons.py
- **Creates**: Add-ons for different stylist levels
- **Dependencies**: Services, add-ons, stylist levels
- **Required Tables**: `services_stylistleveladdon`

### 8. create_customer.py
- **Creates**: Customer profiles
- **Dependencies**: Business
- **Required Tables**: `customers_customer`

## Running Scripts Manually

If you need to run scripts manually, follow this order:

```bash
python scripts/data/create_clement_lash_initial_admin.py
python scripts/data/create_clement_lash_business.py
python scripts/data/create_clement_lash_employee_profile.py
python scripts/data/create_lash_services.py
python scripts/data/create_stylist_level_services.py
python scripts/data/create_employee_services.py
python scripts/data/create_stylist_level_addons.py
python scripts/data/create_customer.py
```

## Common Issues

1. **Missing Tables**: If you get errors about missing tables, make sure migrations have been run with `python manage.py migrate`

2. **Foreign Key Errors**: These indicate scripts are being run out of order

3. **Duplicate Key Errors**: These may occur if scripts are run multiple times; most scripts include checks to prevent duplicates

## Reset Process

For a complete reset:
1. Back up the database
2. Delete the database
3. Run migrations
4. Run the scripts in the order listed above

Use `scripts/setup/reset_database.sh` to automate this process. 