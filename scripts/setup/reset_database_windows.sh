#!/bin/bash

# Reset Database Script - Comprehensive version
# This script performs a complete database reset:
# 1. Backs up the current database
# 2. Drops the current database
# 3. Wipes all migrations (keeping only __init__.py files)
# 4. Runs makemigrations and migrate
# 5. Runs all data scripts in the correct order

# Exit on any error
set -e

# Get the project root directory
SCRIPT_DIR="$(dirname "$0")"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
cd "$PROJECT_ROOT"

# Function to print section headers
print_header() {
    echo ""
    echo "======================================================"
    echo "  $1"
    echo "======================================================"
}

# Ask for confirmation
echo "⚠️  WARNING: This will completely reset your database ⚠️"
echo "This script will:"
echo "  1. Back up your current database"
echo "  2. Delete your current database"
echo "  3. Wipe all migration files (keeping only __init__.py)"
echo "  4. Create fresh migrations"
echo "  5. Run all data scripts in the correct order"
echo ""
echo "Are you sure you want to continue? (y/n)"
read -r response
if [[ ! "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    echo "Operation canceled."
    exit 0
fi

# STEP 1: Back up the database
print_header "BACKING UP DATABASE"
BACKUP_DIR="$PROJECT_ROOT/backups/db"
mkdir -p "$BACKUP_DIR"
TIMESTAMP=$(date +"%Y%m%d%H%M%S")
BACKUP_FILE="$BACKUP_DIR/db_backup_$TIMESTAMP.sqlite3"

# Check if database exists before backing up
DB_FILE="$PROJECT_ROOT/db.sqlite3"
if [ -f "$DB_FILE" ]; then
    echo "Creating database backup..."
    cp "$DB_FILE" "$BACKUP_FILE"
    echo "✅ Database backed up to: $BACKUP_FILE"
else
    echo "No database file found. Skipping backup."
fi

# STEP 2: Delete the database
print_header "DELETING CURRENT DATABASE"
if [ -f "$DB_FILE" ]; then
    rm "$DB_FILE"
    echo "✅ Database deleted."
else
    echo "No database file to delete. Skipping."
fi

# STEP 3: Wipe migrations
print_header "WIPING MIGRATION FILES"
echo "Finding migration files to remove..."
MIGRATION_FILES=$(find . -path "*/migrations/*.py" \
                 ! -name "__init__.py" \
                 ! -path "./venv/*" \
                 ! -path "./backups/*" \
                 | sort)

# Count and display the files to be removed
FILE_COUNT=$(echo "$MIGRATION_FILES" | wc -l | xargs)
echo "Found $FILE_COUNT migration files to remove."

# Remove the migration files
if [ "$FILE_COUNT" -gt "0" ]; then
    echo "$MIGRATION_FILES" | xargs rm -f
    echo "✅ Migration files removed."
else
    echo "No migration files to remove."
fi

# Check if all __init__.py files are present in migrations directories
print_header "CHECKING MIGRATION DIRECTORIES"
APP_DIRS=$(find . -type d -path "*/migrations" \
          ! -path "./venv/*" \
          ! -path "./backups/*")

echo "Ensuring all migration directories have __init__.py files..."
for DIR in $APP_DIRS; do
    INIT_FILE="$DIR/__init__.py"
    if [ ! -f "$INIT_FILE" ]; then
        echo "Creating missing $INIT_FILE"
        touch "$INIT_FILE"
    fi
done
echo "✅ All migration directories have __init__.py files."

# STEP 4: Create fresh migrations
print_header "CREATING FRESH MIGRATIONS"
echo "Running makemigrations..."
python manage.py makemigrations

echo "Running migrate..."
python manage.py migrate

# STEP 5: Run data scripts
print_header "RUNNING DATA SCRIPTS"
DATA_SCRIPTS=(
    "create_clement_lash_initial_admin.py"
    "create_clement_lash_business.py"
    "create_clement_lash_employee_profile.py"
    "create_lash_services.py"
    "create_stylist_level_services.py"
    "create_stylist_level_addons.py"
    "create_customer.py"
)

for SCRIPT in "${DATA_SCRIPTS[@]}"; do
    echo "Running $SCRIPT..."
    python "scripts/data/$SCRIPT"
    echo "✅ $SCRIPT completed."
    # Small delay between scripts
    sleep 1
done


print_header "RESET COMPLETE"
echo "✅ Database has been successfully reset and populated with initial data."
echo "You can now start the application with: python manage.py runserver" 