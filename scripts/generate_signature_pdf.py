#!/usr/bin/env python
"""
Generate a PDF from a customer signature and save it to AWS S3

Usage:
    python scripts/generate_signature_pdf.py --signature-id <id> --output <output_path>
    python scripts/generate_signature_pdf.py --customer-id <id> --signature-data <base64_data> --output <output_path>
"""
import os
import sys
import argparse
import json
import logging
import io
import re
from datetime import datetime
import django

# Add the project root directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Ensure logs directory exists
logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
os.makedirs(logs_dir, exist_ok=True)
print(f"Logs directory: {logs_dir}")

# Set up Django with our custom settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'scripts.settings_for_scripts')

try:
    django.setup()
    print("Django setup successful")
except Exception as e:
    print(f"Error setting up Django: {e}")
    sys.exit(1)

# Import Django models and utilities
try:
    from business.models import Business
    from forms.models import Signature
    from customers.models import CustomerProfile
    from employees.models import Employee
    from core.utils.pdf_utils import create_signature_pdf, signature_to_image
    from aws_services.s3 import S3Service
    from config.aws import aws_config
    print("Imports successful")
except Exception as e:
    print(f"Error importing modules: {e}")
    sys.exit(1)

# Set up logging
logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(logs_dir, 'signature_pdf_generation.log'))
    ]
)

# Initialize S3 service
try:
    s3_service = S3Service()
    logger.info("S3 service initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize S3 service: {e}")
    s3_service = None


def upload_to_s3(pdf_buffer, key_prefix, customer, business=None):
    """Upload PDF to S3 and return the URL"""
    if not s3_service:
        logger.warning("S3 service not available, skipping upload")
        return None
    
    try:
        # Generate a unique key for the file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        customer_id = customer.id if customer else "unknown"
        business_id = business.id if business else "unknown"
        
        # Get customer name for file naming
        customer_name = ""
        if customer and hasattr(customer, 'user'):
            first_name = customer.user.first_name or ""
            last_name = customer.user.last_name or ""
            if first_name or last_name:
                # Sanitize name for file path (remove special chars)
                sanitized_name = re.sub(r'[^a-zA-Z0-9_\-]', '_', f"{first_name}_{last_name}")
                customer_name = f"_{sanitized_name}"
        
        # Create S3 key with path structure: signatures/YYYY-MM-DD/business_id/customer_id_name_timestamp.pdf
        date_folder = datetime.now().strftime('%Y-%m-%d')
        s3_key = f"{key_prefix}/{date_folder}/{business_id}/{customer_id}{customer_name}_{timestamp}.pdf"
        
        logger.info(f"Uploading PDF to S3: {s3_key}")
        
        # Upload to S3
        s3_url = s3_service.upload_file(
            file_obj=pdf_buffer,
            key=s3_key,
            content_type='application/pdf',
            metadata={
                'customer_id': str(customer_id),
                'business_id': str(business_id),
                'timestamp': timestamp,
                'source': 'signature_pdf_generator'
            }
        )
        
        logger.info(f"PDF uploaded successfully to S3: {s3_url}")
        return s3_url
    
    except Exception as e:
        logger.error(f"Failed to upload PDF to S3: {e}", exc_info=True)
        return None


def generate_from_signature_id(signature_id, output_path):
    """Generate PDF from an existing signature"""
    try:
        logger.info(f"Generating PDF from signature ID: {signature_id}")
        signature = Signature.objects.get(id=signature_id)
    except Signature.DoesNotExist:
        logger.error(f"Signature with ID {signature_id} not found")
        return False, None
    
    # Get related objects
    customer = signature.customer
    business = signature.business
    employee = signature.employee
    form_submission = signature.form_submission
    
    # Prepare form data
    form_data = {
        'title': 'Signed Form',
        'content': {}
    }
    
    # If there's a form submission, get its data
    if form_submission:
        form_data['title'] = form_submission.form_template.name
        form_data['content'] = form_submission.form_data
    
    # Generate PDF
    logger.info(f"Creating PDF for customer: {customer}, business: {business}")
    pdf_buffer = create_signature_pdf(
        customer=customer,
        form_data=form_data,
        signature_data=signature.signature_data,
        business=business,
        employee=employee
    )
    
    # Upload to S3
    s3_url = upload_to_s3(
        pdf_buffer=pdf_buffer, 
        key_prefix='signatures',
        customer=customer,
        business=business
    )
    
    # Save to local file as backup
    try:
        with open(output_path, 'wb') as f:
            f.write(pdf_buffer.getvalue())
        logger.info(f"PDF saved locally: {output_path}")
    except Exception as e:
        logger.error(f"Failed to save PDF locally: {e}")
        if not s3_url:
            return False, None
    
    if s3_url:
        logger.info(f"PDF generation successful. S3 URL: {s3_url}, Local path: {output_path}")
        return True, s3_url
    else:
        logger.info(f"PDF generation successful but S3 upload failed. Local path: {output_path}")
        return True, None


def generate_from_signature_data(customer_id, signature_data, output_path, business_id=None, employee_id=None, form_data=None):
    """Generate PDF from signature data without saving to database"""
    try:
        logger.info(f"Generating PDF from signature data for customer ID: {customer_id}")
        customer = CustomerProfile.objects.get(id=customer_id)
    except CustomerProfile.DoesNotExist:
        logger.error(f"Customer with ID {customer_id} not found")
        return False, None
    
    # Get business and employee if IDs are provided
    business = None
    if business_id:
        try:
            business = Business.objects.get(id=business_id)
        except Business.DoesNotExist:
            logger.warning(f"Business with ID {business_id} not found")
    
    employee = None
    if employee_id:
        try:
            employee = Employee.objects.get(id=employee_id)
        except Employee.DoesNotExist:
            logger.warning(f"Employee with ID {employee_id} not found")
    
    # Parse form data if provided
    form_data_dict = {}
    if form_data:
        try:
            if os.path.isfile(form_data):
                with open(form_data, 'r') as f:
                    form_data_dict = json.load(f)
                logger.info(f"Form data loaded from file: {form_data}")
            else:
                form_data_dict = json.loads(form_data)
                logger.info("Form data parsed from string")
        except Exception as e:
            logger.warning(f"Could not parse form data: {e}")
            form_data_dict = {'title': 'Signed Form', 'content': {}}
    else:
        form_data_dict = {'title': 'Signed Form', 'content': {}}
    
    # Read signature data if it's a file path
    if os.path.isfile(signature_data):
        with open(signature_data, 'r') as f:
            signature_data = f.read().strip()
        logger.info(f"Signature data loaded from file: {signature_data}")
    
    # Generate PDF
    logger.info(f"Creating PDF for customer: {customer.id}, business: {business.id if business else 'None'}")
    pdf_buffer = create_signature_pdf(
        customer=customer,
        form_data=form_data_dict,
        signature_data=signature_data,
        business=business,
        employee=employee
    )
    
    # Upload to S3
    s3_url = upload_to_s3(
        pdf_buffer=pdf_buffer, 
        key_prefix='signatures',
        customer=customer,
        business=business
    )
    
    # Save to local file as backup
    try:
        with open(output_path, 'wb') as f:
            f.write(pdf_buffer.getvalue())
        logger.info(f"PDF saved locally: {output_path}")
    except Exception as e:
        logger.error(f"Failed to save PDF locally: {e}")
        if not s3_url:
            return False, None
    
    if s3_url:
        logger.info(f"PDF generation successful. S3 URL: {s3_url}, Local path: {output_path}")
        return True, s3_url
    else:
        logger.info(f"PDF generation successful but S3 upload failed. Local path: {output_path}")
        return True, None


def main():
    parser = argparse.ArgumentParser(description='Generate a PDF from a customer signature and save to S3')
    
    # Create mutually exclusive group for signature source
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--signature-id', type=int, help='ID of the existing signature')
    group.add_argument('--customer-id', type=int, help='ID of the customer (when using signature data)')
    
    # Other arguments
    parser.add_argument('--signature-data', help='Base64 encoded signature data or file path')
    parser.add_argument('--business-id', type=int, help='ID of the business')
    parser.add_argument('--employee-id', type=int, help='ID of the employee')
    parser.add_argument('--form-data', help='JSON form data as string or file path')
    parser.add_argument('--output', required=True, help='Output PDF file path')
    parser.add_argument('--skip-s3', action='store_true', help='Skip S3 upload and save locally only')
    
    args = parser.parse_args()
    
    if args.skip_s3:
        global s3_service
        s3_service = None
        logger.info("S3 upload disabled by command line argument")
    
    if args.signature_id:
        # Generate from existing signature
        success, s3_url = generate_from_signature_id(args.signature_id, args.output)
        if success:
            print(f"PDF generated successfully:")
            print(f"- Local path: {args.output}")
            if s3_url:
                print(f"- S3 URL: {s3_url}")
        else:
            print("PDF generation failed")
            sys.exit(1)
    elif args.customer_id:
        # Validate required arguments
        if not args.signature_data:
            parser.error("--signature-data is required when using --customer-id")
        
        # Generate from signature data
        success, s3_url = generate_from_signature_data(
            args.customer_id,
            args.signature_data,
            args.output,
            args.business_id,
            args.employee_id,
            args.form_data
        )
        
        if success:
            print(f"PDF generated successfully:")
            print(f"- Local path: {args.output}")
            if s3_url:
                print(f"- S3 URL: {s3_url}")
        else:
            print("PDF generation failed")
            sys.exit(1)


if __name__ == '__main__':
    main()