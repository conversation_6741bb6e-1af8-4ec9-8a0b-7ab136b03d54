#!/usr/bin/env python
import os
import sys
import django
from datetime import datetime, timezone
from django.db import transaction

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from django.db import connection
from accounts.models import User
from employees.models import Employee, EmployeeWorkingHours
from business.models import Business, StylistLevel, AccessLevel
from employees.admin import AccessLevelAdmin
import traceback

def reset_sequence(table_name):
    """Reset the SQLite sequence counter for a table"""
    with connection.cursor() as cursor:
        cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table_name}'")
        print(f"Reset ID sequence for {table_name}")

def get_all_permissions_dict():
    """Return a dictionary with all permissions set to True."""
    permissions = {}
    for perm in AccessLevelAdmin.get_all_permissions():
        permissions[perm] = True
    return permissions

@transaction.atomic
def create_clement_lash_employee_profiles():
    """Create employee profiles for <PERSON> Lash."""
    print("Starting creation of Clement Lash employee profiles...")
    
    # First, clean up any existing employee data
    print("Cleaning up existing employee data...")
    EmployeeWorkingHours.objects.all().delete()
    Employee.objects.all().delete()
    
    # Reset the ID sequences in SQLite
    reset_sequence('employees_employee')
    reset_sequence('employees_employeeworkinghours')
    
    try:
        # Get Clement Lash business
        business = Business.objects.get(name='Clement Lash')
        print(f"Found Clement Lash business with ID: {business.id}")
        
        # Get or create stylist levels
        master_level = StylistLevel.objects.get_or_create(
            business=business, name='Master Stylist', 
            defaults={'description': 'Expert lash artist with specialized training and years of experience', 'level_order': 2}
        )[0]
        
        senior_level = StylistLevel.objects.get_or_create(
            business=business, name='Senior Stylist', 
            defaults={'description': 'Experienced lash artist with advanced techniques', 'level_order': 1}
        )[0]
        
        print(f"Master Stylist level ID: {master_level.id}")
        print(f"Senior Stylist level ID: {senior_level.id}")
        
        # Get or create access levels
        # For full access, set all individual permissions to True
        full_access_level = AccessLevel.objects.get_or_create(
            business=business, name='Full Access',
            defaults={
                'description': 'Full access to all business functionality',
                'level': 10,
                'permissions': get_all_permissions_dict()
            }
        )[0]
        
        basic_access_permissions = {
            'calendar_own_view': True, 
            'calendar_own_modify': True,
            'customer_management_view': True,
            'customer_notes_view': True,
            'own_profile_view': True,
            'own_profile_modify': True
        }
        
        basic_access_level = AccessLevel.objects.get_or_create(
            business=business, name='Basic Access',
            defaults={
                'description': 'Basic access for service providers',
                'level': 1,
                'permissions': basic_access_permissions
            }
        )[0]
        
        print(f"Full Access level ID: {full_access_level.id}")
        print(f"Basic Access level ID: {basic_access_level.id}")
        
        # Employee data
        employees = [
            {
                'email': '<EMAIL>',
                'first_name': 'Serena', 
                'last_name': 'Zhou',
                'stylist_level': master_level,
                'employee_type': 'service_provider',
                'access_level': full_access_level,
                'working_days': ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Carol', 
                'last_name': 'Fan',
                'stylist_level': senior_level,
                'employee_type': 'service_provider',
                'access_level': basic_access_level,
                'working_days': ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
            }
        ]
        
        created_employees = []
        # Create or update users and employees
        for emp_data in employees:
            # Get or create user
            email = emp_data['email']
            try:
                user = User.objects.get(email=email)
                print(f"Found existing user: {user.email} (ID: {user.id})")
                
                # Update user information if needed
                user.first_name = emp_data['first_name']
                user.last_name = emp_data['last_name']
                user.save()
                print(f"Updated user information for {user.email}")
                
            except User.DoesNotExist:
                user = User.objects.create(
                    email=email,
                    first_name=emp_data['first_name'],
                    last_name=emp_data['last_name'],
                    is_active=True
                )
                user.set_password('Password123!')
                user.save()
                print(f"Created new user: {user.email} (ID: {user.id})")
            
            # Create employee record using Django ORM
            employee = Employee.objects.create(
                user=user,
                business=business,
                stylist_level=emp_data['stylist_level'],
                employee_type=emp_data['employee_type'],
                access_level=emp_data['access_level'],
                accept_online_bookings=True,
                is_active=True
            )
            created_employees.append(employee)
            print(f"Created employee record for {user.email} with ID: {employee.id}")
            
            # Make sure there are no existing working hours for this employee
            # (This should be handled by cascade delete, but let's be explicit)
            EmployeeWorkingHours.objects.filter(employee=employee).delete()
            
            # Create working hours
            for day in emp_data['working_days']:
                EmployeeWorkingHours.objects.create(
                    employee=employee,
                    day=day,
                    start_time='10:00',
                    end_time='18:00',
                    is_active=True
                )
            print(f"Created working hours for {user.email}")
        
        print("\nCreated Clement Lash employee profiles successfully!")
        return created_employees
        
    except Business.DoesNotExist:
        print("Error: Clement Lash business does not exist. Please create it first.")
    except Exception as e:
        print(f"Error: {str(e)}")
        print(traceback.format_exc())
        return []

if __name__ == '__main__':
    employees = create_clement_lash_employee_profiles()
    
    # Verify working hours
    if employees:
        for employee in employees:
            hours = EmployeeWorkingHours.objects.filter(employee=employee)
            print(f"\nWorking hours for {employee.user.first_name} {employee.user.last_name}:")
            for hour in hours:
                print(f"  - {hour.day.capitalize()}: {hour.start_time} to {hour.end_time}")
            print(f"Total working hours: {hours.count()}") 