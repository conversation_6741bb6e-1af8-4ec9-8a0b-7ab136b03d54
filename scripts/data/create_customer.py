import os
import sys
import django
from datetime import datetime, timezone
import random
import time

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from accounts.models import User, Role
from customers.models import CustomerProfile, CustomerTag
from business.models import Business, BusinessCustomer
from django.db import transaction
from phonenumber_field.phonenumber import PhoneNumber
from django.db import connection

def create_or_get_customer_role():
    """Create or get the customer role."""
    role, created = Role.objects.get_or_create(
        name=Role.Choices.CUSTOMER,
        defaults={
            'description': 'Regular customer with access to booking appointments and viewing their profile'
        }
    )
    return role

def create_or_get_customer_tags():
    """Create common customer tags if they don't exist."""
    tags = [
        {
            'name': 'VIP',
            'description': 'Very important customer',
            'color': '#FFC107'  # Gold color
        },
        {
            'name': 'Sensitive Eyes',
            'description': 'Customer has sensitive eyes',
            'color': '#E91E63'  # Pink color
        },
        {
            'name': 'Referred',
            'description': 'Customer was referred by another client',
            'color': '#4CAF50'  # Green color
        },
        {
            'name': 'New Client',
            'description': 'First-time customer',
            'color': '#2196F3'  # Blue color
        }
    ]
    
    created_tags = []
    for tag_data in tags:
        tag, created = CustomerTag.objects.get_or_create(
            name=tag_data['name'],
            defaults={
                'description': tag_data['description'],
                'color': tag_data['color']
            }
        )
        created_tags.append(tag)
    
    return created_tags

def create_real_customers():
    """Create two real customer examples."""
    customer_role = create_or_get_customer_role()
    tags = create_or_get_customer_tags()
    
    # Get the business for connecting customers
    try:
        business = Business.objects.get(name='Clement Lash')
    except Business.DoesNotExist:
        print("Business 'Clement Lash' not found. Please run create_clement_lash_initial_admin.py first.")
        return
    
    # Generate a timestamp suffix for uniqueness
    timestamp = int(time.time())
    
    # Real customer data
    customers_data = [
        {
            'first_name': 'Jennifer',
            'last_name': 'Lopez',
            'email': f'jennifer.lopez_{timestamp}@example.com',
            'phone_number': f'+14155552{timestamp % 10000:04d}',
            'card_on_file': True,
            'tags': ['VIP', 'Referred'],
            'notes': 'Regular client who comes in every 3 weeks. Prefers classic lashes.',
            'loyalty_points': 75
        },
        {
            'first_name': 'Michael',
            'last_name': 'Johnson',
            'email': f'michael.johnson_{timestamp}@example.com',
            'phone_number': f'+14155557{timestamp % 10000:04d}',
            'card_on_file': False,
            'tags': ['Sensitive Eyes', 'New Client'],
            'notes': 'First appointment on 2023-10-15. Has sensitive eyes, use gentle adhesive.',
            'loyalty_points': 10
        }
    ]
    
    # Show actual database schema
    with connection.cursor() as cursor:
        cursor.execute("SELECT * FROM sqlite_master WHERE type='table' AND name='customers_customerprofile';")
        schema_info = cursor.fetchone()
        print(f"Schema info: {schema_info}")
        cursor.execute("PRAGMA table_info(customers_customerprofile);")
        schema_details = cursor.fetchall()
        print(f"Schema details: {schema_details}")
    
    with transaction.atomic():
        created_customers = []
        
        for customer_data in customers_data:
            try:
                # Create user account
                user = User.objects.create_customer_user(
                    identifier=customer_data['email'],
                    password="SecurePass123",
                    email=customer_data['email'],
                    phone_number=customer_data['phone_number'],
                    first_name=customer_data['first_name'],
                    last_name=customer_data['last_name']
                )
                
                # Add customer role
                user.roles.add(customer_role)
                
                # Create customer profile using Django ORM
                profile = CustomerProfile.objects.create(
                    user=user,
                    card_on_file=customer_data['card_on_file']
                )
                
                print(f"Customer profile created: ID={profile.id}, User ID={user.id}")
                
                # Connect customer to business
                business_customer = BusinessCustomer.objects.create(
                    business=business,
                    customer=profile,
                    notes=customer_data['notes'],
                    loyalty_points=customer_data['loyalty_points'],
                    opt_in_marketing=True,
                    email_reminders=True,
                    sms_reminders=True
                )
                
                # Add tags to business customer rather than profile directly
                for tag_name in customer_data['tags']:
                    tag = CustomerTag.objects.get(name=tag_name)
                    business_customer.tags.add(tag)
                
                created_customers.append({
                    'name': f"{customer_data['first_name']} {customer_data['last_name']}",
                    'email': customer_data['email'],
                    'phone': customer_data['phone_number'],
                    'tags': customer_data['tags'],
                    'user_id': user.id,
                    'profile_id': profile.id
                })
            
            except Exception as e:
                print(f"Error creating customer {customer_data['email']}: {str(e)}")
                import traceback
                traceback.print_exc()
                continue
        
        # Print summary
        print(f"\nSuccessfully created {len(created_customers)} customers:")
        for idx, customer in enumerate(created_customers, 1):
            tags_str = ", ".join(customer['tags']) if customer['tags'] else "None"
            print(f"{idx}. {customer['name']} - {customer['email']} - Tags: {tags_str}")
            print(f"   User UUID: {customer['user_id']}")
            print(f"   Profile ID: {customer['profile_id']}")

if __name__ == '__main__':
    create_real_customers() 