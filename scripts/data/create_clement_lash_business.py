import os
import sys
import django
from datetime import datetime, timezone
import logging

"""
This script creates or updates the Clement Lash business with the following configuration:
- Business details (name, contact info, etc.)
- Business settings (booking lead time of 2 hours, booking window of 30 days)
- Online booking rules (cancellation/rescheduling policy of 24 hours)
- Stylist levels (<PERSON>, Senior Stylist, Master Stylist) - if the StylistLevel table exists

Note on StylistLevel: The script will check if the StylistLevel table exists and only attempt to create
stylist levels if it does. To create the StylistLevel table, you need to run migrations:
    python manage.py migrate business 0012_add_stylist_level

Migration 0012_add_stylist_level is currently dependent on migrations in services and employees apps,
so you may need to resolve those dependencies before running the migration.

Alternatively, this script can create the stylist levels table and required records directly using SQL
if you set the CREATE_STYLIST_LEVEL_TABLE environment variable to "true":
    CREATE_STYLIST_LEVEL_TABLE=true python scripts/data/create_clement_lash_business.py
"""

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from accounts.models import User, Role
from business.models import Business, Location, OnlineBookingRules
from django.db import transaction, connection
from django.db.utils import OperationalError, ProgrammingError

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def table_exists(table_name):
    """Check if a table exists in the database."""
    try:
        with connection.cursor() as cursor:
            # This SQL is SQLite-specific
            cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=%s",
                [table_name]
            )
            return cursor.fetchone() is not None
    except (OperationalError, ProgrammingError):
        return False

def create_stylist_level_table():
    """Create the StylistLevel table directly using SQL."""
    try:
        with connection.cursor() as cursor:
            # Create the table if it doesn't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS "business_stylistlevel" (
                    "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                    "name" varchar(100) NOT NULL,
                    "description" text NOT NULL,
                    "level_order" integer NOT NULL,
                    "is_default" bool NOT NULL,
                    "is_active" bool NOT NULL,
                    "created_at" datetime NOT NULL,
                    "updated_at" datetime NOT NULL,
                    "business_id" bigint NOT NULL REFERENCES "business_business" ("id") DEFERRABLE INITIALLY DEFERRED
                )
            """)
            
            # Create the unique constraint
            cursor.execute("""
                CREATE UNIQUE INDEX IF NOT EXISTS "unique_stylist_level_name_per_business" 
                ON "business_stylistlevel" ("business_id", "name")
            """)
            
            # Create an index for performance
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS "business_stylistlevel_business_id" 
                ON "business_stylistlevel" ("business_id")
            """)
            
            logger.info("Successfully created business_stylistlevel table using SQL")
            return True
    except (OperationalError, ProgrammingError) as e:
        logger.error(f"Error creating StylistLevel table: {str(e)}")
        return False

def create_stylist_levels_for_business(business_id, levels):
    """Create stylist levels directly using SQL."""
    try:
        with connection.cursor() as cursor:
            for level in levels:
                # Check if this level already exists
                cursor.execute(
                    """
                    SELECT id FROM business_stylistlevel 
                    WHERE business_id = %s AND name = %s
                    """,
                    [business_id, level['name']]
                )
                result = cursor.fetchone()
                
                if result:
                    # Update existing level
                    cursor.execute(
                        """
                        UPDATE business_stylistlevel 
                        SET description = %s, level_order = %s, is_default = %s, 
                            is_active = %s, updated_at = %s
                        WHERE id = %s
                        """,
                        [
                            level['description'], 
                            level['level_order'], 
                            level['is_default'], 
                            True,
                            datetime.now(timezone.utc),
                            result[0]
                        ]
                    )
                    logger.info(f"Updated stylist level: {level['name']}")
                else:
                    # Insert new level
                    cursor.execute(
                        """
                        INSERT INTO business_stylistlevel
                        (business_id, name, description, level_order, is_default, is_active, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        [
                            business_id,
                            level['name'],
                            level['description'],
                            level['level_order'],
                            level['is_default'],
                            True,
                            datetime.now(timezone.utc),
                            datetime.now(timezone.utc)
                        ]
                    )
                    logger.info(f"Created new stylist level: {level['name']}")
            
            return True
    except (OperationalError, ProgrammingError) as e:
        logger.error(f"Error creating stylist levels: {str(e)}")
        return False

def get_or_create_admin_user():
    """Get or create an admin user for the business."""
    try:
        # Try to find the admin user first
        user = User.objects.get(email='<EMAIL>')
        logger.info(f"Found existing admin user: {user.email}")
    except User.DoesNotExist:
        # Create a new admin user if not found
        logger.info("Admin user not found. Creating a new one...")
        try:
            user = User.objects.create_user(
                email='<EMAIL>',
                password='Admin123!',  # This should be changed immediately in production
                first_name='Admin',
                last_name='User',
                is_staff=True,
                is_superuser=True
            )
            logger.info(f"Created new admin user: {user.email}")
        except Exception as e:
            logger.error(f"Failed to create admin user: {str(e)}")
            raise
    
    return user

def setup_clement_lash_business():
    """
    Create or update the Clement Lash business with appropriate settings, 
    including online booking rules and stylist levels if supported.
    """
    with transaction.atomic():
        # Get or create the business admin role
        admin_role, created = Role.objects.get_or_create(
            name='admin',
            defaults={
                'description': 'Business administrator with full access to business settings and management'
            }
        )
        
        # Get or create the admin user
        user = get_or_create_admin_user()
        
        # Add business admin role to user if not already assigned
        if admin_role not in user.roles.all():
            user.roles.add(admin_role)
            logger.info(f"Added admin role to user {user.email}")
        
        # Create or update the business
        business, created = Business.objects.get_or_create(
            name='Clement Lash',
            defaults={
                'description': 'Premium lash extension services in a luxurious setting',
                'owner': user,
                'website': 'https://www.clementlash.com',
                'phone': '+***********',
                'email': '<EMAIL>',
                'created_at': datetime.now(timezone.utc),
                'updated_at': datetime.now(timezone.utc)
            }
        )
        
        if created:
            logger.info(f"Created new business: {business.name}")
        else:
            logger.info(f"Found existing business: {business.name}")
            # Update business details
            business.description = 'Premium lash extension services in a luxurious setting'
            business.website = 'https://www.clementlash.com'
            business.phone = '+***********'
            business.email = '<EMAIL>'
            business.updated_at = datetime.now(timezone.utc)
            business.save()
            logger.info(f"Updated business details for: {business.name}")
        
        # Create or update online booking rules
        booking_rules, rules_created = OnlineBookingRules.objects.get_or_create(
            business=business,
            defaults={
                'timezone': 'America/Los_Angeles',
                'currency': 'USD',
                'max_days_in_advance': 30,  # Match booking window in business settings
                'min_hours_before': 2,      # Match booking lead time (2 hours)
                'appointment_interval': 15,  # Default 15-minute intervals
                'allow_cancellation': True,
                'cancellation_hours_before': 24,  # 24 hours notice for cancellation
                'allow_rescheduling': True,
                'rescheduling_hours_before': 24,  # 24 hours notice for rescheduling
                'require_payment': False,
                # Enable all smart booking rules by default
                'enable_bookend_slots': True,
                'enable_gapless_booking': True,
                'enable_tentative_hold': True,
                'tentative_hold_tolerance': 30,  # 30 minutes tolerance
                'created_at': datetime.now(timezone.utc),
                'updated_at': datetime.now(timezone.utc)
            }
        )
        
        if not rules_created:
            # Update booking rules
            booking_rules.timezone = 'America/Los_Angeles'
            booking_rules.currency = 'USD'
            booking_rules.max_days_in_advance = 30       # 30 days in advance
            booking_rules.min_hours_before = 2           # 2 hours notice for booking
            booking_rules.appointment_interval = 15      # 15-minute intervals
            booking_rules.cancellation_hours_before = 24  # 24 hours notice for cancellation
            booking_rules.rescheduling_hours_before = 24  # 24 hours notice for rescheduling
            # Enable all smart booking rules by default
            booking_rules.enable_bookend_slots = True
            booking_rules.enable_gapless_booking = True
            booking_rules.enable_tentative_hold = True
            booking_rules.tentative_hold_tolerance = 30  # 30 minutes tolerance
            booking_rules.updated_at = datetime.now(timezone.utc)
            booking_rules.save()
            logger.info("Updated online booking rules with smart booking rules enabled")
        
        # Define stylist levels
        stylist_levels = [
            {
                'name': 'Junior Stylist',
                'description': 'Entry-level lash artist with foundational skills',
                'level_order': 0,
                'is_default': True
            },
            {
                'name': 'Senior Stylist',
                'description': 'Experienced lash artist with advanced techniques',
                'level_order': 1,
                'is_default': False
            },
            {
                'name': 'Master Stylist',
                'description': 'Expert lash artist with specialized training and years of experience',
                'level_order': 2,
                'is_default': False
            }
        ]
        
        # Check if we should create the StylistLevel table directly
        create_table = os.environ.get('CREATE_STYLIST_LEVEL_TABLE', 'false').lower() == 'true'
        
        # Check if StylistLevel table exists or should be created
        if table_exists('business_stylistlevel') or (create_table and create_stylist_level_table()):
            # Try using the Django ORM first
            try:
                from business.models import StylistLevel
                
                # Create or update each stylist level
                for level_data in stylist_levels:
                    level, level_created = StylistLevel.objects.update_or_create(
                        business=business,
                        name=level_data['name'],
                        defaults={
                            'description': level_data['description'],
                            'level_order': level_data['level_order'],
                            'is_default': level_data['is_default'],
                            'is_active': True
                        }
                    )
                    
                    action = "Created" if level_created else "Updated"
                    logger.info(f"{action} stylist level: {level.name}")
                
                # Log all stylist levels
                all_levels = StylistLevel.objects.filter(business=business).order_by('level_order')
                logger.info(f"Stylist levels:")
                for level in all_levels:
                    logger.info(f"  - {level.name}: {level.description}")
            except (ImportError, OperationalError, ProgrammingError) as e:
                # Fall back to direct SQL if ORM fails
                logger.warning(f"Could not use Django ORM: {str(e)}")
                if create_stylist_levels_for_business(business.id, stylist_levels):
                    logger.info("Created stylist levels using SQL")
                else:
                    logger.error("Failed to create stylist levels")
        else:
            logger.warning("StylistLevel table does not exist. Run migrations first to create it.")
            logger.warning("Or set CREATE_STYLIST_LEVEL_TABLE=true to create it directly with SQL.")
            logger.warning("Stylist levels were not created.")
        
        logger.info(f"Successfully configured Clement Lash business with ID: {business.id}")
        logger.info(f"Business settings: Booking lead time: {booking_rules.min_hours_before} hours (2 hours)")
        logger.info(f"Cancellation/rescheduling notice: {booking_rules.cancellation_hours_before} hours (24 hours)")

if __name__ == '__main__':
    setup_clement_lash_business() 