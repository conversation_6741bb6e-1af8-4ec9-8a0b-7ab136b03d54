#!/usr/bin/env python3
"""
<PERSON>ript to create StyleGroup records and migrate existing data
"""

import os
import sys
import django

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from services.models import StyleGroup, Service, LashServiceSuggestionRule
from business.models import Business


def create_style_groups():
    """Create the standard style groups"""
    style_groups_data = [
        {'name': 'Classic', 'description': 'Classic lash extensions with natural look', 'display_order': 10},
        {'name': 'Styling', 'description': 'Styling lash extensions with enhanced curl', 'display_order': 20},
        {'name': 'Volume', 'description': 'Volume lash extensions for fuller look', 'display_order': 30},
        {'name': 'Real Mink', 'description': 'Premium real mink lash extensions', 'display_order': 40},
    ]
    
    created_groups = {}
    
    print("Creating style groups...")
    for group_data in style_groups_data:
        style_group, created = StyleGroup.objects.get_or_create(
            name=group_data['name'],
            defaults={
                'description': group_data['description'],
                'display_order': group_data['display_order']
            }
        )
        
        if created:
            print(f"✅ Created style group: {style_group.name}")
        else:
            print(f"📋 Found existing style group: {style_group.name}")
        
        created_groups[group_data['name']] = style_group
    
    return created_groups


def migrate_service_style_groups(style_groups):
    """Migrate services from string style_group to foreign key"""
    print("\nMigrating service style groups...")
    
    # Map old string values to new StyleGroup objects
    style_group_mapping = {
        'Classic': style_groups['Classic'],
        'Styling': style_groups['Styling'],
        'Volume': style_groups['Volume'],
        'Real Mink': style_groups['Real Mink'],
        'Premium': style_groups['Real Mink'],  # Map Premium to Real Mink
    }
    
    services = Service.objects.all()
    updated_count = 0
    
    for service in services:
        # Get the old string value (this will be in the database as a string)
        old_style_group = None
        
        # Determine style group based on service name
        if 'Classic' in service.name:
            old_style_group = 'Classic'
        elif 'Styling' in service.name:
            old_style_group = 'Styling'
        elif 'Volume' in service.name:
            old_style_group = 'Volume'
        elif 'Premium' in service.name or 'Real Mink' in service.name:
            old_style_group = 'Real Mink'
        
        if old_style_group and old_style_group in style_group_mapping:
            service.style_group = style_group_mapping[old_style_group]
            service.save()
            updated_count += 1
            print(f"✅ Updated {service.name} → {style_group_mapping[old_style_group].name}")
    
    print(f"\n✅ Updated {updated_count} services with style group references")


def clean_old_rules():
    """Clean up old lash service suggestion rules"""
    print("\nCleaning up old lash service suggestion rules...")
    
    old_rules = LashServiceSuggestionRule.objects.all()
    count = old_rules.count()
    
    if count > 0:
        old_rules.delete()
        print(f"✅ Deleted {count} old rules")
    else:
        print("📋 No old rules to delete")


def main():
    """Main function"""
    print("Setting up StyleGroups and migrating data")
    print("=" * 60)
    
    try:
        # Create style groups
        style_groups = create_style_groups()
        
        # Migrate service style groups
        migrate_service_style_groups(style_groups)
        
        # Clean old rules (they'll be recreated with new structure)
        clean_old_rules()
        
        print("\n🎉 StyleGroup setup and data migration completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error during setup: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
