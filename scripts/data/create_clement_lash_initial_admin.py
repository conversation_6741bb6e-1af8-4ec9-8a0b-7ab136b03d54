import os
import sys
import django
from datetime import datetime, timezone
from uuid import UUID

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from accounts.models import User, Role
from business.models import Business, OnlineBookingRules
from django.db import transaction

def get_or_create_business_admin_role():
    """Create or get the business admin role."""
    role, created = Role.objects.get_or_create(
        name='admin',
        defaults={
            'description': 'Business administrator with full access to business settings and management'
        }
    )
    return role

def create_admin_user():
    """Create the admin user if it doesn't exist."""
    try:
        user = User.objects.get(email='<EMAIL>')
        print(f'Admin user already exists: {user.email}')
        return user
    except User.DoesNotExist:
        print('Creating new admin user: <EMAIL>')
        user = User.objects.create_superuser(
            identifier='<EMAIL>',
            password='AdminPass123!',  # This should be changed immediately after first login
            email='<EMAIL>',
            phone_number='+***********',  # Example US phone number
            first_name='Admin',
            last_name='User'
        )
        return user

def setup_clement_lash_business():
    """Set up the Clement Lash business and make the existing user a business admin."""
    with transaction.atomic():
        # Get or create the admin user
        user = create_admin_user()
        
        # Add business admin role
        business_admin_role = get_or_create_business_admin_role()
        user.roles.add(business_admin_role)
        
        # Update user details if not already set
        if not user.is_staff or not user.is_superuser:
            user.is_staff = True
            user.is_superuser = True
            user.save()

        # Create or get the business
        business, created = Business.objects.get_or_create(
            name='Clement Lash',
            defaults={
                'description': 'Premium lash extension services',
                'owner': user,
                'created_at': datetime.now(timezone.utc),
                'updated_at': datetime.now(timezone.utc)
            }
        )

        # Create or update online booking rules (instead of business settings)
        booking_rules, created = OnlineBookingRules.objects.get_or_create(
            business=business,
            defaults={
                'timezone': 'America/Los_Angeles',
                'currency': 'USD',
                'min_hours_before': 60,  # 60 hours notice required for booking
                'max_days_in_advance': 30,  # Can book up to 30 days in advance
                'appointment_interval': 15,  # 15-minute appointment intervals
                'created_at': datetime.now(timezone.utc),
                'updated_at': datetime.now(timezone.utc)
            }
        )

        print(f'Successfully set up Clement Lash business with ID: {business.id}')
        print(f'Business admin user: {user.email}')
        print(f'Business settings configured with timezone: {booking_rules.timezone}')

if __name__ == '__main__':
    setup_clement_lash_business() 