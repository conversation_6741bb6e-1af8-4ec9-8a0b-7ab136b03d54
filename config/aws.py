"""
AWS Configuration Module

Centralizes all AWS-related configuration and provides utilities for AWS service initialization.
Based on DJANGO_AWS_INTEGRATION.md recommendations.
"""
import os
import boto3
from botocore.exceptions import ClientError, NoCredentialsError


class AWSConfig:
    """AWS configuration and client management."""
    
    def __init__(self):
        self.region = os.getenv('AWS_DEFAULT_REGION', 'us-west-2')
        
        # Force profile to None in container environments to prevent any profile usage
        if self._is_container_environment_static():
            self.profile = None
            # Also clear the environment variable to prevent boto3 from picking it up
            os.environ.pop('AWS_PROFILE', None)
        else:
            self.profile = os.getenv('AWS_PROFILE', None)
        
        # S3 Configuration
        self.s3_bucket_name = os.getenv('AWS_STORAGE_BUCKET_NAME')
        self.s3_region = os.getenv('AWS_S3_REGION_NAME', self.region)
        
        # SQS Configuration
        self.sqs_file_processing_queue_url = os.getenv('AWS_SQS_FILE_PROCESSING_QUEUE_URL')
        
        # SNS Configuration for notifications (future use)
        self.sns_topic_arn = os.getenv('AWS_SNS_TOPIC_ARN')
        
        # Client cache
        self._s3_client = None
        self._sqs_client = None
        self._sns_client = None
    
    @property
    def s3_client(self):
        """Get or create S3 client using credential chain."""
        if self._s3_client is None:
            self._s3_client = self._create_client('s3', self.s3_region)
        return self._s3_client
    
    @property
    def sqs_client(self):
        """Get or create SQS client using credential chain."""
        if self._sqs_client is None:
            self._sqs_client = self._create_client('sqs', self.region)
        return self._sqs_client
    
    @property
    def sns_client(self):
        """Get or create SNS client using credential chain."""
        if self._sns_client is None:
            self._sns_client = self._create_client('sns', self.region)
        return self._sns_client
    
    def _create_client(self, service_name, region=None):
        """Create AWS client using credential chain."""
        region = region or self.region
        
        # Force no profile usage in ECS/container environments
        # ECS should always use IAM roles attached to tasks
        use_profile = self.profile and not self._is_container_environment()
        
        if use_profile:
            # Use specific profile for local development only
            session = boto3.Session(
                profile_name=self.profile,
                region_name=region
            )
            return session.client(service_name)
        else:
            # Use default credential chain (IAM roles for ECS)
            # Explicitly disable any profile usage by creating a fresh session
            session = boto3.Session(region_name=region)
            return session.client(service_name)
    
    def _is_container_environment(self):
        """Check if running in a container environment like ECS."""
        return self._is_container_environment_static()
    
    @staticmethod
    def _is_container_environment_static():
        """Static method to check if running in a container environment like ECS."""
        # ECS sets this environment variable
        return (
            os.getenv('AWS_EXECUTION_ENV', '').startswith('AWS_ECS') or
            os.getenv('ECS_CONTAINER_METADATA_URI') is not None or
            os.getenv('SERVICE_TYPE') in ['api', 'worker']  # Our custom indicator
        )
    
    def validate_configuration(self):
        """Validate AWS configuration and credentials."""
        errors = []
        
        # Check required configuration
        if not self.s3_bucket_name:
            errors.append("AWS_STORAGE_BUCKET_NAME is not configured")
        
        if not self.sqs_file_processing_queue_url:
            errors.append("AWS_SQS_FILE_PROCESSING_QUEUE_URL is not configured")
        
        # Test credentials
        try:
            self.s3_client.list_buckets()
        except NoCredentialsError:
            errors.append("No AWS credentials found")
        except ClientError as e:
            errors.append(f"AWS credentials invalid: {e}")
        
        return errors
    
    def get_s3_url(self, key):
        """Generate S3 URL for a given key."""
        return f"https://{self.s3_bucket_name}.s3.{self.s3_region}.amazonaws.com/{key}"


# Global AWS configuration instance
aws_config = AWSConfig()


# Legacy settings for backward compatibility
AWS_STORAGE_BUCKET_NAME = aws_config.s3_bucket_name
AWS_S3_REGION_NAME = aws_config.s3_region
AWS_DEFAULT_REGION = aws_config.region
AWS_PROFILE = aws_config.profile
AWS_SQS_FILE_PROCESSING_QUEUE_URL = aws_config.sqs_file_processing_queue_url
