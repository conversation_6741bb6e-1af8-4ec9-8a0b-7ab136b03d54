from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_protect
from django.http import HttpResponse, JsonResponse
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

@login_required
@csrf_protect
def signature_test_view(request):
    """
    Test view for the signature component
    """
    if request.method == 'POST':
        # Get form data
        customer_name = request.POST.get('customer_name')
        customer_email = request.POST.get('customer_email')
        signature_data = request.POST.get('signature_data')
        signature_completed = request.POST.get('signature_completed') == 'true'
        signature_s3_url = request.POST.get('signature_s3_url')
        
        # Log the submission
        logger.info(f"Signature test form submitted: {customer_name}, {customer_email}, signature_completed={signature_completed}")
        
        if signature_completed and signature_s3_url:
            logger.info(f"Signature S3 URL: {signature_s3_url}")
            
            # In a real application, you would save this to your database
            # For the test, we'll just return a success message
            return render(request, 'forms/signature_success.html', {
                'customer_name': customer_name,
                'signature_s3_url': signature_s3_url
            })
        else:
            # If signature is not completed, redirect back to the form with an error
            return render(request, 'forms/signature_test.html', {
                'error': 'Please complete and save your signature before submitting the form.',
                'customer_name': customer_name,
                'customer_email': customer_email
            })
    
    # GET request - show the form
    return render(request, 'forms/signature_test.html') 