from django.core.management.base import BaseCommand
from django.db import transaction
from forms.models import FormTemplate, BusinessRequiredForm
import json


class Command(BaseCommand):
    help = 'Sync mandatory forms with BusinessRequiredForm records'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        # Get all published form templates
        form_templates = FormTemplate.objects.filter(status='Published')
        
        created_count = 0
        updated_count = 0
        
        for form_template in form_templates:
            try:
                # Check if the form is marked as mandatory in its content
                content = form_template.content or {}
                settings = content.get('settings', {})
                is_mandatory = settings.get('mandatory', False)
                
                if is_mandatory:
                    # Check if BusinessRequiredForm record already exists
                    business_required_form, created = BusinessRequiredForm.objects.get_or_create(
                        business=form_template.business,
                        form_template=form_template,
                        defaults={
                            'is_required': True,
                            'required_for_new_customers': True,
                            'required_for_existing_customers': False,
                            'order': 0
                        }
                    )
                    
                    if created:
                        created_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'✓ Created BusinessRequiredForm for "{form_template.name}" (Business: {form_template.business.name})'
                            )
                        )
                    else:
                        # Update existing record to ensure it's marked as required
                        if not business_required_form.is_required:
                            if not dry_run:
                                business_required_form.is_required = True
                                business_required_form.save(update_fields=['is_required', 'updated_at'])
                            updated_count += 1
                            self.stdout.write(
                                self.style.WARNING(
                                    f'⚠ Updated BusinessRequiredForm for "{form_template.name}" (Business: {form_template.business.name}) - marked as required'
                                )
                            )
                else:
                    # Form is not mandatory, check if we should remove the BusinessRequiredForm record
                    try:
                        business_required_form = BusinessRequiredForm.objects.get(
                            business=form_template.business,
                            form_template=form_template
                        )
                        if not dry_run:
                            business_required_form.delete()
                        self.stdout.write(
                            self.style.WARNING(
                                f'🗑 Removed BusinessRequiredForm for "{form_template.name}" (Business: {form_template.business.name}) - form is not mandatory'
                            )
                        )
                    except BusinessRequiredForm.DoesNotExist:
                        # No BusinessRequiredForm record exists, which is correct
                        pass
                        
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'✗ Error processing form "{form_template.name}": {str(e)}'
                    )
                )
        
        # Summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write('SYNC SUMMARY:')
        self.stdout.write(f'Forms processed: {form_templates.count()}')
        self.stdout.write(f'BusinessRequiredForm records created: {created_count}')
        self.stdout.write(f'BusinessRequiredForm records updated: {updated_count}')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('\nThis was a dry run. No changes were made.'))
        else:
            self.stdout.write(self.style.SUCCESS('\nSync completed successfully!')) 