# Architecture Guide

## Overview

This document outlines the recommended architecture and organization for the Django backend, following industry best practices for maintainability, scalability, and clarity.

## Current Issues with Structure

### Problems Identified:

1. **Confusing Naming**: Both `services/` (Django app) and `api/services/` (utilities) exist
2. **Mixed Responsibilities**: AWS, file processing, and business logic mixed together
3. **Configuration Scattered**: AWS settings mixed with Django settings
4. **No Clear Separation**: Infrastructure concerns mixed with business logic
5. **Import Confusion**: Unclear dependencies and circular imports

## Recommended Architecture

### Layered Architecture Approach

```
┌─────────────────────────────────────────┐
│              API Layer                  │  ← REST API endpoints, serializers
├─────────────────────────────────────────┤
│           Application Layer             │  ← Business logic, services
├─────────────────────────────────────────┤
│           Infrastructure Layer          │  ← AWS, external services, storage
├─────────────────────────────────────────┤
│              Core Layer                 │  ← Shared utilities, exceptions
└─────────────────────────────────────────┘
```

### Directory Structure

```
chatbook-backend/
├── config/                          # Configuration Management
│   ├── settings/                    # Environment-specific settings
│   │   ├── base.py                  # Base Django settings
│   │   ├── development.py           # Development settings
│   │   ├── production.py            # Production settings
│   │   └── testing.py               # Test settings
│   ├── aws.py                       # AWS configuration
│   ├── database.py                  # Database configuration
│   └── logging.py                   # Logging configuration
│
├── core/                            # Core/Shared Functionality
│   ├── exceptions.py                # Global exceptions
│   ├── middleware/                  # Custom middleware
│   ├── permissions.py               # Global permissions
│   └── utils/                       # Shared utilities
│
├── aws_services/                    # AWS Services Layer
│   ├── s3.py                        # S3 operations
│   ├── sqs.py                       # SQS operations
│   └── base.py                      # Common AWS functionality
│
├── apps/                            # Django Applications (Business Logic)
│   ├── accounts/                    # User management
│   ├── appointments/                # Appointment management
│   ├── business/                    # Business configuration
│   ├── customers/                   # Customer management
│   ├── employees/                   # Employee management
│   ├── services/                    # Business services (hair, nails, etc.)
│   └── files/                       # File management
│       ├── models.py                # File-related models
│       ├── views.py                 # File upload/download views
│       ├── serializers.py           # File serializers
│       ├── services/                # File business logic
│       │   ├── upload.py            # File upload service
│       │   ├── processing.py        # File processing service
│       │   └── import_service.py    # Data import service
│       └── tasks.py                 # Async tasks (Celery)
│
├── api/                             # API Layer
│   ├── v1/                          # API versioning
│   │   ├── accounts/                # Account endpoints
│   │   ├── appointments/            # Appointment endpoints
│   │   ├── files/                   # File endpoints
│   │   └── ...
│   └── middleware/                  # API-specific middleware
│
└── tests/                           # Test Organization
    ├── unit/                        # Unit tests
    ├── integration/                 # Integration tests
    └── e2e/                         # End-to-end tests
```

## Layer Responsibilities

### 1. Configuration Layer (`config/`)

**Purpose**: Centralize all configuration management

**Responsibilities**:
- Environment-specific settings
- AWS configuration
- Database configuration
- Logging configuration
- Feature flags

**Benefits**:
- Single source of truth for configuration
- Environment-specific overrides
- Easy to test different configurations

### 2. Core Layer (`core/`)

**Purpose**: Shared functionality across the application

**Responsibilities**:
- Global exceptions
- Custom middleware
- Shared permissions
- Common utilities
- Base classes

**Benefits**:
- Avoid code duplication
- Consistent error handling
- Shared business rules

### 3. AWS Services Layer (`aws_services/`)

**Purpose**: Handle AWS service integrations

**Responsibilities**:
- AWS S3 operations
- AWS SQS operations
- AWS credential management
- AWS error handling

**Benefits**:
- Separation of concerns
- Easy to mock for testing
- Centralized AWS logic
- Clear external dependencies

### 4. Application Layer (`apps/`)

**Purpose**: Business logic and domain models

**Responsibilities**:
- Django models
- Business services
- Domain logic
- Data validation
- Business rules

**Benefits**:
- Clear business boundaries
- Domain-driven design
- Testable business logic

### 5. API Layer (`api/`)

**Purpose**: HTTP interface and API versioning

**Responsibilities**:
- REST API endpoints
- Request/response serialization
- API versioning
- Authentication/authorization
- Rate limiting

**Benefits**:
- Clean API design
- Version management
- Clear HTTP interface

## Migration Strategy

### Phase 1: Create New Structure (Non-Breaking)
1. Create new directory structure
2. Move AWS configuration to `config/aws.py`
3. Create infrastructure services
4. Update imports gradually

### Phase 2: Reorganize Services (Breaking)
1. Move `api/services/` to appropriate layers
2. Create `apps/files/` for file management
3. Update all import statements
4. Update tests

### Phase 3: Split Settings (Optional)
1. Split `settings.py` into environment-specific files
2. Update deployment configurations
3. Update documentation

## Benefits of This Architecture

### 1. **Separation of Concerns**
- Each layer has a single responsibility
- Easy to understand and maintain
- Clear boundaries between components

### 2. **Testability**
- Infrastructure layer can be easily mocked
- Business logic isolated from external dependencies
- Clear test boundaries

### 3. **Scalability**
- Easy to add new features
- Clear extension points
- Modular design

### 4. **Maintainability**
- Clear code organization
- Easy to find and modify code
- Consistent patterns

### 5. **Team Collaboration**
- Clear ownership boundaries
- Parallel development possible
- Consistent coding patterns

## Implementation Guidelines

### 1. **Dependency Direction**
```
API Layer → Application Layer → Infrastructure Layer
     ↓              ↓                    ↓
Core Layer ← Core Layer ← Core Layer
```

### 2. **Import Rules**
- Higher layers can import from lower layers
- Lower layers should not import from higher layers
- Use dependency injection for infrastructure services

### 3. **Configuration Management**
- All configuration in `config/` module
- Environment variables for deployment-specific values
- No hardcoded values in business logic

### 4. **Error Handling**
- Infrastructure errors handled at infrastructure layer
- Business errors handled at application layer
- HTTP errors handled at API layer

### 5. **Testing Strategy**
- Unit tests for business logic
- Integration tests for infrastructure
- API tests for endpoints
- Mock external dependencies

## Next Steps

1. **Review and Approve**: Team review of proposed structure
2. **Create Migration Plan**: Detailed steps for migration
3. **Implement Gradually**: Phase-by-phase implementation
4. **Update Documentation**: Keep documentation current
5. **Train Team**: Ensure team understands new structure

## Industry Examples

This architecture follows patterns used by:
- **Django Best Practices**: Two Scoops of Django
- **Clean Architecture**: Robert Martin's principles
- **Domain-Driven Design**: Eric Evans' patterns
- **Microservices Patterns**: Preparation for future scaling

## Comparison: Current vs Recommended

### Current Structure Issues:
```
api/services/           # ❌ Confusing - not API-specific
├── aws_queue_service.py    # ❌ Infrastructure mixed with API
├── file_processing_service.py  # ❌ Business logic in wrong place
├── file_reader_service.py      # ❌ Infrastructure concern
└── import_service.py           # ❌ Business logic in API layer

services/               # ❌ Name collision with api/services/
├── models.py          # ✅ Correct - business models
└── ...

settings.py            # ❌ AWS config mixed with Django settings
```

### Recommended Structure:
```
aws_services/          # ✅ Clear AWS services layer
├── s3.py              # ✅ S3 operations isolated
└── sqs.py             # ✅ SQS operations isolated

apps/files/services/    # ✅ Business logic in application layer
├── upload.py          # ✅ File upload business logic
├── processing.py      # ✅ File processing business logic
└── import_service.py  # ✅ Import business logic

apps/services/         # ✅ Clear business services (hair, nails, etc.)
├── models.py          # ✅ Business service models
└── ...

config/aws.py          # ✅ AWS config separated and centralized
```

## Key Principles Applied

### 1. **Single Responsibility Principle**
- Each module has one reason to change
- Clear separation of concerns
- Focused functionality

### 2. **Dependency Inversion Principle**
- High-level modules don't depend on low-level modules
- Both depend on abstractions
- Easy to test and mock

### 3. **Open/Closed Principle**
- Open for extension, closed for modification
- New features don't require changing existing code
- Plugin architecture for external services

### 4. **Interface Segregation Principle**
- Clients don't depend on interfaces they don't use
- Small, focused interfaces
- Clear contracts between layers
