"""
SQS Service

Handles all SQS operations with proper error handling and logging.
"""
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
from botocore.exceptions import ClientError

from config.aws import aws_config

logger = logging.getLogger(__name__)


class SQSService:
    """Service for SQS operations."""
    
    def __init__(self):
        self.client = aws_config.sqs_client
        self.file_processing_queue_url = aws_config.sqs_file_processing_queue_url
    
    def send_message(self, queue_url: str, message_body: dict, delay_seconds: int = 0) -> str:
        """
        Send a message to an SQS queue.
        
        Args:
            queue_url: URL of the SQS queue
            message_body: Message content as dictionary
            delay_seconds: Delay before message becomes available
            
        Returns:
            Message ID
            
        Raises:
            ClientError: If sending fails
        """
        try:
            logger.info(f"Sending message to SQS queue: {queue_url}")
            
            response = self.client.send_message(
                QueueUrl=queue_url,
                MessageBody=json.dumps(message_body),
                DelaySeconds=delay_seconds
            )
            
            message_id = response['MessageId']
            logger.info(f"Message sent successfully: {message_id}")
            
            return message_id
            
        except ClientError as e:
            logger.error(f"Failed to send message to SQS: {e}")
            raise
    
    def send_file_processing_message(self, file_id: str, s3_key: str, user_email: str,
                                   uploaded_by: str = None, processing_options: dict = None,
                                   metadata: dict = None, tenant_id: str = None) -> dict:
        """
        Send a file processing message to the processing queue.
        
        Args:
            file_id: Unique file identifier
            s3_key: S3 key of the file
            user_email: Email address for processing notifications
            uploaded_by: User ID who uploaded the file
            processing_options: Additional processing options
            metadata: Additional file metadata
            tenant_id: Optional tenant identifier
            
        Returns:
            Dictionary with queue response information
        """
        message_body = {
            'file_id': file_id,
            's3_key': s3_key,
            'user_email': user_email,
            'uploaded_by': uploaded_by,
            'processing_options': processing_options or {},
            'metadata': metadata or {},
            'timestamp': datetime.now().isoformat()
        }
        
        if tenant_id:
            message_body['tenant_id'] = tenant_id
        
        message_id = self.send_message(self.file_processing_queue_url, message_body)
        
        return {
            'status': 'queued',
            'message_id': message_id,
            'queue_url': self.file_processing_queue_url
        }
    
    def is_available(self) -> bool:
        """Check if SQS service is available and configured"""
        try:
            return bool(self.file_processing_queue_url and self.client)
        except Exception:
            return False
    
    def receive_messages(self, queue_url: str, max_messages: int = 1, 
                        wait_time_seconds: int = 0) -> List[Dict]:
        """
        Receive messages from an SQS queue.
        
        Args:
            queue_url: URL of the SQS queue
            max_messages: Maximum number of messages to receive
            wait_time_seconds: Long polling wait time
            
        Returns:
            List of message dictionaries
            
        Raises:
            ClientError: If receiving fails
        """
        try:
            logger.info(f"Receiving messages from SQS queue: {queue_url}")
            
            response = self.client.receive_message(
                QueueUrl=queue_url,
                MaxNumberOfMessages=max_messages,
                WaitTimeSeconds=wait_time_seconds,
                MessageAttributeNames=['All']
            )
            
            messages = response.get('Messages', [])
            logger.info(f"Received {len(messages)} messages")
            
            return messages
            
        except ClientError as e:
            logger.error(f"Failed to receive messages from SQS: {e}")
            raise
    
    def delete_message(self, queue_url: str, receipt_handle: str) -> bool:
        """
        Delete a message from an SQS queue.
        
        Args:
            queue_url: URL of the SQS queue
            receipt_handle: Receipt handle of the message to delete
            
        Returns:
            True if successful
            
        Raises:
            ClientError: If deletion fails
        """
        try:
            logger.info(f"Deleting message from SQS queue: {queue_url}")
            
            self.client.delete_message(
                QueueUrl=queue_url,
                ReceiptHandle=receipt_handle
            )
            
            logger.info("Message deleted successfully")
            return True
            
        except ClientError as e:
            logger.error(f"Failed to delete message from SQS: {e}")
            raise


# Global SQS service instance
sqs_service = SQSService()
