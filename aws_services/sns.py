"""
SNS Service

Handles all SNS operations for notifications.
Based on DJANGO_AWS_INTEGRATION.md recommendations.
"""
import json
import logging
from datetime import datetime
from typing import Dict, Optional
from botocore.exceptions import ClientError

from config.aws import aws_config

logger = logging.getLogger(__name__)


class SNSService:
    """Service for SNS notification operations."""
    
    def __init__(self):
        self.client = aws_config.sns_client
        self.topic_arn = aws_config.sns_topic_arn
    
    def send_notification(self, message: str, subject: str, 
                         message_attributes: Dict = None) -> Optional[str]:
        """
        Send a notification to the configured SNS topic.
        
        Args:
            message: Message content
            subject: Message subject
            message_attributes: Additional message attributes
            
        Returns:
            Message ID if successful, None otherwise
            
        Raises:
            ClientError: If sending fails
        """
        if not self.topic_arn:
            logger.warning("SNS topic ARN not configured, skipping notification")
            return None
        
        try:
            logger.info(f"Sending SNS notification: {subject}")
            
            response = self.client.publish(
                TopicArn=self.topic_arn,
                Message=message,
                Subject=subject,
                MessageAttributes=message_attributes or {}
            )
            
            message_id = response['MessageId']
            logger.info(f"SNS notification sent successfully: {message_id}")
            
            return message_id
            
        except ClientError as e:
            logger.error(f"Failed to send SNS notification: {e}")
            raise
    
    def send_failure_notification(self, file_id: str, error_message: str, 
                                 user_email: str = None) -> Optional[str]:
        """
        Send a file processing failure notification.
        Based on DJANGO_AWS_INTEGRATION.md implementation.
        
        Args:
            file_id: File ID that failed processing
            error_message: Error message
            user_email: Optional user email for context
            
        Returns:
            Message ID if successful
        """
        message = {
            'file_id': file_id,
            'error': error_message,
            'timestamp': datetime.now().isoformat(),
            'severity': 'ERROR'
        }
        
        if user_email:
            message['user_email'] = user_email
        
        message_attributes = {
            'file_id': {
                'DataType': 'String',
                'StringValue': str(file_id)
            },
            'severity': {
                'DataType': 'String',
                'StringValue': 'ERROR'
            }
        }
        
        return self.send_notification(
            message=json.dumps(message),
            subject=f'File Processing Failed - {file_id}',
            message_attributes=message_attributes
        )
    
    def send_success_notification(self, file_id: str, processed_count: int,
                                 user_email: str = None) -> Optional[str]:
        """
        Send a file processing success notification.
        
        Args:
            file_id: File ID that was processed successfully
            processed_count: Number of records processed
            user_email: Optional user email for context
            
        Returns:
            Message ID if successful
        """
        message = {
            'file_id': file_id,
            'processed_count': processed_count,
            'timestamp': datetime.now().isoformat(),
            'severity': 'INFO'
        }
        
        if user_email:
            message['user_email'] = user_email
        
        message_attributes = {
            'file_id': {
                'DataType': 'String',
                'StringValue': str(file_id)
            },
            'severity': {
                'DataType': 'String',
                'StringValue': 'INFO'
            }
        }
        
        return self.send_notification(
            message=json.dumps(message),
            subject=f'File Processing Completed - {file_id}',
            message_attributes=message_attributes
        )


# Global SNS service instance
sns_service = SNSService() 