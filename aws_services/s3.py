"""
S3 Service

Handles all S3 operations with proper error handling and logging.
"""
import logging
import tempfile
import os
import urllib.parse
from typing import Optional, BinaryIO, List
from botocore.exceptions import ClientError, NoCredentialsError

from config.aws import aws_config
from core.exceptions import S3ServiceError, ConfigurationError

logger = logging.getLogger(__name__)


class S3Service:
    """Service for S3 operations."""

    def __init__(self):
        # Validate configuration before creating clients
        if not aws_config.s3_bucket_name:
            raise ConfigurationError(
                "AWS S3 bucket name not configured. Please set AWS_STORAGE_BUCKET_NAME in your environment variables."
            )

        try:
            self.client = aws_config.s3_client
            self.bucket_name = aws_config.s3_bucket_name
        except NoCredentialsError:
            raise ConfigurationError(
                "AWS credentials not found. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in your .env file, "
                "or configure AWS CLI with 'aws configure'."
            )
        except Exception as e:
            raise ConfigurationError(f"Failed to initialize AWS S3 client: {str(e)}")
    
    def upload_file(self, file_obj: BinaryIO, key: str, content_type: str = None, metadata: dict = None, tags: dict = None) -> str:
        """
        Upload a file to S3.
        
        Args:
            file_obj: File object to upload
            key: S3 key (path) for the file
            content_type: MIME type of the file
            metadata: Additional metadata to store with the file
            tags: Additional tags to apply to the file
            
        Returns:
            S3 URL of the uploaded file
            
        Raises:
            ClientError: If upload fails
        """
        try:
            extra_args = {}
            
            if content_type:
                extra_args['ContentType'] = content_type
            
            if metadata:
                extra_args['Metadata'] = metadata
            
            # ------------------------------------------------------------------
            #  S3 Object Tags – bucket policy requires certain keys to be present
            # ------------------------------------------------------------------
            # Convert incoming tags dict → query-string format expected by boto3
            # If caller did not provide tags, apply safe defaults so that uploads
            # never fail due to the RequireObjectTags policy enforced by CDK.
            #
            # Tag requirements (from S3StorageStack):
            #   env     – dev / staging / prod / sandbox
            #   tenant  – business / organisation id (or global)
            #   form    – form identifier
            #   status  – blank / completed / etc.
            #
            default_tags = {
                'env': os.getenv('ENV_NAME', 'dev'),
                'tenant': 'global',
                'form': 'generic',
                'status': 'uploaded',
            }

            if tags:
                default_tags.update(tags)

            # boto3 expects Tagging string like "key1=value1&key2=value2"
            extra_args['Tagging'] = urllib.parse.urlencode(default_tags)
            
            logger.info(f"Uploading file to S3: {key}")
            
            file_obj.seek(0)  # Reset file pointer
            self.client.upload_fileobj(
                file_obj,
                self.bucket_name,
                key,
                ExtraArgs=extra_args
            )
            
            url = aws_config.get_s3_url(key)
            logger.info(f"File uploaded successfully: {url}")
            
            return url
            
        except ClientError as e:
            logger.error(f"Failed to upload file to S3: {e}")
            raise
    
    def download_file(self, key: str) -> str:
        """
        Download a file from S3 to a temporary local file.
        
        Args:
            key: S3 key of the file to download
            
        Returns:
            Path to the temporary local file
            
        Raises:
            ClientError: If download fails
        """
        try:
            logger.info(f"Downloading file from S3: {key}")
            
            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False)
            temp_path = temp_file.name
            temp_file.close()
            
            # Download file
            self.client.download_file(self.bucket_name, key, temp_path)
            
            logger.info(f"File downloaded successfully: {temp_path}")
            return temp_path
            
        except ClientError as e:
            logger.error(f"Failed to download file from S3: {e}")
            raise
    
    def get_file_info(self, key: str) -> dict:
        """
        Get metadata about a file in S3.
        
        Args:
            key: S3 key of the file
            
        Returns:
            Dictionary with file metadata
            
        Raises:
            ClientError: If file doesn't exist or access fails
        """
        try:
            response = self.client.head_object(Bucket=self.bucket_name, Key=key)
            
            return {
                'size': response['ContentLength'],
                'last_modified': response['LastModified'],
                'content_type': response.get('ContentType'),
                'metadata': response.get('Metadata', {}),
                'etag': response['ETag'].strip('"'),
            }
            
        except ClientError as e:
            logger.error(f"Failed to get file info from S3: {e}")
            raise
    
    def delete_file(self, key: str) -> bool:
        """
        Delete a file from S3.
        
        Args:
            key: S3 key of the file to delete
            
        Returns:
            True if successful
            
        Raises:
            ClientError: If deletion fails
        """
        try:
            logger.info(f"Deleting file from S3: {key}")
            
            self.client.delete_object(Bucket=self.bucket_name, Key=key)
            
            logger.info(f"File deleted successfully: {key}")
            return True
            
        except ClientError as e:
            logger.error(f"Failed to delete file from S3: {e}")
            raise
    
    def file_exists(self, key: str) -> bool:
        """
        Check if a file exists in S3.
        
        Args:
            key: S3 key of the file
            
        Returns:
            True if file exists, False otherwise
        """
        try:
            self.client.head_object(Bucket=self.bucket_name, Key=key)
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            raise
    
    def generate_presigned_url(self, key: str, expiration: int = 3600, method: str = 'get_object') -> str:
        """
        Generate a presigned URL for S3 object access.
        
        Args:
            key: S3 key of the file
            expiration: URL expiration time in seconds
            method: HTTP method ('get_object' or 'put_object')
            
        Returns:
            Presigned URL
        """
        try:
            url = self.client.generate_presigned_url(
                method,
                Params={'Bucket': self.bucket_name, 'Key': key},
                ExpiresIn=expiration
            )
            return url
        except ClientError as e:
            logger.error(f"Failed to generate presigned URL: {e}")
            raise

    def create_folder(self, key: str, tags: dict = None, metadata: dict = None) -> bool:
        """
        Create an empty "folder" **placeholder object** in S3.

        This uploads a zero-byte object whose key ends with a trailing slash so
        that tools such as the S3 console treat it as a folder. Although S3
        does not require folders to exist before uploading objects, creating
        them explicitly makes browsing the bucket structure easier for humans
        and allows attaching metadata/tags to the logical folder.

        Args:
            key: Full S3 key for the folder. It may or may not end with '/'; the
                 method will ensure a single trailing slash is present.
            tags: Optional dict of object tags. If omitted, safe defaults that
                  comply with the bucket's RequireObjectTags policy are used.
            metadata: Optional user metadata dict.

        Returns:
            True if the placeholder object was created successfully.
        """
        try:
            # Ensure exactly one trailing slash so that the key represents a folder
            key = key.rstrip('/') + '/'

            # ------------------------------------------------------------------
            #  Bucket-level policy requires certain tags be present on *every*
            #  object, including these folder placeholders.
            # ------------------------------------------------------------------
            default_tags = {
                'env': os.getenv('ENV_NAME', 'dev'),
                'tenant': 'global',
                'form': 'generic',
                'status': 'created',
            }
            if tags:
                default_tags.update(tags)

            params = {
                'Bucket': self.bucket_name,
                'Key': key,
                'ContentType': 'application/x-directory',  # Hint for consoles
                'Tagging': urllib.parse.urlencode(default_tags),
            }
            if metadata:
                params['Metadata'] = metadata

            self.client.put_object(**params)
            logger.info(f"Folder created in S3: {key}")
            return True
        except ClientError as e:
            logger.error(f"Failed to create folder {key} in S3: {e}")
            raise

    def create_customer_folder(self, tenant_id: str, customer_uuid: str) -> bool:
        """
        Convenience wrapper to create the root folder for a customer under a
        tenant prefix.

        The resulting key looks like::

            tenant_<tenantId>/customer_<customerUUID>/

        Args:
            tenant_id: Numeric/UUID identifier of the tenant/business.
            customer_uuid: UUID of the customer.

        Returns:
            True if the folder was created successfully.
        """
        tenant_prefix = f"tenant_{tenant_id}"
        folder_key = f"{tenant_prefix}/customer_{customer_uuid}/"
        tags = {
            'tenant': str(tenant_id),
            'form': 'customer',
            'status': 'created',
        }
        return self.create_folder(folder_key, tags=tags)


# Global S3 service instance
s3_service = S3Service()
