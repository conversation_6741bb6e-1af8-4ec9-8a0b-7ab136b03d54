# Timezone Management Rules

## Overview

This document outlines the timezone handling strategy for the chatbook-backend project. Our approach ensures consistent, reliable datetime management across all layers of the application while avoiding common timezone pitfalls.

## Core Principles

1. **Single Source of Truth**: All absolute timestamps are stored in UTC
2. **Boundary Conversion**: Timezone conversion happens only at UI boundaries
3. **Separation of Concerns**: Appointment times (absolute moments) vs Working hours (recurring patterns)
4. **Business Logic in UTC**: All server-side calculations use UTC

## Architecture Overview

| Layer              | Storage Format                | Conversion Point               |
| ------------------ | ----------------------------- | ------------------------------ |
| **Database**       | UTC (`DateTimeField`, aware)  | None                           |
| **API Exchange**   | ISO 8601 UTC strings          | None                           |
| **Client Display** | `Date` → local components     | On rendering & input parsing   |
| **Working Hours**  | Local `TimeField` + IANA zone | When scheduling / availability |

## Django Configuration

### Setting Your Business Zone

In `settings.py`, configure your salon's primary locale:

```python
USE_TZ = True
TIME_ZONE = "America/Los_Angeles"  # Or your business's primary timezone
```

This setting affects:
- Django admin interface display
- Default timezone for naive datetime objects
- Server-side logging timestamps

**Important**: The admin will show and accept times in your local timezone (PDT/PST) while still storing UTC in the database.

## Data Model Strategy

### Appointment Times (Absolute Moments)

Appointment times represent specific moments in time and should be stored as timezone-aware UTC datetimes.

```python
class Appointment(models.Model):
    # Store as UTC-aware datetime
    appointment_datetime = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

**Why UTC?**
- Eliminates DST transition bugs
- Enables reliable sorting and comparison
- Supports clients in multiple timezones
- Simplifies business logic calculations

### Working Hours (Recurring Patterns)

Working hours represent recurring "clock-time" windows with no intrinsic date.

```python
class BusinessHours(models.Model):
    day_of_week = models.IntegerField(choices=WEEKDAY_CHOICES)
    # Store as local time components
    open_time = models.TimeField()
    close_time = models.TimeField()
    # Store business timezone separately
    timezone = models.CharField(max_length=50, default="America/Los_Angeles")
```

**Why Local Time + Timezone?**
- Working hours are conceptual (9 AM - 5 PM)
- No specific date attached
- Timezone conversion happens during availability calculations

## API Design Guidelines

### Request/Response Format

**Incoming Requests**: Accept ISO 8601 format with timezone offset
```json
{
    "appointment_datetime": "2025-06-11T17:00:00+00:00"
}
```

**Outgoing Responses**: Return ISO 8601 format with timezone offset
```json
{
    "appointment_datetime": "2025-06-11T17:00:00+00:00",
    "created_at": "2025-06-11T10:30:00+00:00"
}
```

**IMPORTANT**: All API responses containing datetime values MUST use the format `"2025-06-11T17:00:00+00:00"` (ISO 8601 with timezone offset), not the shorter `"Z"` format. This includes:
- Appointment times
- Available time slots
- Created/updated timestamps
- Any other datetime fields

### Client Responsibility

The client (iOS app, web frontend) is responsible for:
- Converting UTC to local time for display
- Converting user input to UTC before sending to API
- Handling timezone-specific formatting

## Implementation Examples

### Creating Appointments

```python
from django.utils import timezone
from datetime import datetime

# Client sends: "2024-03-15T17:00:00Z"
# Django automatically parses to timezone-aware datetime
appointment = Appointment.objects.create(
    appointment_datetime=request.data['appointment_datetime']
)
```

### Availability Calculations

```python
from zoneinfo import ZoneInfo
from datetime import datetime, time

def get_available_slots(business, date):
    # Get business hours for the day
    business_hours = business.hours.filter(day_of_week=date.weekday()).first()
    
    # Convert business hours to UTC for the specific date
    business_tz = ZoneInfo(business_hours.timezone)
    
    # Create datetime objects for the specific date
    open_datetime = datetime.combine(date, business_hours.open_time)
    close_datetime = datetime.combine(date, business_hours.close_time)
    
    # Localize to business timezone, then convert to UTC
    open_utc = open_datetime.replace(tzinfo=business_tz).astimezone(timezone.utc)
    close_utc = close_datetime.replace(tzinfo=business_tz).astimezone(timezone.utc)
    
    # Now work with UTC times for availability logic
    return calculate_slots(open_utc, close_utc)
```

## Common Pitfalls & Best Practices

### ❌ Don't Do This

1. **Never mix local and UTC in one field**
   ```python
   # BAD: Sometimes UTC, sometimes local
   appointment_time = models.DateTimeField()  # Ambiguous!
   ```

2. **Don't store timezone-naive datetimes for appointments**
   ```python
   # BAD: No timezone information
   appointment_time = datetime(2024, 3, 15, 17, 0)
   ```

3. **Don't perform timezone conversion in the database**
   ```python
   # BAD: Database-level timezone conversion
   appointments = Appointment.objects.extra(
       select={'local_time': "appointment_datetime AT TIME ZONE 'PST'"}
   )
   ```

### ✅ Best Practices

1. **Use reliable timezone data**
   ```python
   # Python 3.9+
   from zoneinfo import ZoneInfo
   
   # For older Python versions
   import pytz
   ```

2. **Keep business logic in UTC**
   ```python
   # All comparisons and calculations in UTC
   now_utc = timezone.now()
   upcoming = Appointment.objects.filter(appointment_datetime__gt=now_utc)
   ```

3. **Delegate localization to the UI**
   ```javascript
   // Client-side conversion
   const localTime = new Date(utcString).toLocaleString();
   ```

4. **Validate timezone identifiers**
   ```python
   def validate_timezone(tz_string):
       try:
           ZoneInfo(tz_string)
           return True
       except ZoneInfoNotFoundError:
           return False
   ```

## Testing Considerations

### Test Across DST Transitions

```python
def test_dst_transition():
    # Test appointment scheduling across DST boundary
    spring_forward = datetime(2024, 3, 10, 7, 0, tzinfo=timezone.utc)  # 2 AM PST becomes 3 AM PDT
    appointment = Appointment.objects.create(appointment_datetime=spring_forward)
    
    # Verify UTC storage is unaffected
    assert appointment.appointment_datetime.tzinfo == timezone.utc
```

### Mock Timezone for Testing

```python
from django.test import override_settings

@override_settings(TIME_ZONE='America/New_York')
def test_different_timezone():
    # Test behavior with different server timezone
    pass
```

## Migration Strategy

If migrating from timezone-naive to timezone-aware:

1. **Audit existing data** for timezone assumptions
2. **Add timezone fields** to models that need them
3. **Convert existing datetimes** to UTC with explicit timezone assumption
4. **Update API contracts** to use ISO 8601 format
5. **Test thoroughly** across DST transitions

## Monitoring & Debugging

### Logging Timezone Information

```python
import logging

logger = logging.getLogger(__name__)

def create_appointment(appointment_data):
    logger.info(f"Creating appointment at {appointment_data['appointment_datetime']} UTC")
    # ... creation logic
```

### Common Debug Queries

```python
# Check for timezone-naive datetimes (should return empty)
naive_appointments = Appointment.objects.filter(appointment_datetime__isnull=False)
for apt in naive_appointments:
    if apt.appointment_datetime.tzinfo is None:
        print(f"Found naive datetime: {apt.id}")
```

## Current Implementation Status

### ✅ Fully Compliant - BULLETPROOF! 🎯
- **Django timezone configuration** (`USE_TZ=True`, `TIME_ZONE="America/Los_Angeles"`)
- **Database storage** (all DateTimeFields store UTC)
- **Business timezone configuration** via OnlineBookingRules with validation
- **API documentation** and ISO 8601 UTC responses
- **Client responsibility** for timezone conversion
- **Modern zoneinfo library** (upgraded from pytz)
- **Explicit timezone fields** in EmployeeWorkingHours
- **Comprehensive timezone utilities** (`utils/timezone_utils.py`)
- **Robust validation framework** (`utils/timezone_validators.py`)
- **Simplified availability calculation** with DST handling
- **Comprehensive test coverage** including edge cases

### 🚀 Recent Improvements (2024)
1. **✅ Upgraded to zoneinfo**: Modern Python 3.9+ timezone handling
2. **✅ Added explicit timezone fields**: EmployeeWorkingHours now has optional timezone field
3. **✅ Streamlined availability logic**: New `calculate_employee_availability()` function
4. **✅ Added timezone validation**: Comprehensive validation for all timezone fields
5. **✅ Created utility functions**: Centralized timezone operations with error handling
6. **✅ Enhanced error handling**: Graceful fallbacks and detailed logging
7. **✅ Added comprehensive tests**: Including DST transitions and edge cases

### 📚 Documentation
- **TIMEZONE_RULE.md**: This comprehensive guide
- **TIMEZONE_MIGRATION_GUIDE.md**: Detailed migration documentation
- **API_DOCUMENTATION.md**: Updated with timezone handling details

## Summary

By following these rules, we ensure:
- **Consistency**: All absolute times stored in UTC
- **Reliability**: No DST-related bugs
- **Scalability**: Support for multi-timezone clients
- **Maintainability**: Clear separation of concerns
- **Performance**: Efficient database queries and comparisons

Remember: **Store in UTC, convert at the boundary, keep business logic timezone-agnostic.**
