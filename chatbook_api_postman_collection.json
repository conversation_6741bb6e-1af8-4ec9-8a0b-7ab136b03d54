{"info": {"_postman_id": "a8f7b2e5-6c8d-4e3c-a3e1-9d2f5b3f7c25", "name": "Chatbook API", "description": "Collection for testing the Chatbook backend API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);", "", "// Set the access token if available", "if (jsonData.access) {", "    pm.environment.set(\"token\", jsonData.access);", "    console.log(\"Access token saved to environment\");", "}", "", "// Set the refresh token if available", "if (jsonData.refresh) {", "    pm.environment.set(\"refresh_token\", jsonData.refresh);", "    console.log(\"Refresh token saved to environment\");", "}", "", "// Handle MFA case", "if (jsonData.mfa_required && jsonData.temp_token) {", "    pm.environment.set(\"temp_token\", jsonData.temp_token);", "    console.log(\"MFA required. Temporary token saved to environment\");", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{user_email}}\",\n    \"password\": \"{{user_password}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login/", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login", ""]}, "description": "Login to get authentication token"}, "response": []}, {"name": "Verify MFA", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);", "", "// Set the access token if available", "if (jsonData.access) {", "    pm.environment.set(\"token\", jsonData.access);", "    console.log(\"Access token saved to environment\");", "}", "", "// Set the refresh token if available", "if (jsonData.refresh) {", "    pm.environment.set(\"refresh_token\", jsonData.refresh);", "    console.log(\"Refresh token saved to environment\");", "}", "", "// Clear temporary token", "pm.environment.unset(\"temp_token\");"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"token\": \"{{temp_token}}\",\n    \"code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/verify-mfa/", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify-mfa", ""]}, "description": "Verify MFA code if required"}, "response": []}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);", "", "// Set the access token if available", "if (jsonData.access) {", "    pm.environment.set(\"token\", jsonData.access);", "    console.log(\"Access token refreshed successfully\");", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refresh\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh/", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh", ""]}, "description": "Refresh access token"}, "response": []}], "description": "Authentication related endpoints"}, {"name": "BFF (Backend-For-Frontend)", "item": [{"name": "App <PERSON>trap", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/app/bootstrap/?from=2023-09-01&to=2023-09-30", "host": ["{{base_url}}"], "path": ["api", "v1", "app", "bootstrap", ""], "query": [{"key": "from", "value": "2023-09-01", "description": "Start date for appointments (YYYY-MM-DD)"}, {"key": "to", "value": "2023-09-30", "description": "End date for appointments (YYYY-MM-DD)"}]}, "description": "Fetches all essential data for the app in a single call, including employee profile, working hours, permissions, calendar configuration, appointments, and viewable employees. Also returns server time, business_timezone, app version, and feature flags. The 'viewable_employees' field includes both the current user and other employees that the user has permission to view. Each employee entry contains ID, name, avatar URL, stylist level (if available), and an 'is_current_user' flag to identify the current user in the list."}, "response": []}], "description": "Backend-For-Frontend APIs that combine multiple endpoints into single responses"}, {"name": "Users", "item": [{"name": "User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/profile/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile", ""]}, "description": "Get the current user's profile"}, "response": []}, {"name": "User Settings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/settings/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings", ""]}, "description": "Get the current user's settings"}, "response": []}, {"name": "Update User Settings", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email_notifications\": true,\n    \"sms_notifications\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/settings/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings", ""]}, "description": "Update user settings"}, "response": []}], "description": "User profile and settings endpoints"}, {"name": "Employees", "item": [{"name": "Employee List", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/employees/", "host": ["{{base_url}}"], "path": ["api", "v1", "employees", ""]}, "description": "Get list of employees. Returns a response with fields: id, full_name, stylist_level, stylist_level_display, is_active"}, "response": []}, {"name": "Employee List (By Business)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/employees/?business_id={{business_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "employees", ""], "query": [{"key": "business_id", "value": "{{business_id}}", "description": "Filter employees by business ID"}]}, "description": "Get list of employees for a specific business. Response example: { \"count\": 2, \"results\": [ { \"id\": 2, \"full_name\": \"<PERSON>\", \"stylist_level\": 2, \"stylist_level_display\": \"Senior Stylist\", \"is_active\": true }, { \"id\": 1, \"full_name\": \"<PERSON>\", \"stylist_level\": 3, \"stylist_level_display\": \"Master Stylist\", \"is_active\": true } ] }"}, "response": []}, {"name": "Employee Detail", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/employees/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "employees", "1", ""]}, "description": "Get employee details. Response example: { \"id\": 1, \"full_name\": \"<PERSON>\", \"stylist_level\": 3, \"stylist_level_display\": \"Master Stylist\", \"is_active\": true, \"role\": \"admin\" }"}, "response": []}, {"name": "My Employee Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/employees/me/", "host": ["{{base_url}}"], "path": ["api", "v1", "employees", "me", ""]}, "description": "Get the currently logged-in employee's details. Response includes fields: id, full_name, stylist_level, stylist_level_display, is_active, role"}, "response": []}, {"name": "My Working Hours", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/employees/me/working-hours/", "host": ["{{base_url}}"], "path": ["api", "v1", "employees", "me", "working-hours", ""]}, "description": "Get the currently logged-in employee's working hours"}, "response": []}, {"name": "My Permissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/employees/me/permissions/", "host": ["{{base_url}}"], "path": ["api", "v1", "employees", "me", "permissions", ""]}, "description": "Get the currently logged-in employee's permissions based on their access level. Example response: {\n    \"permissions\": {\n        \"view_appointments\": true,\n        \"edit_appointments\": true,\n        \"manage_services\": false,\n        \"manage_employees\": false\n    }\n}"}, "response": []}, {"name": "My Calendar Configuration", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/employees/me/calendar-configs/", "host": ["{{base_url}}"], "path": ["api", "v1", "employees", "me", "calendar-configs", ""]}, "description": "Get the currently logged-in employee's calendar configuration. Example response: {\n    \"id\": 1,\n    \"week_start_day\": \"Monday\",\n    \"default_view\": \"Week\",\n    \"calendar_resolution\": 15,\n    \"created_at\": \"2023-05-17T12:30:45Z\",\n    \"updated_at\": \"2023-05-17T12:30:45Z\"\n}"}, "response": []}, {"name": "Update My Calendar Configuration", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"week_start_day\": \"Monday\",\n    \"default_view\": \"Week\",\n    \"calendar_resolution\": 15\n}"}, "url": {"raw": "{{base_url}}/api/v1/employees/me/calendar-configs/", "host": ["{{base_url}}"], "path": ["api", "v1", "employees", "me", "calendar-configs", ""]}, "description": "Update the currently logged-in employee's calendar configuration. You can provide any of the fields to update only those settings."}, "response": []}, {"name": "My Appointments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/employees/me/appointments/?from=2023-09-01&to=2023-09-30", "host": ["{{base_url}}"], "path": ["api", "v1", "employees", "me", "appointments", ""], "query": [{"key": "from", "value": "2023-09-01", "description": "Start date in YYYY-MM-DD format"}, {"key": "to", "value": "2023-09-30", "description": "End date in YYYY-MM-DD format"}]}, "description": "Get appointments for the currently logged-in employee within a date range. Returns non-canceled appointments with client name, service type, duration, and status information. Defaults to showing 7 days from today if no date range provided."}, "response": []}], "description": "Employee-related endpoints"}, {"name": "Services", "item": [{"name": "Service Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/service-categories/", "host": ["{{base_url}}"], "path": ["api", "v1", "service-categories", ""]}, "description": "Get list of service categories"}, "response": []}, {"name": "Services List", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/services/", "host": ["{{base_url}}"], "path": ["api", "v1", "services", ""]}, "description": "Get list of services"}, "response": []}, {"name": "Service Detail", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/services/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "services", "1", ""]}, "description": "Get service details"}, "response": []}, {"name": "Service Employees", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/services/1/employees/", "host": ["{{base_url}}"], "path": ["api", "v1", "services", "1", "employees", ""]}, "description": "Get employees offering this service"}, "response": []}, {"name": "Create Service", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"business\": 1,\n    \"category\": 1,\n    \"name\": \"New Service\",\n    \"description\": \"Service description\",\n    \"price\": \"99.99\",\n    \"duration\": 60,\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/services/", "host": ["{{base_url}}"], "path": ["api", "v1", "services", ""]}, "description": "Create a new service"}, "response": []}, {"name": "Update Service", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"business\": 1,\n    \"category\": 1,\n    \"name\": \"Updated Service\",\n    \"description\": \"Updated description\",\n    \"price\": \"129.99\",\n    \"duration\": 90,\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/services/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "services", "1", ""]}, "description": "Update a service"}, "response": []}, {"name": "Delete Service", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/services/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "services", "1", ""]}, "description": "Delete a service"}, "response": []}], "description": "Service-related endpoints"}, {"name": "Appointments", "item": [{"name": "Available Time Slots", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/businesses/{{business_id}}/appointments/available-times/?date=2025-06-29&service_id=9", "host": ["{{base_url}}"], "path": ["api", "v1", "businesses", "{{business_id}}", "appointments", "available-times", ""], "query": [{"key": "date", "value": "2025-06-29", "description": "Date in YYYY-MM-DD format"}, {"key": "service_id", "value": "9", "description": "ID of the requested service"}, {"key": "employee_id", "value": "", "description": "Optional: ID of a specific employee", "disabled": true}]}, "description": "Get available time slots for a specific business, date and service with automatic smart booking rules"}, "response": []}, {"name": "Available Time Slots (Specific Employee)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/businesses/{{business_id}}/appointments/available-times/?date=2025-06-29&service_id=9&employee_id=1", "host": ["{{base_url}}"], "path": ["api", "v1", "businesses", "{{business_id}}", "appointments", "available-times", ""], "query": [{"key": "date", "value": "2025-06-29", "description": "Date in YYYY-MM-DD format"}, {"key": "service_id", "value": "9", "description": "ID of the requested service"}, {"key": "employee_id", "value": "1", "description": "ID of specific employee"}]}, "description": "Get available time slots for a specific employee with automatic smart booking rules"}, "response": []}, {"name": "List Appointments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/appointments/", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", ""]}, "description": "Get list of appointments (requires authentication). Response includes basic appointment details including customer ID, employee ID, start_time, status, etc."}, "response": []}, {"name": "Get Appointment", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/appointments/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", "1", ""]}, "description": "Get details of a specific appointment. Uses a detailed serializer that expands customer_details, employee_details, and appointment_services. All PhoneNumber fields and other complex types are safely converted to strings."}, "response": []}, {"name": "Create Appointment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer\": 1,\n    \"employee\": 1,\n    \"start_time\": \"2023-09-15T10:30:00Z\",\n    \"status\": \"pending\",\n    \"payment_status\": \"unpaid\",\n    \"source\": \"online\",\n    \"notes_from_customer\": \"Please use the organic products\",\n    \"appointment_services\": [\n        {\n            \"service\": 1,\n            \"quantity\": 1,\n            \"buffer_time\": 15,\n            \"notes\": \"Use the special shampoo\"\n        }\n    ],\n    \"appointment_add_ons\": [\n        {\n            \"add_on\": 2,\n            \"add_on_price\": 25.00,\n            \"duration\": 15\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/appointments/", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", ""]}, "description": "Create a new appointment with nested services and add-ons in a single API call. Now uses customer field which refers to a BusinessCustomer."}, "response": []}, {"name": "Update Appointment", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer\": 1,\n    \"employee\": 1,\n    \"start_time\": \"2023-09-15T10:30:00Z\",\n    \"status\": \"confirmed\",\n    \"payment_status\": \"unpaid\",\n    \"notes_from_customer\": \"Updated notes\",\n    \"appointment_services\": [\n        {\n            \"service\": 1,\n            \"quantity\": 1,\n            \"buffer_time\": 20,\n            \"notes\": \"Updated service notes\"\n        },\n        {\n            \"service\": 2,\n            \"quantity\": 1,\n            \"buffer_time\": 10,\n            \"notes\": \"Added a second service\"\n        }\n    ],\n    \"appointment_add_ons\": [\n        {\n            \"add_on\": 3,\n            \"add_on_price\": 35.00,\n            \"duration\": 20\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/appointments/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", "1", ""]}, "description": "Update an existing appointment including its services and add-ons. Now uses customer field which refers to a BusinessCustomer."}, "response": []}, {"name": "Partial Update Appointment", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"confirmed\",\n    \"appointment_services\": [\n        {\n            \"service\": 1,\n            \"quantity\": 1,\n            \"buffer_time\": 25,\n            \"notes\": \"Only updating services\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/appointments/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", "1", ""]}, "description": "Partially update an appointment (e.g., change status) and/or its related services/add-ons."}, "response": []}, {"name": "<PERSON>cel Appointment", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/appointments/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", "1", ""]}, "description": "Cancel (delete) an appointment"}, "response": []}], "description": "Appointment-related endpoints including booking and availability"}, {"name": "Employee Services", "item": [{"name": "List Employee Services", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/employee-services/", "host": ["{{base_url}}"], "path": ["api", "v1", "employee-services", ""]}, "description": "Get all employee services"}, "response": []}, {"name": "Filter by Employee", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/employee-services/?employee_id=1", "host": ["{{base_url}}"], "path": ["api", "v1", "employee-services", ""], "query": [{"key": "employee_id", "value": "1"}]}, "description": "Get only explicitly assigned services for an employee through EmployeeService records. Use the '/employees/{id}/services/' endpoint instead to get all services offered by an employee including those through their stylist level."}, "response": []}, {"name": "Filter by Service", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/employee-services/?service_id=1", "host": ["{{base_url}}"], "path": ["api", "v1", "employee-services", ""], "query": [{"key": "service_id", "value": "1"}]}, "description": "Get all employees offering a specific service"}, "response": []}, {"name": "Filter by Business", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/employee-services/?business_id=1", "host": ["{{base_url}}"], "path": ["api", "v1", "employee-services", ""], "query": [{"key": "business_id", "value": "1"}]}, "description": "Get all employee services for a specific business"}, "response": []}, {"name": "Employee Service Detail", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/employee-services/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "employee-services", "1", ""]}, "description": "Get details of a specific employee service"}, "response": []}, {"name": "Create Employee Service", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"business\": 1,\n    \"employee\": 1,\n    \"service\": 1,\n    \"custom_price\": 85.00,\n    \"custom_duration\": 45,\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/employee-services/", "host": ["{{base_url}}"], "path": ["api", "v1", "employee-services", ""]}, "description": "Create a new employee service relationship"}, "response": []}, {"name": "Update Employee Service", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"business\": 1,\n    \"employee\": 1,\n    \"service\": 1,\n    \"custom_price\": 90.00,\n    \"custom_duration\": 50,\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/employee-services/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "employee-services", "1", ""]}, "description": "Update an employee service"}, "response": []}, {"name": "Delete Employee Service", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/employee-services/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "employee-services", "1", ""]}, "description": "Delete an employee service"}, "response": []}, {"name": "Get Employee's Offered Services", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/employees/1/services/", "host": ["{{base_url}}"], "path": ["api", "v1", "employees", "1", "services", ""]}, "description": "Get all services offered by a specific employee with correct pricing and duration logic. This combines both EmployeeService customizations and services available via StylistLevel."}, "response": []}, {"name": "Get Employee's Service Add-ons", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/employees/1/service-addons/", "host": ["{{base_url}}"], "path": ["api", "v1", "employees", "1", "service-addons", ""]}, "description": "Get all add-ons that an employee can offer, regardless of which service they are associated with. Returns add-ons from both explicit employee assignments and from the employee's stylist level."}, "response": []}, {"name": "Get Add-ons for Specific Service and Employee", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/employees/1/services/2/addons/", "host": ["{{base_url}}"], "path": ["api", "v1", "employees", "1", "services", "2", "addons", ""]}, "description": "Get all add-ons that an employee can offer for a specific service. This returns the intersection of add-ons associated with the specified service and add-ons available to the employee (either directly or through their stylist level)."}, "response": []}], "description": "Employee Service-related endpoints"}, {"name": "Stylist Level Services", "item": [{"name": "List Stylist Level Services", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/stylist-level-services/", "host": ["{{base_url}}"], "path": ["api", "v1", "stylist-level-services", ""]}, "description": "Get all stylist level services configurations"}, "response": []}, {"name": "Filter by Service", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/stylist-level-services/?service_id=1", "host": ["{{base_url}}"], "path": ["api", "v1", "stylist-level-services", ""], "query": [{"key": "service_id", "value": "1"}]}, "description": "Get all stylist level services for a specific service"}, "response": []}, {"name": "Filter by Stylist Level", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/stylist-level-services/?stylist_level=Junior", "host": ["{{base_url}}"], "path": ["api", "v1", "stylist-level-services", ""], "query": [{"key": "stylist_level", "value": "Junior"}]}, "description": "Get all services for a specific stylist level"}, "response": []}, {"name": "Stylist Level Service Detail", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/stylist-level-services/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "stylist-level-services", "1", ""]}, "description": "Get details of a specific stylist level service"}, "response": []}, {"name": "Create Stylist Level Service", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"business\": 1,\n    \"service\": 1,\n    \"stylist_level\": \"Junior\",\n    \"price\": 70.00,\n    \"duration\": 45,\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/stylist-level-services/", "host": ["{{base_url}}"], "path": ["api", "v1", "stylist-level-services", ""]}, "description": "Create a new stylist level service configuration"}, "response": []}, {"name": "Update Stylist Level Service", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"business\": 1,\n    \"service\": 1,\n    \"stylist_level\": \"Junior\",\n    \"price\": 75.00,\n    \"duration\": 45,\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/stylist-level-services/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "stylist-level-services", "1", ""]}, "description": "Update a stylist level service configuration"}, "response": []}, {"name": "Delete Stylist Level Service", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/stylist-level-services/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "stylist-level-services", "1", ""]}, "description": "Delete a stylist level service configuration"}, "response": []}], "description": "Stylist Level Service related endpoints"}, {"name": "Files & Import", "item": [{"name": "Upload Customer Import File", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "if (pm.response.code === 201) {", "    var jsonData = pm.response.json();", "    ", "    // Save file ID for subsequent tests", "    pm.environment.set(\"uploaded_file_id\", jsonData.fileId);", "    pm.environment.set(\"uploaded_file_name\", jsonData.fileName);", "    ", "    // Verify response structure", "    pm.test(\"Response has required fields\", function () {", "        pm.expect(jsonData).to.have.property('fileId');", "        pm.expect(jsonData).to.have.property('fileName');", "        pm.expect(jsonData).to.have.property('fileSize');", "        pm.expect(jsonData).to.have.property('filePath');", "        pm.expect(jsonData).to.have.property('uploadedAt');", "        pm.expect(jsonData).to.have.property('status');", "        pm.expect(jsonData).to.have.property('fileType');", "    });", "    ", "    pm.test(\"File upload successful\", function () {", "        pm.expect(jsonData.status).to.equal('uploaded');", "        pm.expect(jsonData.fileType).to.equal('customer_import');", "        pm.expect(jsonData.fileSize).to.be.greaterThan(0);", "    });", "    ", "    console.log(`✅ File uploaded: ${jsonData.fileName} (${jsonData.fileSize} bytes)`);", "    console.log(`📄 File ID: ${jsonData.fileId}`);", "} else {", "    console.log(\"❌ File upload failed\");", "    ", "    pm.test(\"Error response structure\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('error');", "    });", "}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["// Mock CSV data for testing (use this if no actual file)", "const mockCsvContent = `firstName,lastName,email,phone,dateOfBirth,address,city,state,zipCode,notes,preferredContactMethod,marketingOptIn", "<PERSON>,<PERSON><PERSON>,<EMAIL>,+1234567890,1990-01-15,\\\"123 Main St\\\",New York,NY,10001,\\\"Regular customer, prefers morning appointments\\\",email,true", "<PERSON>,<PERSON>,<EMAIL>,+1987654321,1985-03-22,\\\"456 Oak Ave\\\",Los Angeles,CA,90210,\\\"VIP customer, sensitive scalp\\\",sms,true", "<PERSON>,<PERSON>,micha<PERSON>.<EMAIL>,+1555123456,1992-07-08,\\\"789 Pine Rd\\\",Chicago,IL,60601,\\\"New customer referral from <PERSON>\\\",email,false", "<PERSON>,<PERSON>,<EMAIL>,+1555987654,1988-11-30,\\\"321 Elm St\\\",Miami,FL,33101,\\\"Allergic to certain hair products\\\",phone,true", "<PERSON>,<PERSON>,<EMAIL>,+1555456789,1995-05-17,\\\"654 Maple Dr\\\",Seattle,WA,98101,\\\"Prefers stylist Carol\\\",email,true`;", "", "pm.environment.set('mock_csv_content', mockCsvContent);", "pm.environment.set('mock_file_size', mockCsvContent.length);", "console.log('📝 Mock CSV data prepared for upload testing');", "console.log(`File size: ${mockCsvContent.length} bytes`);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "📎 Select your CSV/Excel file here. Use sample_customer_import.csv for clean data or sample_customer_import_with_errors.csv for testing error handling."}, {"key": "file_type", "value": "customer_import", "type": "text", "description": "Type of file upload (customer_import, employee_import, etc.)"}, {"key": "description", "value": "Customer import test from Postman - {{$timestamp}}", "type": "text", "description": "Optional description of the upload"}, {"key": "skip_duplicates", "value": "true", "type": "text", "description": "Skip existing customers (true/false)"}, {"key": "update_existing", "value": "false", "type": "text", "description": "Update existing customer records (true/false)"}]}, "url": {"raw": "{{base_url}}/api/v1/files/upload/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "upload", ""]}, "description": "**📁 File Upload Endpoint**\n\nUpload CSV or Excel files for customer import with comprehensive validation and processing.\n\n**🎯 Mock Data Available:**\n- **Clean Data**: Use sample_customer_import.csv (15 valid customer records)\n- **Error Testing**: Use sample_customer_import_with_errors.csv (includes validation errors)\n\n**📋 Expected CSV Format:**\n```csv\nfirstName,lastName,email,phone,dateOfBirth,address,city,state,zipCode,notes,preferredContactMethod,marketingOptIn\nJohn,Doe,<EMAIL>,+1234567890,1990-01-15,\"123 Main St\",New York,NY,10001,\"Regular customer\",email,true\n```\n\n**📤 Request (multipart/form-data):**\n- `file`: CSV/Excel file (required)\n- `file_type`: Type identifier (default: customer_import)\n- `description`: Optional description\n- `skip_duplicates`: Skip existing customers (default: true)\n- `update_existing`: Update existing records (default: false)\n\n**✅ Success Response (201):**\n```json\n{\n  \"fileId\": \"file_abc123456\",\n  \"fileName\": \"customers.csv\",\n  \"fileSize\": 1547,\n  \"filePath\": \"/uploads/customer_imports/file_abc123456.csv\",\n  \"uploadedAt\": \"2024-01-15T10:30:00Z\",\n  \"status\": \"uploaded\",\n  \"fileType\": \"customer_import\"\n}\n```\n\n**❌ Error Response (400):**\n```json\n{\n  \"error\": \"Invalid file type\",\n  \"detail\": \"Only CSV and Excel files are supported\",\n  \"supported_formats\": [\".csv\", \".xlsx\", \".xls\"]\n}\n```"}, "response": [{"name": "✅ Successful Upload", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "sample_customer_import.csv"}, {"key": "file_type", "value": "customer_import", "type": "text"}, {"key": "description", "value": "Customer data import - Test batch", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v1/files/upload/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "upload", ""]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"fileId\": \"file_abc123456\",\n  \"fileName\": \"sample_customer_import.csv\",\n  \"fileSize\": 1547,\n  \"filePath\": \"/uploads/customer_imports/file_abc123456.csv\",\n  \"uploadedAt\": \"2024-01-15T10:30:00Z\",\n  \"status\": \"uploaded\",\n  \"fileType\": \"customer_import\"\n}"}, {"name": "❌ Invalid File Type", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "invalid_file.txt"}, {"key": "file_type", "value": "customer_import", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v1/files/upload/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "upload", ""]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"error\": \"Invalid file type\",\n  \"detail\": \"Only CSV and Excel files are supported\",\n  \"supported_formats\": [\".csv\", \".xlsx\", \".xls\"],\n  \"received_format\": \".txt\"\n}"}]}, {"name": "Process Uploaded File", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "if (pm.response.code === 200) {", "    var jsonData = pm.response.json();", "    ", "    pm.test(\"Processing started successfully\", function () {", "        pm.expect(jsonData.status).to.equal('processing');", "        pm.expect(jsonData.file_id).to.equal(pm.environment.get('uploaded_file_id'));", "        pm.expect(jsonData.message).to.include('processing started');", "    });", "    ", "    console.log('⚙️ File processing started successfully');", "    console.log(`📄 File ID: ${jsonData.file_id}`);", "    console.log(`🔄 Status: ${jsonData.status}`);", "    ", "    // Set processing flag for status checks", "    pm.environment.set('processing_started', 'true');", "    pm.environment.set('processing_start_time', new Date().toISOString());", "} else {", "    console.log('❌ Failed to start processing');", "    ", "    pm.test(\"Error response structure\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('error');", "    });", "}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["// Check if we have a file ID from previous upload", "const fileId = pm.environment.get('uploaded_file_id');", "if (!fileId) {", "    console.log('❌ No file ID found. Please upload a file first.');", "    // Set a mock file ID for testing", "    pm.environment.set('uploaded_file_id', 'file_mock123456');", "    console.log('🔧 Using mock file ID for testing: file_mock123456');", "} else {", "    console.log(`🔍 Processing file ID: ${fileId}`);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"skip_duplicates\": true,\n    \"update_existing\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/files/{{uploaded_file_id}}/process/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "{{uploaded_file_id}}", "process", ""]}, "description": "**⚙️ Process File Endpoint**\n\nStart processing an uploaded file for customer import.\n\n**📤 Request Body (JSON):**\n- `skip_duplicates`: Skip existing customers (default: true)\n- `update_existing`: Update existing records (default: false)\n\n**✅ Success Response (200):**\n```json\n{\n  \"message\": \"File processing started\",\n  \"file_id\": \"file_abc123456\",\n  \"status\": \"processing\"\n}\n```\n\n**❌ Error Responses:**\n- **404**: File not found\n- **400**: File already processed or invalid state\n- **401**: Authentication required\n\n**🔄 Processing Flow:**\n1. Upload file → Get file_id\n2. Process file → Start processing\n3. Check status → Monitor progress\n4. Get results → Review import results"}, "response": [{"name": "✅ Processing Started", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"skip_duplicates\": true,\n    \"update_existing\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/files/file_abc123456/process/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "file_abc123456", "process", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"File processing started\",\n  \"file_id\": \"file_abc123456\",\n  \"status\": \"processing\"\n}"}, {"name": "❌ File Not Found", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"skip_duplicates\": true,\n    \"update_existing\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/files/invalid_file_id/process/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "invalid_file_id", "process", ""]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"error\": \"File not found\",\n  \"detail\": \"The specified file ID does not exist or has been deleted\",\n  \"file_id\": \"invalid_file_id\"\n}"}]}, {"name": "Check Processing Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "if (pm.response.code === 200) {", "    var jsonData = pm.response.json();", "    ", "    pm.test(\"Status response structure\", function () {", "        pm.expect(jsonData).to.have.property('file_id');", "        pm.expect(jsonData).to.have.property('status');", "        pm.expect(jsonData).to.have.property('progress');", "        pm.expect(jsonData).to.have.property('total_rows');", "        pm.expect(jsonData).to.have.property('processed_rows');", "        pm.expect(jsonData).to.have.property('started_at');", "    });", "    ", "    // Enhanced progress logging", "    const progressBar = '█'.repeat(Math.floor(jsonData.progress / 10)) + '░'.repeat(10 - Math.floor(jsonData.progress / 10));", "    console.log(`📊 Processing Progress: [${progressBar}] ${jsonData.progress}%`);", "    console.log(`📈 Processed: ${jsonData.processed_rows}/${jsonData.total_rows} rows`);", "    console.log(`🔄 Status: ${jsonData.status.toUpperCase()}`);", "    ", "    // Status-specific actions", "    if (jsonData.status === 'completed') {", "        console.log('✅ Processing completed successfully!');", "        pm.environment.set('processing_completed', 'true');", "        pm.environment.set('processing_end_time', new Date().toISOString());", "        ", "        // Calculate processing duration", "        const startTime = pm.environment.get('processing_start_time');", "        if (startTime) {", "            const duration = (new Date() - new Date(startTime)) / 1000;", "            console.log(`⏱️ Processing took: ${duration} seconds`);", "        }", "    } else if (jsonData.status === 'failed') {", "        console.log('❌ Processing failed');", "        pm.environment.set('processing_failed', 'true');", "    } else if (jsonData.status === 'processing') {", "        console.log('⏳ Still processing... Check again in a few seconds');", "        if (jsonData.estimated_completion) {", "            console.log(`🕐 Estimated completion: ${jsonData.estimated_completion}`);", "        }", "    }", "    ", "    // Progress validation", "    pm.test(\"Progress is valid\", function () {", "        pm.expect(jsonData.progress).to.be.within(0, 100);", "        pm.expect(jsonData.processed_rows).to.be.at.most(jsonData.total_rows);", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/files/{{uploaded_file_id}}/status/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "{{uploaded_file_id}}", "status", ""]}, "description": "**📊 File Status Endpoint**\n\nGet real-time processing status of an uploaded file with progress tracking.\n\n**✅ Success Response (200):**\n```json\n{\n  \"file_id\": \"file_abc123456\",\n  \"status\": \"processing\",\n  \"progress\": 65.2,\n  \"total_rows\": 15,\n  \"processed_rows\": 10,\n  \"started_at\": \"2024-01-15T10:30:00Z\",\n  \"completed_at\": null,\n  \"estimated_completion\": \"2024-01-15T10:32:15Z\"\n}\n```\n\n**📋 Status Values:**\n- `uploaded`: File uploaded but not processed\n- `processing`: Currently processing\n- `completed`: Successfully completed\n- `failed`: Processing failed with errors\n\n**📊 Progress Calculation:**\n- Progress = (processed_rows / total_rows) × 100\n- Visual progress bar in test console\n- Estimated completion time when available\n\n**🔄 Polling Strategy:**\n- Check every 2-5 seconds during processing\n- Stop polling when status is 'completed' or 'failed'"}, "response": [{"name": "🔄 Processing Status", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/files/file_abc123456/status/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "file_abc123456", "status", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"file_id\": \"file_abc123456\",\n  \"status\": \"processing\",\n  \"progress\": 65.2,\n  \"total_rows\": 15,\n  \"processed_rows\": 10,\n  \"started_at\": \"2024-01-15T10:30:00Z\",\n  \"completed_at\": null,\n  \"estimated_completion\": \"2024-01-15T10:32:15Z\"\n}"}, {"name": "✅ Completed Status", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/files/file_abc123456/status/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "file_abc123456", "status", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"file_id\": \"file_abc123456\",\n  \"status\": \"completed\",\n  \"progress\": 100.0,\n  \"total_rows\": 15,\n  \"processed_rows\": 15,\n  \"started_at\": \"2024-01-15T10:30:00Z\",\n  \"completed_at\": \"2024-01-15T10:32:45Z\",\n  \"estimated_completion\": null\n}"}, {"name": "❌ Failed Status", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/files/file_error123/status/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "file_error123", "status", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"file_id\": \"file_error123\",\n  \"status\": \"failed\",\n  \"progress\": 23.5,\n  \"total_rows\": 10,\n  \"processed_rows\": 2,\n  \"started_at\": \"2024-01-15T10:30:00Z\",\n  \"completed_at\": null,\n  \"error_message\": \"Invalid CSV format detected at row 3\"\n}"}]}, {"name": "Get Import Results", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "if (pm.response.code === 200) {", "    var jsonData = pm.response.json();", "    ", "    pm.test(\"Results response structure\", function () {", "        pm.expect(jsonData).to.have.property('file_id');", "        pm.expect(jsonData).to.have.property('summary');", "        pm.expect(jsonData).to.have.property('imported');", "        pm.expect(jsonData).to.have.property('errors');", "        ", "        // Check summary structure", "        pm.expect(jsonData.summary).to.have.property('total_rows');", "        pm.expect(jsonData.summary).to.have.property('successful');", "        pm.expect(jsonData.summary).to.have.property('failed');", "        pm.expect(jsonData.summary).to.have.property('duplicates_skipped');", "    });", "    ", "    // Enhanced results logging with emojis", "    console.log('📊 ===== IMPORT RESULTS SUMMARY =====');", "    console.log(`📝 Total Rows Processed: ${jsonData.summary.total_rows}`);", "    console.log(`✅ Successfully Imported: ${jsonData.summary.successful}`);", "    console.log(`❌ Failed Imports: ${jsonData.summary.failed}`);", "    console.log(`🔄 Duplicates Skipped: ${jsonData.summary.duplicates_skipped}`);", "    ", "    // Success rate calculation", "    const successRate = (jsonData.summary.successful / jsonData.summary.total_rows * 100).toFixed(1);", "    console.log(`📈 Success Rate: ${successRate}%`);", "    ", "    // Log sample imported customers", "    if (jsonData.imported && jsonData.imported.length > 0) {", "        console.log('👥 Sample Imported Customers:');", "        jsonData.imported.slice(0, 3).forEach((customer, index) => {", "            console.log(`  ${index + 1}. ${customer.firstName} ${customer.lastName} (${customer.email})`);", "        });", "        if (jsonData.imported.length > 3) {", "            console.log(`  ... and ${jsonData.imported.length - 3} more customers`);", "        }", "    }", "    ", "    // Log validation errors", "    if (jsonData.errors && jsonData.errors.length > 0) {", "        console.log('⚠️ Validation Errors Found:');", "        jsonData.errors.forEach((error, index) => {", "            console.log(`  Row ${error.row}: ${error.errors.join(', ')}`);", "            if (index >= 4) {", "                console.log(`  ... and ${jsonData.errors.length - 5} more errors`);", "                return false;", "            }", "        });", "    }", "    ", "    // Save results to environment for further testing", "    pm.environment.set('import_successful_count', jsonData.summary.successful);", "    pm.environment.set('import_failed_count', jsonData.summary.failed);", "    pm.environment.set('import_success_rate', successRate);", "    ", "    // Validation tests", "    pm.test(\"Import summary totals are consistent\", function () {", "        const expectedTotal = jsonData.summary.successful + jsonData.summary.failed + jsonData.summary.duplicates_skipped;", "        pm.expect(jsonData.summary.total_rows).to.equal(expectedTotal);", "    });", "    ", "    pm.test(\"Imported customers have required fields\", function () {", "        if (jsonData.imported.length > 0) {", "            const firstCustomer = jsonData.imported[0];", "            pm.expect(firstCustomer).to.have.property('id');", "            pm.expect(firstCustomer).to.have.property('firstName');", "            pm.expect(firstCustomer).to.have.property('lastName');", "            pm.expect(firstCustomer).to.have.property('email');", "        }", "    });", "} else {", "    console.log('❌ Failed to retrieve import results');", "}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["// Check if processing is completed", "const isCompleted = pm.environment.get('processing_completed');", "if (!isCompleted) {", "    console.log('⚠️ Processing may not be completed. Results might not be available yet.');", "    console.log('💡 Try checking the status first or wait for processing to complete.');", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/files/{{uploaded_file_id}}/results/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "{{uploaded_file_id}}", "results", ""]}, "description": "**📋 Import Results Endpoint**\n\nGet comprehensive import results after processing is complete.\n\n**✅ Success Response (200):**\n```json\n{\n  \"file_id\": \"file_abc123456\",\n  \"summary\": {\n    \"total_rows\": 15,\n    \"successful\": 13,\n    \"failed\": 2,\n    \"duplicates_skipped\": 1\n  },\n  \"imported\": [\n    {\n      \"id\": 1,\n      \"firstName\": \"<PERSON>\",\n      \"lastName\": \"Doe\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1234567890\",\n      \"dateOfBirth\": \"1990-01-15\"\n    }\n  ],\n  \"errors\": [\n    {\n      \"row\": 3,\n      \"data\": {\n        \"firstName\": \"\",\n        \"email\": \"invalid-email\"\n      },\n      \"errors\": [\n        \"First name is required\",\n        \"Invalid email format\"\n      ]\n    }\n  ]\n}\n```\n\n**📊 Response Details:**\n- `summary`: High-level statistics\n- `imported`: Successfully imported customer records\n- `errors`: Failed rows with validation details\n\n**❌ Error Responses:**\n- **404**: File not found\n- **400**: File not yet processed\n- **401**: Authentication required\n\n**💡 Usage Notes:**\n- Only available for files with status 'completed'\n- Use for audit trails and error resolution\n- Results available for 30 days after processing"}, "response": [{"name": "✅ Successful Import Results", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/files/file_abc123456/results/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "file_abc123456", "results", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"file_id\": \"file_abc123456\",\n  \"summary\": {\n    \"total_rows\": 15,\n    \"successful\": 13,\n    \"failed\": 2,\n    \"duplicates_skipped\": 1\n  },\n  \"imported\": [\n    {\n      \"id\": 1,\n      \"firstName\": \"<PERSON>\",\n      \"lastName\": \"Doe\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1234567890\",\n      \"dateOfBirth\": \"1990-01-15\",\n      \"address\": \"123 Main St\",\n      \"city\": \"New York\",\n      \"state\": \"NY\",\n      \"zipCode\": \"10001\",\n      \"notes\": \"Regular customer, prefers morning appointments\",\n      \"preferredContactMethod\": \"email\",\n      \"marketingOptIn\": true\n    },\n    {\n      \"id\": 2,\n      \"firstName\": \"<PERSON>\",\n      \"lastName\": \"<PERSON>\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1987654321\",\n      \"dateOfBirth\": \"1985-03-22\",\n      \"address\": \"456 Oak Ave\",\n      \"city\": \"Los Angeles\",\n      \"state\": \"CA\",\n      \"zipCode\": \"90210\",\n      \"notes\": \"VIP customer, sensitive scalp\",\n      \"preferredContactMethod\": \"sms\",\n      \"marketingOptIn\": true\n    },\n    {\n      \"id\": 3,\n      \"firstName\": \"<PERSON>\",\n      \"lastName\": \"<PERSON>\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1555456789\",\n      \"dateOfBirth\": \"1995-05-17\",\n      \"address\": \"654 Maple Dr\",\n      \"city\": \"Seattle\",\n      \"state\": \"WA\",\n      \"zipCode\": \"98101\",\n      \"notes\": \"Prefers stylist Carol\",\n      \"preferredContactMethod\": \"email\",\n      \"marketingOptIn\": true\n    }\n  ],\n  \"errors\": [\n    {\n      \"row\": 3,\n      \"data\": {\n        \"firstName\": \"\",\n        \"lastName\": \"Johnson\",\n        \"email\": \"invalid-email\",\n        \"phone\": \"+1555123456\"\n      },\n      \"errors\": [\n        \"First name is required\",\n        \"Invalid email format\"\n      ]\n    },\n    {\n      \"row\": 5,\n      \"data\": {\n        \"firstName\": \"Sarah\",\n        \"lastName\": \"Williams\",\n        \"email\": \"<EMAIL>\",\n        \"phone\": \"invalid-phone\",\n        \"dateOfBirth\": \"invalid-date\"\n      },\n      \"errors\": [\n        \"Invalid phone number format\",\n        \"Invalid date format for dateOfBirth\"\n      ]\n    }\n  ]\n}"}, {"name": "❌ Processing Not Complete", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/files/file_processing123/results/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "file_processing123", "results", ""]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"error\": \"Processing not complete\",\n  \"detail\": \"File is still being processed. Check status endpoint first.\",\n  \"file_id\": \"file_processing123\",\n  \"current_status\": \"processing\",\n  \"progress\": 45.2\n}"}]}, {"name": "Upload File - Invalid Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Error message for invalid file type\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('error');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/invalid-file.txt", "description": "Upload an invalid file type to test validation"}, {"key": "file_type", "value": "customer_import", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v1/files/upload/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "upload", ""]}, "description": "Test uploading an invalid file type (should return 400 error)"}, "response": []}, {"name": "Upload File - No Authentication", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test(\"Authentication required\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('detail');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/test-file.csv"}]}, "url": {"raw": "{{base_url}}/api/v1/files/upload/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "upload", ""]}, "description": "Test uploading without authentication (should return 401 error)"}, "response": []}, {"name": "Get Upload Endpoint Info", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains endpoint info\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('methods');", "    pm.expect(jsonData).to.have.property('accepted_formats');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/files/upload/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "upload", ""]}, "description": "Get information about the file upload endpoint (methods, accepted formats, etc.)"}, "response": []}, {"name": "Process File", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains processing info\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('file_id');", "    pm.expect(jsonData).to.have.property('status');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"skip_duplicates\": true,\n    \"update_existing\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/files/{{uploaded_file_id}}/process/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "{{uploaded_file_id}}", "process", ""]}, "description": "Process an uploaded file for customer import. This endpoint starts the processing of a previously uploaded file, parsing the CSV/Excel data and importing customers into the system.\n\n**Request Body (JSON):**\n- `skip_duplicates`: <PERSON>olean (optional, defaults to true) - Skip customers that already exist\n- `update_existing`: <PERSON>olean (optional, defaults to false) - Update existing customer records\n\n**Response (200 OK):**\n```json\n{\n  \"message\": \"File processing started\",\n  \"file_id\": \"file_abc123\",\n  \"status\": \"processing\"\n}\n```\n\n**Error Responses:**\n- 404: File not found\n- 400: File already processed or invalid state\n- 401: Authentication required"}, "response": [{"name": "Processing Started", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"skip_duplicates\": true,\n    \"update_existing\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/files/file_abc123/process/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "file_abc123", "process", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"File processing started\",\n  \"file_id\": \"file_abc123\",\n  \"status\": \"processing\"\n}"}]}, {"name": "Get File Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains status info\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('file_id');", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('progress');", "    pm.expect(jsonData).to.have.property('total_rows');", "    pm.expect(jsonData).to.have.property('processed_rows');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/files/{{uploaded_file_id}}/status/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "{{uploaded_file_id}}", "status", ""]}, "description": "Get the processing status of an uploaded file. This endpoint provides real-time information about the file processing progress.\n\n**Response (200 OK):**\n```json\n{\n  \"file_id\": \"file_abc123\",\n  \"status\": \"processing\",\n  \"progress\": 45.5,\n  \"total_rows\": 1000,\n  \"processed_rows\": 455,\n  \"started_at\": \"2024-01-15T10:30:00Z\",\n  \"completed_at\": null,\n  \"estimated_completion\": \"2024-01-15T10:35:30Z\"\n}\n```\n\n**Status Values:**\n- `uploaded`: File uploaded but not yet processed\n- `processing`: File is currently being processed\n- `completed`: Processing completed successfully\n- `failed`: Processing failed with errors\n\n**Progress Calculation:**\n- Progress is calculated as (processed_rows / total_rows) * 100\n- For completed files, progress will be 100.0\n- For failed files, progress shows how far processing got before failure"}, "response": [{"name": "Processing Status", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/files/file_abc123/status/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "file_abc123", "status", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"file_id\": \"file_abc123\",\n  \"status\": \"processing\",\n  \"progress\": 45.5,\n  \"total_rows\": 1000,\n  \"processed_rows\": 455,\n  \"started_at\": \"2024-01-15T10:30:00Z\",\n  \"completed_at\": null,\n  \"estimated_completion\": \"2024-01-15T10:35:30Z\"\n}"}, {"name": "Completed Status", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/files/file_abc123/status/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "file_abc123", "status", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"file_id\": \"file_abc123\",\n  \"status\": \"completed\",\n  \"progress\": 100.0,\n  \"total_rows\": 1000,\n  \"processed_rows\": 1000,\n  \"started_at\": \"2024-01-15T10:30:00Z\",\n  \"completed_at\": \"2024-01-15T10:33:45Z\",\n  \"estimated_completion\": null\n}"}]}, {"name": "Get File Results", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains results\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('file_id');", "    pm.expect(jsonData).to.have.property('summary');", "    pm.expect(jsonData).to.have.property('imported');", "    pm.expect(jsonData).to.have.property('errors');", "    ", "    // Check summary structure", "    pm.expect(jsonData.summary).to.have.property('total_rows');", "    pm.expect(jsonData.summary).to.have.property('successful');", "    pm.expect(jsonData.summary).to.have.property('failed');", "    pm.expect(jsonData.summary).to.have.property('duplicates_skipped');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/files/{{uploaded_file_id}}/results/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "{{uploaded_file_id}}", "results", ""]}, "description": "Get the detailed results of a processed file. This endpoint returns comprehensive information about what was imported, what failed, and any duplicates that were skipped.\n\n**Response (200 OK):**\n```json\n{\n  \"file_id\": \"file_abc123\",\n  \"summary\": {\n    \"total_rows\": 1000,\n    \"successful\": 945,\n    \"failed\": 55,\n    \"duplicates_skipped\": 12\n  },\n  \"imported\": [\n    {\n      \"id\": 1,\n      \"firstName\": \"<PERSON>\",\n      \"lastName\": \"Doe\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1234567890\",\n      \"dateOfBirth\": \"1990-01-15\"\n    }\n  ],\n  \"errors\": [\n    {\n      \"row\": 23,\n      \"data\": {\n        \"firstName\": \"<PERSON>\",\n        \"lastName\": \"<PERSON>\",\n        \"email\": \"invalid-email\"\n      },\n      \"errors\": [\n        \"Invalid email format\"\n      ]\n    }\n  ]\n}\n```\n\n**Error Responses:**\n- 404: File not found\n- 400: File not yet processed or processing failed\n- 401: Authentication required\n\n**Notes:**\n- Only available for files with status 'completed'\n- The `imported` array contains successfully imported customer records\n- The `errors` array contains rows that failed validation with error details\n- Duplicate customers are counted in `duplicates_skipped` but not included in error details"}, "response": [{"name": "File Results", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/files/file_abc123/results/", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "file_abc123", "results", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"file_id\": \"file_abc123\",\n  \"summary\": {\n    \"total_rows\": 1000,\n    \"successful\": 945,\n    \"failed\": 55,\n    \"duplicates_skipped\": 12\n  },\n  \"imported\": [\n    {\n      \"id\": 1,\n      \"firstName\": \"<PERSON>\",\n      \"lastName\": \"<PERSON><PERSON>\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1234567890\",\n      \"dateOfBirth\": \"1990-01-15\"\n    },\n    {\n      \"id\": 2,\n      \"firstName\": \"<PERSON>\",\n      \"lastName\": \"<PERSON>\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+1987654321\",\n      \"dateOfBirth\": \"1985-03-22\"\n    }\n  ],\n  \"errors\": [\n    {\n      \"row\": 23,\n      \"data\": {\n        \"firstName\": \"<PERSON>\",\n        \"lastName\": \"<PERSON>\",\n        \"email\": \"invalid-email\"\n      },\n      \"errors\": [\n        \"Invalid email format\"\n      ]\n    },\n    {\n      \"row\": 156,\n      \"data\": {\n        \"firstName\": \"\",\n        \"lastName\": \"<PERSON>\",\n        \"email\": \"<EMAIL>\"\n      },\n      \"errors\": [\n        \"First name is required\"\n      ]\n    }\n  ]\n}"}]}], "description": "File upload and management endpoints. Supports uploading CSV and Excel files with metadata tracking and S3-like storage simulation."}, {"name": "Business Customers", "item": [{"name": "List Customers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/business-customers/?business_id={{business_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", ""], "query": [{"key": "business_id", "value": "{{business_id}}"}]}, "description": "List all customers for a business"}, "response": []}, {"name": "List My Business Customers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/business-customers/me/", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "me", ""]}, "description": "List all customers for the current user's business without needing to specify business_id"}, "response": []}, {"name": "Search My Business Customers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/business-customers/me/?q=john", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "me", ""], "query": [{"key": "q", "value": "john"}]}, "description": "Search customers by name, email, or phone in the current user's business"}, "response": []}, {"name": "Search Customers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/business-customers/?business_id={{business_id}}&q=john", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", ""], "query": [{"key": "business_id", "value": "{{business_id}}"}, {"key": "q", "value": "john"}]}, "description": "Search customers by name, email, or phone"}, "response": []}, {"name": "Create My Business Customer", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer_id\": 1,\n    \"notes\": \"Customer notes here\",\n    \"loyalty_points\": 0,\n    \"opt_in_marketing\": true,\n    \"email_reminders\": true,\n    \"sms_reminders\": true,\n    \"tag_ids\": [1, 2, 3]\n}"}, "url": {"raw": "{{base_url}}/api/v1/business-customers/me/", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "me", ""]}, "description": "Create a business-customer relationship in the current user's business"}, "response": []}, {"name": "Create Business Customer", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer_id\": 1,\n    \"notes\": \"Customer notes here\",\n    \"loyalty_points\": 0,\n    \"opt_in_marketing\": true,\n    \"email_reminders\": true,\n    \"sms_reminders\": true,\n    \"tag_ids\": [1, 2, 3]\n}"}, "url": {"raw": "{{base_url}}/api/v1/business-customers/?business_id={{business_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", ""], "query": [{"key": "business_id", "value": "{{business_id}}"}]}, "description": "Create a business-customer relationship"}, "response": []}, {"name": "Get My Business Customer", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/business-customers/me/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "me", "1", ""]}, "description": "Get a specific business customer from the current user's business"}, "response": []}, {"name": "Get Business Customer", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/business-customers/1/?business_id={{business_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "1", ""], "query": [{"key": "business_id", "value": "{{business_id}}"}]}, "description": "Get a specific business customer"}, "response": []}, {"name": "Update My Business Customer", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"notes\": \"Updated customer notes\",\n    \"loyalty_points\": 100,\n    \"opt_in_marketing\": true,\n    \"email_reminders\": true,\n    \"sms_reminders\": true,\n    \"tag_ids\": [1, 2, 4]\n}"}, "url": {"raw": "{{base_url}}/api/v1/business-customers/me/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "me", "1", ""]}, "description": "Update a business customer in the current user's business"}, "response": []}, {"name": "Update Business Customer", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"notes\": \"Updated customer notes\",\n    \"loyalty_points\": 100,\n    \"opt_in_marketing\": true,\n    \"email_reminders\": true,\n    \"sms_reminders\": true,\n    \"tag_ids\": [1, 2, 4]\n}"}, "url": {"raw": "{{base_url}}/api/v1/business-customers/1/?business_id={{business_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "1", ""], "query": [{"key": "business_id", "value": "{{business_id}}"}]}, "description": "Update a business customer"}, "response": []}, {"name": "Partially Update My Business Customer", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"notes\": \"Partial update notes\",\n    \"email_reminders\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/business-customers/me/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "me", "1", ""]}, "description": "Partially update a business customer in the current user's business"}, "response": []}, {"name": "Partially Update Business Customer", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"notes\": \"Partial update notes\",\n    \"email_reminders\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/business-customers/1/?business_id={{business_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "1", ""], "query": [{"key": "business_id", "value": "{{business_id}}"}]}, "description": "Partially update a business customer"}, "response": []}, {"name": "Delete My Business Customer", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/business-customers/me/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "me", "1", ""]}, "description": "Delete a business customer from the current user's business"}, "response": []}, {"name": "Delete Business Customer", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/business-customers/1/?business_id={{business_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "1", ""], "query": [{"key": "business_id", "value": "{{business_id}}"}]}, "description": "Delete a business customer"}, "response": []}, {"name": "Get Service Suggestions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/business-customers/me/1/service-suggestions/?requested_date=2025-06-27", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "me", "1", "service-suggestions", ""], "query": [{"key": "requested_date", "value": "2025-06-27", "description": "Date for the appointment (YYYY-MM-DD format, today or future). Optional - defaults to today if not provided.", "disabled": false}]}, "description": "Get personalized service suggestions for a specific customer based on their appointment history and the requested appointment date. The API automatically calculates suggestions based on the customer's last completed appointment and business rules, including employee availability.\n\nPath Parameters:\n- customer_id: ID of the customer (in the URL path)\n\nQuery Parameters:\n- requested_date (optional): Date for the appointment in YYYY-MM-DD format (today or future). Defaults to today if not provided.\n\nValidation:\n- Date cannot be in the past (today or future allowed)\n- Customer must belong to the current user's business\n\nThe API automatically determines:\n- Last appointment date, services, and add-ons\n- Days between last appointment and requested date\n- Whether customer is new\n- Style groups from service history\n- Appropriate suggestion rules to apply\n- Employee availability for suggested services\n\nReturns:\n- Customer ID and requested appointment date\n- Last appointment details with employee info, appointment_services and appointment_add_ons\n- Suggestions array with service suggestions, addon suggestions, and employee availability"}, "response": []}, {"name": "Check My Forms (Customer)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/business-customers/me/forms/", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "me", "forms", ""]}, "description": "Check form completion status for the current logged-in customer. This endpoint is for customers to check their own form completion status.\n\nThe system automatically determines which business the customer belongs to based on their BusinessCustomer relationship.\n\nForm completion is tracked by form submission status. If a signature is required for a form, the customer cannot submit without signing it.\n\nNote: Each customer can only have one BusinessCustomer record per business due to unique_together constraint.\n\nReturns:\n- Customer information and business name\n- Overall completion status (all_forms_completed)\n- Required forms count and completed forms count\n- Detailed form status for each required form (is_submitted, submitted_at)\n- List of missing forms that need to be submitted\n- Forms completion timestamp"}, "response": []}, {"name": "Check Customer Form Completion (Staff)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/business-customers/forms/?customer_id=1", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "forms", ""], "query": [{"key": "customer_id", "value": "1", "description": "ID of the customer to check form completion for"}]}, "description": "Check form completion status for a specific customer. This endpoint is for business staff to check which required forms a customer has submitted.\n\nForm completion is tracked by form submission status. If a signature is required for a form, the customer cannot submit without signing it.\n\nQuery Parameters:\n- customer_id: ID of the customer to check (required)\n\nReturns:\n- Customer information and business name\n- Overall completion status (all_forms_completed)\n- Required forms count and completed forms count\n- Detailed form status for each required form (is_submitted, submitted_at)\n- List of missing forms that need to be submitted\n- Forms completion timestamp"}, "response": []}, {"name": "Check All Customers Form Completion (Staff)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/business-customers/forms/?all_customers=true", "host": ["{{base_url}}"], "path": ["api", "v1", "business-customers", "forms", ""], "query": [{"key": "all_customers", "value": "true", "description": "Set to true to check form completion for all customers"}]}, "description": "Check form completion status for all customers in the business. This endpoint is for business staff to get an overview of form completion across all customers.\n\nForm completion is tracked by form submission status. If a signature is required for a form, the customer cannot submit without signing it.\n\nQuery Parameters:\n- all_customers: Set to 'true' to check all customers (required)\n\nReturns:\n- Business-wide statistics (total customers, completion rate)\n- List of all required forms for the business\n- Array of all customers with their completion status\n- Missing forms for each customer\n- Forms completion timestamps"}, "response": []}], "description": "Business Customer-related endpoints"}, {"name": "Forms", "item": [{"name": "Form Templates", "item": [{"name": "List Form Templates", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/forms/templates/", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "templates", ""]}, "description": "Get all form templates"}, "response": []}, {"name": "Get Form Template", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/forms/templates/:id/", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "templates", ":id", ""], "variable": [{"key": "id", "value": "1", "description": "Form template ID"}]}, "description": "Get a specific form template by ID"}, "response": []}, {"name": "Create Form Template", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Customer Intake Form\",\n    \"document_type\": \"intake\",\n    \"status\": \"Published\",\n    \"business\": 1,\n    \"content\": {\n        \"fields\": [\n            {\n                \"id\": \"name\",\n                \"type\": \"text\",\n                \"label\": \"Full Name\",\n                \"required\": true\n            },\n            {\n                \"id\": \"email\",\n                \"type\": \"email\",\n                \"label\": \"Email Address\",\n                \"required\": true\n            },\n            {\n                \"id\": \"phone\",\n                \"type\": \"tel\",\n                \"label\": \"Phone Number\",\n                \"required\": false\n            },\n            {\n                \"id\": \"allergies\",\n                \"type\": \"textarea\",\n                \"label\": \"Do you have any allergies?\",\n                \"required\": false\n            }\n        ]\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/forms/templates/", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "templates", ""]}, "description": "Create a new form template"}, "response": []}, {"name": "Update Form Template", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Customer Intake Form\",\n    \"document_type\": \"intake\",\n    \"status\": \"Published\",\n    \"business\": 1,\n    \"content\": {\n        \"fields\": [\n            {\n                \"id\": \"name\",\n                \"type\": \"text\",\n                \"label\": \"Full Name\",\n                \"required\": true\n            },\n            {\n                \"id\": \"email\",\n                \"type\": \"email\",\n                \"label\": \"Email Address\",\n                \"required\": true\n            },\n            {\n                \"id\": \"phone\",\n                \"type\": \"tel\",\n                \"label\": \"Phone Number\",\n                \"required\": true\n            },\n            {\n                \"id\": \"allergies\",\n                \"type\": \"textarea\",\n                \"label\": \"Do you have any allergies?\",\n                \"required\": false\n            },\n            {\n                \"id\": \"medical_history\",\n                \"type\": \"textarea\",\n                \"label\": \"Medical History\",\n                \"required\": false\n            }\n        ]\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/forms/templates/:id/", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "templates", ":id", ""], "variable": [{"key": "id", "value": "1", "description": "Form template ID"}]}, "description": "Update an existing form template"}, "response": []}, {"name": "Partial Update Form Template", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"Draft\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/forms/templates/:id/", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "templates", ":id", ""], "variable": [{"key": "id", "value": "1", "description": "Form template ID"}]}, "description": "Partially update a form template"}, "response": []}, {"name": "Delete Form Template", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/forms/templates/:id/", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "templates", ":id", ""], "variable": [{"key": "id", "value": "1", "description": "Form template ID"}]}, "description": "Delete a form template"}, "response": []}], "description": "Endpoints for managing form templates"}, {"name": "Form Submissions", "item": [{"name": "List Form Submissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/forms/submissions/", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "submissions", ""]}, "description": "Get all form submissions"}, "response": []}, {"name": "Filter Submissions by Business", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/forms/submissions/?business_id=1", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "submissions", ""], "query": [{"key": "business_id", "value": "1"}]}, "description": "Get form submissions filtered by business ID"}, "response": []}, {"name": "Filter Submissions by Template", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/forms/submissions/?template_id=1", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "submissions", ""], "query": [{"key": "template_id", "value": "1"}]}, "description": "Get form submissions filtered by template ID"}, "response": []}, {"name": "Get Submissions by Template (Action)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/forms/submissions/by_template/?template_id=1", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "submissions", "by_template", ""], "query": [{"key": "template_id", "value": "1"}]}, "description": "Get submissions for a specific template using the action endpoint"}, "response": []}, {"name": "Get Form Submission", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/forms/submissions/:id/", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "submissions", ":id", ""], "variable": [{"key": "id", "value": "1", "description": "Form submission ID"}]}, "description": "Get a specific form submission by ID"}, "response": []}, {"name": "Create Form Submission", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"form_template\": 1,\n    \"business\": 1,\n    \"customer\": 1,\n    \"content\": {\n        \"name\": \"<PERSON>\",\n        \"email\": \"<EMAIL>\",\n        \"phone\": \"************\",\n        \"allergies\": \"None\"\n    },\n    \"status\": \"Submitted\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/forms/submissions/", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "submissions", ""]}, "description": "Create a new form submission"}, "response": []}, {"name": "Update Form Submission", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"form_template\": 1,\n    \"business\": 1,\n    \"customer\": 1,\n    \"content\": {\n        \"name\": \"<PERSON>\",\n        \"email\": \"<EMAIL>\",\n        \"phone\": \"************\",\n        \"allergies\": \"None\"\n    },\n    \"status\": \"Reviewed\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/forms/submissions/:id/", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "submissions", ":id", ""], "variable": [{"key": "id", "value": "1", "description": "Form submission ID"}]}, "description": "Update an existing form submission"}, "response": []}, {"name": "Partial Update Form Submission", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"Approved\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/forms/submissions/:id/", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "submissions", ":id", ""], "variable": [{"key": "id", "value": "1", "description": "Form submission ID"}]}, "description": "Partially update a form submission"}, "response": []}, {"name": "Delete Form Submission", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/forms/submissions/:id/", "host": ["{{base_url}}"], "path": ["api", "v1", "forms", "submissions", ":id", ""], "variable": [{"key": "id", "value": "1", "description": "Form submission ID"}]}, "description": "Delete a form submission"}, "response": []}], "description": "Endpoints for managing form submissions"}], "description": "Endpoints for managing forms and form submissions"}, {"name": "Waitlist", "item": [{"name": "List Waitlist Entries", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/waitlist/", "host": ["{{base_url}}"], "path": ["api", "v1", "waitlist", ""], "query": [{"key": "status", "value": "current", "description": "Filter by status (current/expired)", "disabled": true}]}, "description": "Get all waitlist entries. Optional query parameter 'status' to filter by current or expired entries."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/waitlist/", "host": ["{{base_url}}"], "path": ["api", "v1", "waitlist", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"count\": 2,\n    \"next\": null,\n    \"previous\": null,\n    \"results\": [\n        {\n            \"id\": 1,\n            \"business\": 1,\n            \"business_name\": \"<PERSON>\",\n            \"customer_name\": \"<PERSON>\",\n            \"phone_number\": \"555-0123\",\n            \"email\": \"<EMAIL>\",\n            \"services\": [\n                {\n                    \"id\": 1,\n                    \"name\": \"Classic Lash Fullset(C01-C02)\",\n                    \"price\": \"199.00\",\n                    \"duration\": 70,\n                    \"buffer_time\": 15\n                }\n            ],\n            \"add_ons\": [\n                {\n                    \"id\": 1,\n                    \"name\": \"Bottom Lash(as an add-on service)\",\n                    \"price\": \"29.00\",\n                    \"duration\": 25\n                }\n            ],\n            \"employees\": [\n                {\n                    \"id\": 1,\n                    \"name\": \"<PERSON>\"\n                },\n                {\n                    \"id\": 2,\n                    \"name\": \"<PERSON>\"\n                }\n            ],\n            \"notes\": \"Prefers morning appointments. Allergic to certain adhesives.\",\n            \"status\": \"current\",\n            \"priority_rule\": \"manual\",\n            \"total_price\": \"228.00\",\n            \"total_duration\": 110,\n            \"created_at\": \"2025-07-04T05:30:00Z\",\n            \"updated_at\": \"2025-07-04T05:30:00Z\",\n            \"expired_at\": null,\n            \"availability\": [\n                {\n                    \"id\": 1,\n                    \"start_datetime\": \"2025-07-10T09:00:00Z\",\n                    \"end_datetime\": \"2025-07-10T12:00:00Z\"\n                }\n            ]\n        }\n    ]\n}"}]}, {"name": "Create Waitlist Entry", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"business\": {{business_id}},\n    \"customer_name\": \"<PERSON>\",\n    \"phone_number\": \"555-0123\",\n    \"email\": \"<EMAIL>\",\n    \"service_ids\": [1, 2],\n    \"add_on_ids\": [1],\n    \"employee_ids\": [1, 2],\n    \"notes\": \"Prefers morning appointments. Allergic to certain adhesives.\",\n    \"status\": \"current\",\n    \"priority_rule\": \"manual\",\n    \"availability\": [\n        {\n            \"start_datetime\": \"2025-07-10T09:00:00Z\",\n            \"end_datetime\": \"2025-07-10T12:00:00Z\"\n        },\n        {\n            \"start_datetime\": \"2025-07-11T14:00:00Z\",\n            \"end_datetime\": \"2025-07-11T17:00:00Z\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/waitlist/", "host": ["{{base_url}}"], "path": ["api", "v1", "waitlist", ""]}, "description": "Create a new waitlist entry with services, add-ons, and preferred time windows."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"business\": 1,\n    \"customer_name\": \"<PERSON>\",\n    \"phone_number\": \"555-0123\",\n    \"email\": \"<EMAIL>\",\n    \"service_ids\": [1, 2],\n    \"add_on_ids\": [1],\n    \"notes\": \"Prefers morning appointments.\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/waitlist/", "host": ["{{base_url}}"], "path": ["api", "v1", "waitlist", ""]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"id\": 2,\n    \"business\": 1,\n    \"business_name\": \"<PERSON> Lash\",\n    \"customer_name\": \"<PERSON>\",\n    \"phone_number\": \"555-0123\",\n    \"email\": \"<EMAIL>\",\n    \"services\": [\n        {\n            \"id\": 1,\n            \"name\": \"Classic Lash Fullset(C01-C02)\",\n            \"price\": \"199.00\",\n            \"duration\": 70,\n            \"buffer_time\": 15\n        },\n        {\n            \"id\": 2,\n            \"name\": \"<PERSON><PERSON><PERSON> Lash Fullset(S01-S08)\",\n            \"price\": \"229.00\",\n            \"duration\": 105,\n            \"buffer_time\": 15\n        }\n    ],\n    \"add_ons\": [\n        {\n            \"id\": 1,\n            \"name\": \"Bottom Lash(as an add-on service)\",\n            \"price\": \"29.00\",\n            \"duration\": 25\n        }\n    ],\n    \"employees\": [\n        {\n            \"id\": 1,\n            \"name\": \"<PERSON>\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"<PERSON>\"\n        }\n    ],\n    \"notes\": \"Prefers morning appointments.\",\n    \"status\": \"current\",\n    \"priority_rule\": \"manual\",\n    \"total_price\": \"457.00\",\n    \"total_duration\": 230,\n    \"created_at\": \"2025-07-04T05:35:00Z\",\n    \"updated_at\": \"2025-07-04T05:35:00Z\",\n    \"expired_at\": null,\n    \"availability\": [\n        {\n            \"id\": 2,\n            \"start_datetime\": \"2025-07-10T09:00:00Z\",\n            \"end_datetime\": \"2025-07-10T12:00:00Z\"\n        },\n        {\n            \"id\": 3,\n            \"start_datetime\": \"2025-07-11T14:00:00Z\",\n            \"end_datetime\": \"2025-07-11T17:00:00Z\"\n        }\n    ]\n}"}]}, {"name": "Get Waitlist Entry", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/waitlist/{{waitlist_entry_id}}/", "host": ["{{base_url}}"], "path": ["api", "v1", "waitlist", "{{waitlist_entry_id}}", ""]}, "description": "Retrieve a specific waitlist entry by ID."}, "response": []}, {"name": "Update Waitlist Entry", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer_name\": \"<PERSON>\",\n    \"phone_number\": \"555-0124\",\n    \"notes\": \"Updated notes: Prefers afternoon appointments now.\",\n    \"service_ids\": [1, 3],\n    \"add_on_ids\": []\n}"}, "url": {"raw": "{{base_url}}/api/v1/waitlist/{{waitlist_entry_id}}/", "host": ["{{base_url}}"], "path": ["api", "v1", "waitlist", "{{waitlist_entry_id}}", ""]}, "description": "Update a waitlist entry. Use PATCH for partial updates or PUT for full replacement."}, "response": []}, {"name": "Delete Waitlist Entry", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/waitlist/{{waitlist_entry_id}}/", "host": ["{{base_url}}"], "path": ["api", "v1", "waitlist", "{{waitlist_entry_id}}", ""]}, "description": "Delete a waitlist entry (soft delete)."}, "response": []}, {"name": "Expire Waitlist Entry", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/waitlist/{{waitlist_entry_id}}/expire/", "host": ["{{base_url}}"], "path": ["api", "v1", "waitlist", "{{waitlist_entry_id}}", "expire", ""]}, "description": "Mark a waitlist entry as expired."}, "response": []}, {"name": "Reactivate Waitlist Entry", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/v1/waitlist/{{waitlist_entry_id}}/reactivate/", "host": ["{{base_url}}"], "path": ["api", "v1", "waitlist", "{{waitlist_entry_id}}", "reactivate", ""]}, "description": "Reactivate an expired waitlist entry."}, "response": []}], "description": "Waitlist management endpoints for tracking customer service requests and preferred appointment windows"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "token", "value": "your_access_token_here", "type": "string"}, {"key": "business_id", "value": "1", "type": "string"}, {"key": "waitlist_entry_id", "value": "1", "type": "string"}]}