# Docker Build and Deployment Guide

## Overview
This guide covers building and deploying a single Docker image that serves both API and worker services for the Chatbook backend.

## Files Created
- `Dockerfile.sqs-services` - Multi-service container for SQS API and worker services
- `docker-entrypoint.sh` - Smart entrypoint script for service selection
- `.dockerignore` - Build optimization

## Building the Image

### 1. Build locally for testing
```bash
docker build -f Dockerfile.sqs-services -t chatbook-sqs-services:latest .
```

### 2. Build and push to ECR
```bash
# Login to ECR
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin ************.dkr.ecr.us-west-2.amazonaws.com

# Build with ECR tag
docker build -f Dockerfile.sqs-services -t ************.dkr.ecr.us-west-2.amazonaws.com/chatbook-impots-dev:latest .

# Push to ECR
docker push ************.dkr.ecr.us-west-2.amazonaws.com/chatbook-impots-dev:latest
```

## Running the Container

### API Service
```bash
# Using command argument
docker run -p 8000:8000 chatbook-sqs-services:latest api

# Using environment variable
docker run -e SERVICE_TYPE=api -p 8000:8000 chatbook-sqs-services:latest
```

### Worker Service
```bash
# Using command argument
docker run chatbook-sqs-services:latest worker

# Using environment variable
docker run -e SERVICE_TYPE=worker chatbook-sqs-services:latest
```

### Other Commands
```bash
# Run migrations
docker run chatbook-sqs-services:latest migrate

# Django shell
docker run -it chatbook-sqs-services:latest shell

# Custom management command
docker run chatbook-sqs-services:latest manage collectstatic --noinput
```

## CDK Updates Required

Replace your existing container definitions with the simplified versions in `cdk_updates.py`:

1. **API Container**: Uses `command=["api"]`
2. **Worker Container**: Uses `command=["worker"]`

## Environment Variables

The container uses these key environment variables:

### Common
- `SERVICE_TYPE`: "api" or "worker" (fallback if no command specified)
- `DJANGO_SETTINGS_MODULE`: Settings module to use
- `AWS_DEFAULT_REGION`: AWS region

### API Service
- `AWS_SQS_FILE_PROCESSING_QUEUE_URL`: Queue URL for sending messages
- `AWS_STORAGE_BUCKET_NAME`: S3 bucket for file storage

### Worker Service
- `AWS_SQS_FILE_PROCESSING_QUEUE_URL`: Queue URL for receiving messages
- `AWS_SNS_TOPIC_ARN`: SNS topic for failure notifications
- `AWS_STORAGE_BUCKET_NAME`: S3 bucket for file processing

## Health Checks

The Dockerfile includes a health check for the API service:
- Endpoint: `http://localhost:8000/health/`
- Interval: 30 seconds
- Timeout: 10 seconds
- Retries: 3

## Benefits

1. **Single Image**: One Docker image for both services
2. **Simplified Deployment**: Same image, different commands
3. **Cost Effective**: Reduced ECR storage and build times
4. **Consistency**: Same base environment for both services
5. **Flexibility**: Can run any Django management command

## Troubleshooting

### Container won't start
- Check logs: `docker logs <container_id>`
- Verify environment variables are set correctly
- Ensure database connectivity

### Worker not processing messages
- Check SQS queue permissions
- Verify queue URL is correct
- Check CloudWatch logs for worker service

### API not accessible
- Verify port mapping: `-p 8000:8000`
- Check health endpoint: `curl http://localhost:8000/health/`
- Review load balancer configuration

## Next Steps

1. Update your CDK stack with the new container definitions
2. Build and push the new image to ECR
3. Deploy the updated ECS services
4. Monitor CloudWatch logs to ensure both services start correctly 