/**
 * Aggressive Sidebar Scroll Position Manager for Jazzmin
 * Maintains sidebar scroll position when navigating between admin pages
 */

(function() {
    'use strict';

    const STORAGE_KEY = 'jazzmin_sidebar_scroll_position';
    const SIDEBAR_SELECTORS = [
        '.main-sidebar .nav-sidebar',
        '.main-sidebar .sidebar',
        '.main-sidebar',
        '[data-widget="pushmenu"] + .main-sidebar .nav-sidebar'
    ];

    let currentScrollPosition = 0;
    let sidebar = null;

    /**
     * Find the correct sidebar element
     */
    function findSidebar() {
        for (let selector of SIDEBAR_SELECTORS) {
            const element = document.querySelector(selector);
            if (element) {
                return element;
            }
        }
        return null;
    }

    /**
     * Save current sidebar scroll position to localStorage
     */
    function saveSidebarScrollPosition() {
        sidebar = sidebar || findSidebar();
        if (sidebar) {
            currentScrollPosition = sidebar.scrollTop;
            localStorage.setItem(STORAGE_KEY, currentScrollPosition.toString());
            console.log('Saved scroll position:', currentScrollPosition);
        }
    }

    /**
     * Restore sidebar scroll position from localStorage
     */
    function restoreSidebarScrollPosition() {
        sidebar = findSidebar();
        const savedPosition = localStorage.getItem(STORAGE_KEY);

        if (sidebar && savedPosition) {
            const position = parseInt(savedPosition, 10);

            // Multiple attempts to restore position
            const attempts = [0, 50, 100, 200, 500, 1000];
            attempts.forEach(delay => {
                setTimeout(() => {
                    if (sidebar) {
                        sidebar.scrollTop = position;
                        console.log(`Restored scroll position: ${position} (attempt at ${delay}ms)`);
                    }
                }, delay);
            });
        }
    }
    
    /**
     * Keep menu sections expanded based on current page
     */
    function maintainMenuState() {
        const currentPath = window.location.pathname;
        const menuItems = document.querySelectorAll('.nav-sidebar .nav-item[data-widget="treeview"]');

        menuItems.forEach(item => {
            const links = item.querySelectorAll('.nav-treeview .nav-link');
            let shouldExpand = false;

            links.forEach(link => {
                const href = link.getAttribute('href');
                if (href && currentPath.includes(href)) {
                    shouldExpand = true;
                    link.classList.add('active');
                }
            });

            if (shouldExpand) {
                item.classList.add('menu-open');
                const treeview = item.querySelector('.nav-treeview');
                if (treeview) {
                    treeview.style.display = 'block';
                }
            }
        });
    }

    /**
     * Add aggressive event listeners to capture all navigation
     */
    function addNavigationListeners() {
        // Save on any link click
        document.addEventListener('click', function(e) {
            const link = e.target.closest('a');
            if (link && link.closest('.main-sidebar')) {
                saveSidebarScrollPosition();
            }
        }, true);

        // Save on form submissions
        document.addEventListener('submit', function(e) {
            saveSidebarScrollPosition();
        }, true);

        // Save before page unload
        window.addEventListener('beforeunload', saveSidebarScrollPosition);
        window.addEventListener('pagehide', saveSidebarScrollPosition);

        // Save on browser navigation
        window.addEventListener('popstate', saveSidebarScrollPosition);

        // Continuous monitoring of scroll position
        sidebar = findSidebar();
        if (sidebar) {
            sidebar.addEventListener('scroll', function() {
                clearTimeout(this.scrollTimeout);
                this.scrollTimeout = setTimeout(() => {
                    currentScrollPosition = this.scrollTop;
                    localStorage.setItem(STORAGE_KEY, currentScrollPosition.toString());
                }, 100);
            });
        }
    }
    
    /**
     * Initialize scroll position manager with multiple fallbacks
     */
    function init() {
        console.log('Initializing sidebar scroll manager...');

        // Restore scroll position immediately
        restoreSidebarScrollPosition();

        // Maintain menu state
        maintainMenuState();

        // Add navigation listeners
        addNavigationListeners();

        // Force restore after a delay (in case of slow loading)
        setTimeout(restoreSidebarScrollPosition, 1500);
    }

    /**
     * Force initialization on multiple events
     */
    function forceInit() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
        } else {
            init();
        }
    }

    // Initialize immediately if possible
    forceInit();

    // Multiple fallback initializations
    document.addEventListener('DOMContentLoaded', init);
    window.addEventListener('load', init);

    // For AJAX/Turbo navigation
    document.addEventListener('turbo:load', init);
    document.addEventListener('pjax:complete', init);

    // For Jazzmin specific events
    document.addEventListener('shown.lte.pushmenu', init);
    document.addEventListener('collapsed.lte.pushmenu', init);

    // Periodic check to ensure scroll position is maintained
    setInterval(function() {
        const saved = localStorage.getItem(STORAGE_KEY);
        sidebar = findSidebar();
        if (sidebar && saved && Math.abs(sidebar.scrollTop - parseInt(saved)) > 10) {
            sidebar.scrollTop = parseInt(saved);
        }
    }, 2000);

})();
