/* Custom Admin Theme */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --text-color: #2c3e50;
    --background-color: #f5f6fa;
    --header-height: 60px;
}

/* Header Styling */
#header {
    background: var(--primary-color);
    color: white;
    height: var(--header-height);
    line-height: var(--header-height);
    padding: 0 2rem;
}

#header a:link, #header a:visited {
    color: white;
}

#branding h1 {
    color: white;
    font-weight: 600;
}

/* Content Area */
#content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 2rem;
    padding: 2rem;
}

/* Module Headers */
.module h2, .module caption {
    background: var(--primary-color);
    color: white;
    font-weight: 500;
    padding: 1rem;
}

/* Links and Buttons */
a:link, a:visited {
    color: var(--secondary-color);
    text-decoration: none;
}

a:hover {
    color: var(--accent-color);
}

.button, input[type=submit], input[type=button], .submit-row input {
    background: var(--secondary-color);
    border: none;
    border-radius: 4px;
    color: white;
    padding: 0.5rem 1rem;
    transition: background 0.3s ease;
}

.button:hover, input[type=submit]:hover, input[type=button]:hover {
    background: var(--accent-color);
}

/* Form Elements */
input[type=text], input[type=password], input[type=email], textarea, select {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0.5rem;
}

/* List View */
#changelist-form .results {
    border-radius: 4px;
    overflow: hidden;
}

#changelist table thead th {
    background: var(--background-color);
    border-bottom: 2px solid var(--primary-color);
    color: var(--text-color);
    padding: 1rem;
}

/* Messages */
.success {
    background: #2ecc71;
    color: white;
}

.warning {
    background: #f1c40f;
    color: white;
}

.error {
    background: var(--accent-color);
    color: white;
}

/* Ultra-aggressive sidebar scrolling fix */
.main-sidebar {
    position: fixed !important;
    height: 100vh;
    overflow-y: auto;
    scroll-behavior: auto !important;
    z-index: 1000;
    /* Prevent scroll reset */
    overscroll-behavior: contain;
    /* Force scroll position preservation */
    scroll-snap-type: none !important;
    scroll-restoration: manual !important;
}

.layout-fixed .brand-link {
    position: fixed;
    width: 250px;
    z-index: 1001;
}

.layout-fixed .main-sidebar .sidebar {
    height: calc(100vh - 57px);
    margin-top: 57px;
    padding-bottom: 100px;
}

.nav-sidebar {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: auto !important;
    /* Prevent automatic scroll to top */
    overscroll-behavior: contain;
    scroll-snap-type: none !important;
    scroll-restoration: manual !important;
    /* Force position preservation */
    contain: layout style paint;
}

/* Keep menu sections expanded */
.nav-sidebar .nav-item.menu-open > .nav-treeview {
    display: block !important;
}

.nav-sidebar .nav-item.menu-open > .nav-link .right {
    transform: rotate(-90deg);
}

/* Highlight active items better */
.nav-sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-left: 3px solid #007bff;
}

/* Smooth transitions */
.nav-sidebar .nav-link {
    transition: all 0.2s ease-in-out;
}

.content-wrapper {
    margin-left: 250px !important;
}

@media (max-width: 992px) {
    .content-wrapper {
        margin-left: 0 !important;
    }
}

/* Save sidebar scroll position */
html {
    scroll-behavior: smooth;
}

/* Force sidebar to maintain position */
.main-sidebar .nav-sidebar:focus-within {
    scroll-behavior: auto;
}

/* Prevent page jumps */
.main-sidebar .nav-link:focus {
    scroll-margin: 0;
    outline: none;
}

/* Additional scroll preservation */
@supports (scroll-timeline: auto) {
    .nav-sidebar {
        scroll-timeline: --sidebar-scroll;
    }
}

/* Prevent any automatic scrolling behaviors */
.main-sidebar *, .nav-sidebar * {
    scroll-margin: 0 !important;
    scroll-padding: 0 !important;
}

/* Prevent focus from causing scroll */
.main-sidebar .nav-link:focus,
.main-sidebar .nav-link:target {
    scroll-margin: 0 !important;
    scroll-behavior: auto !important;
}

/* Override any Jazzmin scroll behaviors */
.layout-fixed .main-sidebar,
.sidebar-mini .main-sidebar {
    scroll-restoration: manual !important;
    scroll-behavior: auto !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
    #content {
        margin: 1rem;
        padding: 1rem;
    }
}

@media (max-width: 767px) {
    #header {
        padding: 0 1rem;
    }
    
    #content {
        margin: 0.5rem;
        padding: 0.5rem;
    }
} 