from django import forms
from django.contrib import admin
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.db.models import Count
from .models import StyleGroup, ServiceCategory, Service, AddOn, ServiceAddOn, EmployeeService, StylistLevelService, StylistLevelAddOn, EmployeeAddOn, AddonSuggestionRule, LashServiceSuggestionRule
from datetime import timedelta
from employees.models import Employee

# Color picker widget
class ColorPickerWidget(forms.Widget):
    """Custom widget for selecting predefined colors"""

    # Get color choices from the model
    @property
    def color_choices(self):
        from .models import ServiceCategory
        return [choice[0] for choice in ServiceCategory.COLOR_CHOICES]

    def __init__(self, attrs=None):
        super().__init__(attrs)

    def render(self, name, value, attrs=None, renderer=None):
        if attrs is None:
            attrs = {}

        # Add CSS classes
        attrs['class'] = attrs.get('class', '') + ' color-picker-input'
        attrs['readonly'] = 'readonly'
        attrs['style'] = 'cursor: pointer;'

        # Handle display for no color option
        display_value = value or ''
        display_text = display_value if display_value else 'No Color'
        background_style = f'background-color: {display_value};' if display_value else 'background: repeating-linear-gradient(45deg, #f0f0f0, #f0f0f0 5px, #e0e0e0 5px, #e0e0e0 10px);'

        # Create the hidden input and color picker HTML
        html = f'''
        <div class="color-picker-container">
            <input type="hidden" name="{name}" value="{display_value}" id="id_{name}">
            <div class="color-display" style="
                width: 40px;
                height: 40px;
                {background_style}
                border: 2px solid #ddd;
                border-radius: 4px;
                cursor: pointer;
                display: inline-block;
                margin-right: 10px;
                vertical-align: middle;
            " onclick="toggleColorPicker_{name}()"></div>
            <span style="vertical-align: middle; font-family: monospace;">{display_text}</span>

            <div id="color-picker-{name}" class="color-picker-panel" style="
                display: none;
                position: absolute;
                background: white;
                border: 1px solid #ccc;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                margin-top: 5px;
                width: 280px;
            ">
                <div style="display: grid; grid-template-columns: repeat(7, 1fr); gap: 8px;">
        '''

        for color in self.color_choices:
            selected_class = 'selected' if color == value else ''
            if color == '':  # No color option
                background_style = 'background: repeating-linear-gradient(45deg, #f0f0f0, #f0f0f0 5px, #e0e0e0 5px, #e0e0e0 10px);'
                title_text = 'No Color'
            else:
                background_style = f'background-color: {color};'
                title_text = color

            html += f'''
                    <div class="color-option {selected_class}"
                         style="
                            width: 32px;
                            height: 32px;
                            {background_style}
                            border: 2px solid {'#333' if color == value else '#ddd'};
                            border-radius: 50%;
                            cursor: pointer;
                            transition: all 0.2s;
                         "
                         onclick="selectColor_{name}('{color}')"
                         title="{title_text}">
                    </div>
            '''

        html += '''
                </div>
            </div>
        </div>

        <script>
        function toggleColorPicker_''' + name + '''() {
            var picker = document.getElementById('color-picker-''' + name + '''');
            picker.style.display = picker.style.display === 'none' ? 'block' : 'none';
        }

        function selectColor_''' + name + '''(color) {
            document.getElementById('id_''' + name + '''').value = color;
            var colorDisplay = document.querySelector('.color-display');
            var textDisplay = document.querySelector('.color-picker-container span');

            if (color === '') {
                // No color selected
                colorDisplay.style.background = 'repeating-linear-gradient(45deg, #f0f0f0, #f0f0f0 5px, #e0e0e0 5px, #e0e0e0 10px)';
                textDisplay.textContent = 'No Color';
            } else {
                // Color selected
                colorDisplay.style.background = color;
                textDisplay.textContent = color;
            }

            document.getElementById('color-picker-''' + name + '''').style.display = 'none';

            // Update border for selected color
            document.querySelectorAll('#color-picker-''' + name + ''' .color-option').forEach(function(el) {
                el.style.border = '2px solid #ddd';
            });
            event.target.style.border = '2px solid #333';
        }

        // Close picker when clicking outside
        document.addEventListener('click', function(event) {
            var picker = document.getElementById('color-picker-''' + name + '''');
            var container = document.querySelector('.color-picker-container');
            if (picker && !container.contains(event.target)) {
                picker.style.display = 'none';
            }
        });
        </script>

        <style>
        .color-option:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        .color-picker-container {
            position: relative;
        }
        </style>
        '''

        return mark_safe(html)

# Custom forms
class ServiceCategoryForm(forms.ModelForm):
    """Custom form for ServiceCategory with color picker"""

    class Meta:
        model = ServiceCategory
        fields = '__all__'
        widgets = {
            'color': ColorPickerWidget(),
        }

# Duration field widgets
class DurationForm(forms.ModelForm):
    duration_minutes = forms.IntegerField(
        help_text='Duration in minutes',
        min_value=0,
        required=True,
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk and hasattr(self.instance, 'base_duration'):
            self.fields['duration_minutes'].initial = int(self.instance.base_duration.total_seconds() // 60)
    
    def save(self, commit=True):
        minutes = self.cleaned_data.get('duration_minutes', 0)
        self.instance.base_duration = timedelta(minutes=minutes)
        return super().save(commit)
    
    class Meta:
        abstract = True

class ServiceForm(DurationForm):
    buffer_minutes = forms.IntegerField(
        help_text='Buffer time in minutes',
        min_value=0,
        initial=15,
        required=True,
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            self.fields['buffer_minutes'].initial = int(self.instance.buffer_time.total_seconds() // 60)
            
            # Auto-populate short_name if it's empty and the name field is filled
            if not self.instance.short_name and self.instance.name:
                # Create a default short name suggestion based on the full name
                name = self.instance.name
                # Strip common prefixes/suffixes for better short names
                for prefix in ['Classic ', 'Styling ', 'Volume ', 'Premium ']:
                    if name.startswith(prefix):
                        name = name.replace(prefix, '', 1)
                        name = prefix.strip() + ' ' + name
                        break
                        
                # Handle specific name patterns
                if 'Within' in name and 'refill' in name:
                    parts = name.split('Within')
                    if len(parts) > 1:
                        name = parts[0].strip() + parts[1].replace('refill', 'refill')
                
                # Clean up parentheses content
                import re
                name = re.sub(r'\([^)]*\)', '', name)
                
                # Remove extra spaces and trim
                name = ' '.join(name.split())
                
                # Set as suggestion, not automatic value
                self.fields['short_name'].help_text = f'Suggested: "{name}"'
    
    def save(self, commit=True):
        buffer_minutes = self.cleaned_data.get('buffer_minutes', 15)
        self.instance.buffer_time = timedelta(minutes=buffer_minutes)
        return super().save(commit)
    
    class Meta:
        model = Service
        fields = '__all__'
        exclude = ['base_duration', 'buffer_time']

class AddOnForm(DurationForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            # Auto-populate short_name if it's empty and the name field is filled
            if not self.instance.short_name and self.instance.name:
                # Create a default short name
                name = self.instance.name
                
                # Handle specific cases
                if name == 'Bottom Lash(as an add-on service)':
                    name = 'Bottom'
                elif name == 'Consulting Session':
                    name = 'Consult'
                elif name == 'Lash Removal':
                    name = 'Removal'
                
                # Set as suggestion, not automatic value
                self.fields['short_name'].help_text = f'Suggested: "{name}"'
                
    class Meta:
        model = AddOn
        fields = '__all__'
        exclude = ['base_duration']

class ServiceAddOnForm(forms.ModelForm):
    duration_override_minutes = forms.IntegerField(
        help_text='Duration override in minutes (leave blank to use add-on default)',
        min_value=0,
        required=False,
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk and self.instance.duration_override:
            self.fields['duration_override_minutes'].initial = int(self.instance.duration_override.total_seconds() // 60)
    
    def save(self, commit=True):
        minutes = self.cleaned_data.get('duration_override_minutes')
        if minutes is not None:
            self.instance.duration_override = timedelta(minutes=minutes)
        return super().save(commit)
    
    class Meta:
        model = ServiceAddOn
        fields = '__all__'
        exclude = ['duration_override']

class EmployeeServiceForm(forms.ModelForm):
    custom_duration_minutes = forms.IntegerField(
        help_text='Custom duration in minutes (leave blank to use service default)',
        min_value=0,
        required=False,
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk and self.instance.custom_duration:
            self.fields['custom_duration_minutes'].initial = int(self.instance.custom_duration.total_seconds() // 60)
    
    def save(self, commit=True):
        minutes = self.cleaned_data.get('custom_duration_minutes')
        if minutes is not None:
            self.instance.custom_duration = timedelta(minutes=minutes)
        return super().save(commit)
    
    class Meta:
        model = EmployeeService
        fields = '__all__'
        exclude = ['custom_duration']

class ServiceAddOnInline(admin.TabularInline):
    model = ServiceAddOn
    form = ServiceAddOnForm
    extra = 0
    ordering = ('display_order', 'addon__name')
    fields = ('addon', 'addon_short_name', 'is_required', 'price_override', 'duration_override_minutes', 'display_order', 'is_active')
    autocomplete_fields = ['addon']
    readonly_fields = ('addon_short_name',)
    
    def addon_short_name(self, obj):
        return obj.addon.short_name if obj.addon else ''
    addon_short_name.short_description = 'Short Name'

@admin.register(ServiceCategory)
class ServiceCategoryAdmin(admin.ModelAdmin):
    form = ServiceCategoryForm
    list_display = ('name', 'business', 'color_display', 'order', 'service_count', 'is_active', 'created_at')
    list_filter = ('business', 'is_active')
    search_fields = ('name', 'business__name')
    ordering = ('order', 'name')
    autocomplete_fields = ['business']

    fieldsets = (
        ('Basic Information', {
            'fields': ('business', 'name', 'description', 'color', 'order', 'is_active')
        }),
    )

    def color_display(self, obj):
        """Display color as a colored box with the hex value"""
        return format_html(
            '<div style="width: 20px; height: 20px; background-color: {}; display: inline-block; border: 1px solid #ccc; margin-right: 5px;"></div>{}',
            obj.color,
            obj.color
        )
    color_display.short_description = 'Color'

    def service_count(self, obj):
        return obj.services.count()
    service_count.short_description = '# Services'

@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    form = ServiceForm
    list_display = ('name', 'short_name', 'business', 'category', 'display_base_price', 'display_duration', 'addon_count', 'is_active', 'show_online', 'created_at')
    list_filter = ('business', 'category', 'is_active', 'show_online')
    search_fields = ('name', 'short_name', 'business__name', 'category__name')
    ordering = ('business', 'category', 'name')
    inlines = [ServiceAddOnInline]
    autocomplete_fields = ['business', 'category']

    fieldsets = (
        ('Basic Information', {
            'fields': ('business', 'category', 'name', 'short_name', 'description', 'is_active')
        }),
        ('Service Details', {
            'fields': ('base_price', 'duration_minutes', 'buffer_minutes', 'image')
        }),
        ('Booking Options', {
            'fields': ('show_online',),
            'description': 'Control how this service appears in the booking system',
        }),
    )

    def display_base_price(self, obj):
        return format_html('${}', str(obj.base_price))
    display_base_price.short_description = 'Base Price'
    
    def display_duration(self, obj):
        minutes = int(obj.base_duration.total_seconds() / 60)
        return format_html('{} min', minutes)
    display_duration.short_description = 'Duration'

    def addon_count(self, obj):
        return obj.service_addons.count()
    addon_count.short_description = '# Add-ons'

@admin.register(AddOn)
class AddOnAdmin(admin.ModelAdmin):
    form = AddOnForm
    list_display = ('name', 'short_name', 'business', 'display_price', 'display_duration', 'service_count', 'is_active')
    list_filter = ('business', 'is_active')
    search_fields = ('name', 'short_name', 'business__name')
    ordering = ('business', 'name')
    autocomplete_fields = ['business', 'category']

    fieldsets = (
        ('Basic Information', {
            'fields': ('business', 'name', 'short_name', 'description', 'is_active', 'image')
        }),
        ('Add-on Details', {
            'fields': ('category', 'base_price', 'duration_minutes')
        }),
    )

    def display_price(self, obj):
        return format_html('${}', str(obj.base_price))
    display_price.short_description = 'Price'
    
    def display_duration(self, obj):
        minutes = int(obj.base_duration.total_seconds() / 60)
        return format_html('{} min', minutes)
    display_duration.short_description = 'Duration'

    def service_count(self, obj):
        return obj.service_addons.count()
    service_count.short_description = '# Services'

@admin.register(ServiceAddOn)
class ServiceAddOnAdmin(admin.ModelAdmin):
    form = ServiceAddOnForm
    list_display = ('service', 'service_short_name', 'addon', 'addon_short_name', 'is_required', 'display_order', 'display_price', 'display_duration', 'is_active')
    list_filter = ('is_required', 'service__business', 'is_active')
    search_fields = ('service__name', 'service__short_name', 'addon__name', 'addon__short_name', 'service__business__name')
    ordering = ('service__name', 'display_order', 'addon__name')
    autocomplete_fields = ['service', 'addon']

    fieldsets = (
        ('Service Add-on', {
            'fields': ('service', 'addon', 'is_required', 'is_active', 'display_order')
        }),
        ('Overrides', {
            'fields': ('price_override', 'duration_override_minutes'),
            'description': 'Optional overrides. Leave blank to use addon defaults.'
        }),
    )
    
    def service_short_name(self, obj):
        return obj.service.short_name if obj.service else ''
    service_short_name.short_description = 'Service Short Name'
    
    def addon_short_name(self, obj):
        return obj.addon.short_name if obj.addon else ''
    addon_short_name.short_description = 'Add-on Short Name'

    def display_price(self, obj):
        price = obj.price_override if obj.price_override is not None else obj.addon.base_price
        return format_html('${}', str(price))
    display_price.short_description = 'Price'

    def display_duration(self, obj):
        if obj.duration_override:
            minutes = int(obj.duration_override.total_seconds() / 60)
        else:
            minutes = int(obj.addon.base_duration.total_seconds() / 60)
        return format_html('{} min', minutes)
    display_duration.short_description = 'Duration'

@admin.register(EmployeeService)
class EmployeeServiceAdmin(admin.ModelAdmin):
    form = EmployeeServiceForm
    list_display = ('employee', 'service', 'business', 'display_price', 'display_duration', 'is_active')
    list_filter = ('is_active', 'business')
    search_fields = ('employee__user__email', 'employee__user__first_name', 'employee__user__last_name', 'service__name', 'business__name')
    ordering = ('employee__user__email', 'service__name')
    autocomplete_fields = ['business', 'employee', 'service']

    fieldsets = (
        ('Employee Service', {
            'fields': ('business', 'employee', 'service', 'is_active')
        }),
        ('Custom Settings', {
            'fields': ('custom_price', 'custom_duration_minutes'),
            'description': 'Optional custom price and duration. Leave blank to use service defaults.'
        }),
    )

    def display_price(self, obj):
        return format_html('${}', str(obj.price))
    display_price.short_description = 'Price'

    def display_duration(self, obj):
        minutes = int(obj.duration.total_seconds() / 60)
        return format_html('{} min', minutes)
    display_duration.short_description = 'Duration'

class StylistLevelServiceForm(forms.ModelForm):
    duration_minutes = forms.IntegerField(
        help_text='Duration in minutes',
        min_value=0,
        required=True,
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Filter stylist level choices based on the business
        if 'business' in self.fields and 'stylist_level' in self.fields:
            # Default to empty queryset
            self.fields['stylist_level'].queryset = self.fields['stylist_level'].queryset.none()
            
            # Get business ID if available
            business_id = None
            
            # For existing instances
            if self.instance and self.instance.pk and self.instance.business_id:
                business_id = self.instance.business_id
            
            # For initial form with business pre-selected
            if 'initial' in kwargs and kwargs['initial'] and 'business' in kwargs['initial']:
                business_id = kwargs['initial']['business']
            
            # Set queryset to be filtered by business if we have one
            if business_id:
                from business.models import StylistLevel
                self.fields['stylist_level'].queryset = StylistLevel.objects.filter(
                    business_id=business_id,
                    is_active=True
                ).order_by('level_order')
        
        # Set initial duration in minutes for existing instances
        if self.instance and self.instance.pk and self.instance.duration:
            self.fields['duration_minutes'].initial = int(self.instance.duration.total_seconds() // 60)
    
    def save(self, commit=True):
        minutes = self.cleaned_data.get('duration_minutes', 0)
        self.instance.duration = timedelta(minutes=minutes)
        return super().save(commit)
    
    class Meta:
        model = StylistLevelService
        fields = '__all__'
        exclude = ['duration']

@admin.register(StylistLevelService)
class StylistLevelServiceAdmin(admin.ModelAdmin):
    form = StylistLevelServiceForm
    list_display = ('service', 'stylist_level_name', 'business', 'display_price', 'display_duration', 'is_offered', 'is_active')
    list_filter = ('business', 'is_active', 'is_offered', 'stylist_level')
    search_fields = ('service__name', 'business__name', 'stylist_level__name')
    ordering = ('business', 'service__name', 'stylist_level__level_order')
    autocomplete_fields = ['business', 'service', 'stylist_level']

    fieldsets = (
        ('Basic Information', {
            'fields': ('business', 'service', 'stylist_level', 'is_active')
        }),
        ('Pricing & Duration', {
            'fields': ('price', 'duration_minutes', 'is_offered'),
            'description': 'Set pricing, duration, and whether this service is offered by this stylist level.'
        }),
    )

    def stylist_level_name(self, obj):
        return obj.stylist_level.name if obj.stylist_level else "-"
    stylist_level_name.short_description = 'Stylist Level'

    def display_price(self, obj):
        return format_html('${}', str(obj.price))
    display_price.short_description = 'Price'
    
    def display_duration(self, obj):
        minutes = int(obj.duration.total_seconds() / 60)
        return format_html('{} min', minutes)
    display_duration.short_description = 'Duration'
    
    def get_form(self, request, obj=None, **kwargs):
        """
        Override get_form to set initial data based on request GET parameters
        This enables pre-selecting the business when adding from an FK relation
        """
        form = super().get_form(request, obj, **kwargs)
        
        # If we're adding a new instance and business is in the URL, pre-select it
        if not obj and request.GET.get('business'):
            try:
                business_id = int(request.GET.get('business'))
                form.base_fields['business'].initial = business_id
            except (ValueError, TypeError):
                pass
                
        return form

@admin.register(EmployeeAddOn)
class EmployeeAddOnAdmin(admin.ModelAdmin):
    form = EmployeeServiceForm  # Reuse the same form type, as the field handling is the same
    list_display = ('employee', 'addon', 'business', 'display_price', 'display_duration', 'is_active')
    list_filter = ('is_active', 'business')
    search_fields = ('employee__user__email', 'employee__user__first_name', 'employee__user__last_name', 'addon__name', 'business__name')
    ordering = ('employee__user__email', 'addon__name')
    autocomplete_fields = ['business', 'employee', 'addon']

    fieldsets = (
        ('Employee Add-On', {
            'fields': ('business', 'employee', 'addon', 'is_active')
        }),
        ('Custom Settings', {
            'fields': ('custom_price', 'custom_duration_minutes'),
            'description': 'Optional custom price and duration. Leave blank to use add-on defaults.'
        }),
    )

    def display_price(self, obj):
        return format_html('${}', str(obj.price))
    display_price.short_description = 'Price'

    def display_duration(self, obj):
        minutes = int(obj.duration.total_seconds() / 60)
        return format_html('{} min', minutes)
    display_duration.short_description = 'Duration'

class StylistLevelAddOnForm(forms.ModelForm):
    duration_minutes = forms.IntegerField(
        help_text='Duration in minutes',
        min_value=0,
        required=True,
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Filter stylist level choices based on the business
        if 'business' in self.fields and 'stylist_level' in self.fields:
            # Default to empty queryset
            self.fields['stylist_level'].queryset = self.fields['stylist_level'].queryset.none()
            
            # Get business ID if available
            business_id = None
            
            # For existing instances
            if self.instance and self.instance.pk and self.instance.business_id:
                business_id = self.instance.business_id
            
            # For initial form with business pre-selected
            if 'initial' in kwargs and kwargs['initial'] and 'business' in kwargs['initial']:
                business_id = kwargs['initial']['business']
            
            # Set queryset to be filtered by business if we have one
            if business_id:
                from business.models import StylistLevel
                self.fields['stylist_level'].queryset = StylistLevel.objects.filter(
                    business_id=business_id,
                    is_active=True
                ).order_by('level_order')
        
        # Set initial duration in minutes for existing instances
        if self.instance and self.instance.pk and self.instance.duration:
            self.fields['duration_minutes'].initial = int(self.instance.duration.total_seconds() // 60)
    
    def save(self, commit=True):
        minutes = self.cleaned_data.get('duration_minutes', 0)
        self.instance.duration = timedelta(minutes=minutes)
        return super().save(commit)
    
    class Meta:
        model = StylistLevelAddOn
        fields = '__all__'
        exclude = ['duration']

@admin.register(StylistLevelAddOn)
class StylistLevelAddOnAdmin(admin.ModelAdmin):
    form = StylistLevelAddOnForm
    list_display = ('addon', 'stylist_level_name', 'business', 'display_price', 'display_duration', 'is_offered', 'is_active')
    list_filter = ('business', 'is_active', 'is_offered', 'stylist_level')
    search_fields = ('addon__name', 'business__name', 'stylist_level__name')
    ordering = ('business', 'addon__name', 'stylist_level__level_order')
    autocomplete_fields = ['business', 'addon', 'stylist_level']

    fieldsets = (
        ('Basic Information', {
            'fields': ('business', 'addon', 'stylist_level', 'is_active')
        }),
        ('Pricing & Duration', {
            'fields': ('price', 'duration_minutes', 'is_offered'),
            'description': 'Set pricing, duration, and whether this add-on is offered by this stylist level.'
        }),
    )

    def stylist_level_name(self, obj):
        return obj.stylist_level.name if obj.stylist_level else "-"
    stylist_level_name.short_description = 'Stylist Level'

    def display_price(self, obj):
        return format_html('${}', str(obj.price))
    display_price.short_description = 'Price'
    
    def display_duration(self, obj):
        minutes = int(obj.duration.total_seconds() / 60)
        return format_html('{} min', minutes)
    display_duration.short_description = 'Duration'


class AddonSuggestionRuleForm(forms.ModelForm):
    class Meta:
        model = AddonSuggestionRule
        fields = '__all__'
        widgets = {
            'min_days_since': forms.NumberInput(attrs={'min': 0, 'placeholder': 'e.g., 30'}),
            'max_days_since': forms.NumberInput(attrs={'min': 1, 'placeholder': 'e.g., 90'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Filter add-ons by business if we have a business context
        business_id = None
        if self.instance and self.instance.pk:
            business_id = self.instance.business_id
        elif 'initial' in kwargs and 'business' in kwargs['initial']:
            business_id = kwargs['initial']['business']

        if business_id:
            # Filter suggested_addon and last_addon by business
            addon_queryset = AddOn.objects.filter(
                business_id=business_id,
                is_active=True
            ).order_by('name')

            self.fields['suggested_addon'].queryset = addon_queryset
            self.fields['last_addon'].queryset = addon_queryset

        # Add help text and styling
        self.fields['suggested_addon'].help_text = 'The add-on to suggest to customers'
        self.fields['last_addon'].help_text = 'Suggest the add-on if customer previously had this add-on'
        self.fields['new_client_only'].help_text = 'Check this to only suggest to customers who have never booked before'
        self.fields['min_days_since'].help_text = 'Minimum days since last add-on (leave blank for no minimum)'
        self.fields['max_days_since'].help_text = 'Maximum days since last add-on (leave blank for no maximum)'

    def clean(self):
        cleaned_data = super().clean()
        new_client_only = cleaned_data.get('new_client_only')
        last_addon = cleaned_data.get('last_addon')
        min_days_since = cleaned_data.get('min_days_since')
        max_days_since = cleaned_data.get('max_days_since')

        # Validate that at least one criteria is specified
        has_time_criteria = min_days_since is not None or max_days_since is not None
        if not new_client_only and not last_addon and not has_time_criteria:
            raise forms.ValidationError(
                'At least one rule criteria must be specified: "New client only", "Last add-on", or time range.'
            )

        # Validate day range logic
        if (min_days_since is not None and max_days_since is not None and
            min_days_since >= max_days_since):
            raise forms.ValidationError(
                'Maximum days must be greater than minimum days.'
            )

        return cleaned_data


@admin.register(AddonSuggestionRule)
class AddonSuggestionRuleAdmin(admin.ModelAdmin):
    form = AddonSuggestionRuleForm
    list_display = (
        'suggested_addon',
        'business',
        'rule_criteria',
        'day_range',
        'is_active',
        'created_at'
    )
    list_filter = ('business', 'new_client_only', 'is_active', 'created_at')
    search_fields = (
        'suggested_addon__name',
        'last_addon__name',
        'business__name'
    )
    ordering = ('business', 'suggested_addon__name')
    autocomplete_fields = ['business', 'suggested_addon', 'last_addon']

    fieldsets = (
        ('Basic Information', {
            'fields': ('business', 'suggested_addon', 'is_active'),
            'description': 'Define which add-on to suggest and for which business.'
        }),
        ('Rule Criteria', {
            'fields': ('new_client_only', 'last_addon'),
            'description': 'Define when this add-on should be suggested.'
        }),
        ('Time Range (Optional)', {
            'fields': ('min_days_since', 'max_days_since'),
            'description': 'Optionally specify a time range since the last add-on. Only applies when "Last add-on" is selected.',
            'classes': ('collapse',)
        }),
    )

    def rule_criteria(self, obj):
        """Display the rule criteria in a readable format"""
        criteria = []
        if obj.new_client_only:
            criteria.append("New clients only")
        if obj.last_addon:
            criteria.append(f"After {obj.last_addon.name}")
        return " & ".join(criteria) if criteria else "No criteria"
    rule_criteria.short_description = 'Rule Criteria'

    def day_range(self, obj):
        """Display the day range in a readable format"""
        if not obj.last_addon:
            return "-"

        if obj.min_days_since is not None and obj.max_days_since is not None:
            return f"{obj.min_days_since}-{obj.max_days_since-1} days"
        elif obj.min_days_since is not None:
            return f"{obj.min_days_since}+ days"
        elif obj.max_days_since is not None:
            return f"<{obj.max_days_since} days"
        else:
            return "Any time"
    day_range.short_description = 'Day Range'

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related(
            'business', 'suggested_addon', 'last_addon'
        )


class LashServiceSuggestionRuleForm(forms.ModelForm):
    class Meta:
        model = LashServiceSuggestionRule
        fields = '__all__'
        widgets = {
            'min_days_since': forms.NumberInput(attrs={'min': 0, 'placeholder': 'e.g., 14'}),
            'max_days_since': forms.NumberInput(attrs={'min': 1, 'placeholder': 'e.g., 17'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Filter services by business if we have a business context
        business_id = None
        if self.instance and self.instance.pk:
            business_id = self.instance.business_id
        elif 'initial' in kwargs and 'business' in kwargs['initial']:
            business_id = kwargs['initial']['business']

        if business_id:
            # Filter next_service by business
            service_queryset = Service.objects.filter(
                business_id=business_id,
                is_active=True
            ).order_by('category__order', 'display_order', 'name')

            self.fields['next_service'].queryset = service_queryset

        # Add help text and styling
        self.fields['last_service_style_group'].help_text = 'Style group ID of the last service for efficient querying'
        self.fields['last_service_style_group_name'].help_text = 'Style group name (auto-populated from the selected style group)'
        self.fields['last_service_style_group_name'].widget.attrs['readonly'] = True
        self.fields['next_service'].help_text = 'The service to suggest next'
        self.fields['min_days_since'].help_text = 'Minimum days since last service (inclusive)'
        self.fields['max_days_since'].help_text = 'Maximum days since last service (exclusive)'

    def clean(self):
        cleaned_data = super().clean()
        min_days_since = cleaned_data.get('min_days_since')
        max_days_since = cleaned_data.get('max_days_since')

        # Validate day range logic
        if (min_days_since is not None and max_days_since is not None and
            min_days_since >= max_days_since):
            raise forms.ValidationError(
                'Maximum days must be greater than minimum days.'
            )

        return cleaned_data


@admin.register(LashServiceSuggestionRule)
class LashServiceSuggestionRuleAdmin(admin.ModelAdmin):
    form = LashServiceSuggestionRuleForm
    list_display = (
        'last_service_style_group_id_display',
        'last_service_style_group_name',
        'next_service_short_name',
        'business',
        'day_range',
        'is_active',
        'created_at'
    )
    list_filter = ('business', 'is_active', 'created_at', 'last_service_style_group_name', 'next_service__category')
    search_fields = (
        'last_service_style_group_name',
        'next_service__name',
        'next_service__short_name',
        'business__name'
    )
    ordering = ('business', 'last_service_style_group__name', 'min_days_since')
    autocomplete_fields = ['business', 'next_service']

    fieldsets = (
        ('Basic Information', {
            'fields': ('business', 'is_active'),
            'description': 'Define which business this rule applies to.'
        }),
        ('Service Progression', {
            'fields': ('last_service_style_group', 'last_service_style_group_name', 'next_service'),
            'description': 'Define the service progression: specify the style group that triggers the suggestion and what service to suggest. The name field is auto-populated.'
        }),
        ('Time Range', {
            'fields': ('min_days_since', 'max_days_since'),
            'description': 'Define when this suggestion should be made based on days since the last service.',
        }),
    )

    def last_service_style_group_id_display(self, obj):
        """Display the style group ID"""
        return obj.last_service_style_group.id if obj.last_service_style_group else None
    last_service_style_group_id_display.short_description = 'Style Group ID'

    def next_service_short_name(self, obj):
        """Display the short name of the next service"""
        return obj.next_service_short_name
    next_service_short_name.short_description = 'Next Service'

    def day_range(self, obj):
        """Display the day range in a readable format"""
        return f"{obj.min_days_since}-{obj.max_days_since-1} days"
    day_range.short_description = 'Day Range'

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related(
            'business', 'last_service_style_group', 'next_service',
            'next_service__category'
        )


@admin.register(StyleGroup)
class StyleGroupAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'display_order', 'created_at')
    list_editable = ('display_order',)
    search_fields = ('name', 'description')
    ordering = ('display_order', 'name')

    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'display_order')
        }),
    )
    
    def get_form(self, request, obj=None, **kwargs):
        """
        Override get_form to set initial data based on request GET parameters
        This enables pre-selecting the business when adding from an FK relation
        """
        form = super().get_form(request, obj, **kwargs)
        
        # If we're adding a new instance and business is in the URL, pre-select it
        if not obj and request.GET.get('business'):
            try:
                business_id = int(request.GET.get('business'))
                form.base_fields['business'].initial = business_id
            except (ValueError, TypeError):
                pass
                
        return form
