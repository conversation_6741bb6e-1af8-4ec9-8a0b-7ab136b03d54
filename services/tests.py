from django.test import TestCase
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from datetime import timedelta
from business.models import Business
from .models import AddOn, AddonSuggestionRule

User = get_user_model()


class AddonSuggestionRuleTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create a user first (required for Business)
        self.user = User.objects.create_user(
            identifier="<EMAIL>",
            email="<EMAIL>",
            phone_number="+***********",
            first_name="Test",
            last_name="Owner",
            password="testpass123"
        )

        self.business = Business.objects.create(
            name="Test Salon",
            owner=self.user,
            email="<EMAIL>",
            phone="555-0123"
        )

        self.addon1 = AddOn.objects.create(
            business=self.business,
            name="Hair Treatment",
            base_price=25.00,
            base_duration=timedelta(minutes=30)
        )

        self.addon2 = AddOn.objects.create(
            business=self.business,
            name="Deep Conditioning",
            base_price=35.00,
            base_duration=timedelta(minutes=45)
        )

    def test_create_new_client_rule(self):
        """Test creating a rule for new clients only"""
        rule = AddonSuggestionRule.objects.create(
            business=self.business,
            suggested_addon=self.addon1,
            new_client_only=True
        )

        self.assertTrue(rule.new_client_only)
        self.assertIsNone(rule.last_addon)
        self.assertIn("new clients only", str(rule))

    def test_create_last_addon_rule(self):
        """Test creating a rule based on last add-on"""
        rule = AddonSuggestionRule.objects.create(
            business=self.business,
            suggested_addon=self.addon2,
            last_addon=self.addon1,
            min_days_since=30,
            max_days_since=90
        )

        self.assertEqual(rule.last_addon, self.addon1)
        self.assertEqual(rule.min_days_since, 30)
        self.assertEqual(rule.max_days_since, 90)
        self.assertIn("after Hair Treatment", str(rule))
        self.assertIn("30-89 days ago", str(rule))

    def test_validation_no_criteria(self):
        """Test that validation fails when no criteria is specified"""
        with self.assertRaises(ValidationError):
            rule = AddonSuggestionRule(
                business=self.business,
                suggested_addon=self.addon1,
                new_client_only=False
            )
            rule.clean()

    def test_validation_day_range_without_last_addon(self):
        """Test that day ranges require last_addon"""
        with self.assertRaises(ValidationError):
            rule = AddonSuggestionRule(
                business=self.business,
                suggested_addon=self.addon1,
                new_client_only=True,
                min_days_since=30
            )
            rule.clean()

    def test_validation_invalid_day_range(self):
        """Test that min_days_since must be less than max_days_since"""
        with self.assertRaises(ValidationError):
            rule = AddonSuggestionRule(
                business=self.business,
                suggested_addon=self.addon2,
                last_addon=self.addon1,
                min_days_since=90,
                max_days_since=30
            )
            rule.clean()

    def test_str_representation(self):
        """Test string representation of the model"""
        rule = AddonSuggestionRule.objects.create(
            business=self.business,
            suggested_addon=self.addon1,
            new_client_only=True
        )

        expected = f"Suggest Hair Treatment - new clients only ({self.business.name})"
        self.assertEqual(str(rule), expected)
