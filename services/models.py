from django.db import models
from django.db.models import UniqueConstraint, CheckConstraint, Q, F
from datetime import timed<PERSON><PERSON>
from business.models import Business
# Don't import Employee here


class StyleGroup(models.Model):
    """
    Model to define style groups for lash services (Classic, Styling, Volume, Real Mink)
    """
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True, null=True)
    display_order = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'services'
        verbose_name = 'Style Group'
        verbose_name_plural = 'Style Groups'
        ordering = ['display_order', 'name']

    def __str__(self):
        return self.name

# Create your models here.

class ActiveManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_active=True)

class SoftDeleteModel(models.Model):
    is_active = models.BooleanField(default=True)

    objects = ActiveManager()
    all_objects = models.Manager()

    class Meta:
        abstract = True

class ServiceCategory(SoftDeleteModel):
    # Predefined color choices
    COLOR_CHOICES = [
        ('', 'No Color'),  # Added no color option
        ('#6D6E6F', 'Dark Gray'),
        ('#FFFFFF', 'White'),
        ('#E91E63', 'Default Pink'),
        ('#E6499A', 'Pink'),
        ('#F27679', 'Light Red'),
        ('#EF484A', 'Red'),
        ('#F69681', 'Coral'),
        ('#F26822', 'Orange'),
        ('#FAA53E', 'Light Orange'),
        ('#FAF6A7', 'Light Yellow'),
        ('#FDD628', 'Yellow'),
        ('#CBDD4E', 'Light Green'),
        ('#7DC242', 'Green'),
        ('#11A44A', 'Dark Green'),
        ('#68C4A0', 'Mint'),
        ('#0EB9A4', 'Teal'),
        ('#478CCA', 'Blue'),
        ('#7AA6BA', 'Light Blue'),
        ('#C3BFDF', 'Light Purple'),
        ('#EFBBD6', 'Light Pink'),
        ('#A391C5', 'Purple'),
        ('#B668AA', 'Dark Purple'),
        ('#B4B5B4', 'Gray'),
        ('#FFF8E9', 'Cream'),
        ('#E5B67F', 'Tan'),
        ('#B47E62', 'Brown'),
        ('#D6D6D6', 'Light Gray'),
    ]

    id = models.BigAutoField(primary_key=True)
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='service_categories')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    order = models.IntegerField(default=0)
    color = models.CharField(
        max_length=7,
        choices=COLOR_CHOICES,
        default='#E91E63',
        blank=True,
        help_text='Select a color for category display'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'services'
        verbose_name = 'Service Category'
        verbose_name_plural = 'Service Categories'
        ordering = ['order', 'name']
        constraints = [
            UniqueConstraint(fields=['business', 'name'], name='uniq_category_per_business')
        ]
        indexes = [
            models.Index(fields=['business'], name='idx_category_business', 
                        condition=Q(is_active=True))
        ]

    def __str__(self):
        return f"{self.name} - {self.business.name}"

    def clean(self):
        super().clean()
        # Validate color field is one of the predefined choices
        if self.color:
            valid_colors = [choice[0] for choice in self.COLOR_CHOICES]
            if self.color not in valid_colors:
                from django.core.exceptions import ValidationError
                raise ValidationError(
                    {'color': 'Please select a color from the available options.'}
                )

class Service(SoftDeleteModel):
    id = models.BigAutoField(primary_key=True)
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='services')
    category = models.ForeignKey(ServiceCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='services')
    name = models.CharField(max_length=255)
    short_name = models.CharField(max_length=100, blank=True, help_text='Short display name for the service')
    style_group = models.ForeignKey(
        StyleGroup,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='services',
        help_text='Style group for service progression (e.g., Classic, Styling, Volume, Real Mink)'
    )
    description = models.TextField(blank=True)
    base_price = models.DecimalField(max_digits=10, decimal_places=2)
    base_duration = models.DurationField(help_text='Duration of the service')
    buffer_time = models.DurationField(default=timedelta(minutes=15), help_text='Buffer time before and after the service')
    show_online = models.BooleanField(default=True, help_text='Show this service in online booking system')
    display_order = models.IntegerField(default=0, help_text='Order in which service is displayed')
    image = models.ImageField(upload_to='services/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'services'
        verbose_name = 'Service'
        verbose_name_plural = 'Services'
        ordering = ['category', 'display_order', 'name']
        indexes = [
            models.Index(
                fields=["business", "category"],
                name="svc_active_biz_cat_idx",
                condition=Q(is_active=True),
            ),
            models.Index(
                fields=["business", "show_online"],
                name="idx_service_online_booking",
                condition=Q(is_active=True, show_online=True),
            )
        ]
        constraints = [
            CheckConstraint(
                check=Q(base_duration__gt=timedelta(0)) & Q(buffer_time__gte=timedelta(0)),
                name='positive_durations'
            ),
            CheckConstraint(
                check=Q(base_price__gte=0),
                name="positive_service_price"
            )
        ]

    def __str__(self):
        return f"{self.name} - {self.business}"

    @property
    def total_duration(self):
        return self.base_duration + self.buffer_time

    @property
    def color(self):
        """Inherit color from the service category"""
        return self.category.color if self.category else ''
    
    def clean(self):
        super().clean()
        # Validate business-category relationship
        if self.category and self.category.business_id != self.business_id:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                {'category': 'Service category must belong to the same business as the service.'}
            )
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

class AddOn(SoftDeleteModel):
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='add_ons')
    name = models.CharField(max_length=100)
    short_name = models.CharField(max_length=50, blank=True, help_text='Short display name for the add-on')
    base_price = models.DecimalField(max_digits=10, decimal_places=2)
    base_duration = models.DurationField(help_text="Duration of the add-on")
    description = models.TextField(blank=True)
    image = models.ImageField(upload_to="addons/", null=True, blank=True)
    category = models.ForeignKey(ServiceCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='addons')
    display_order = models.IntegerField(default=0, help_text='Order in which add-on is displayed')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'services'
        ordering = ["display_order", "name"]
        constraints = [
            UniqueConstraint(fields=["business", "name"], name="uniq_addon_per_business"),
            CheckConstraint(
                check=Q(base_price__gte=0),
                name="positive_addon_price"
            ),
            CheckConstraint(
                check=Q(base_duration__gt=timedelta(0)),
                name="positive_addon_duration"
            )
        ]
        indexes = [
            models.Index(fields=['business'], name='idx_addon_business', 
                        condition=Q(is_active=True))
        ]

    def __str__(self):
        return f"{self.name} ({self.business.name})"

    @property
    def color(self):
        """Inherit color from the add-on category"""
        return self.category.color if self.category else ''

class ServiceAddOn(SoftDeleteModel):
    """
    Junction table for services and their available add-ons
    """
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='service_addons')
    addon = models.ForeignKey(AddOn, on_delete=models.CASCADE, related_name='service_addons')
    is_required = models.BooleanField(default=False, help_text="Whether this add-on is required for the service")
    price_override = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    duration_override = models.DurationField(null=True, blank=True)
    display_order = models.IntegerField(default=0, help_text="Order in which add-ons are displayed")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'services'
        verbose_name = 'Service add-on'
        verbose_name_plural = 'Service add-ons'
        ordering = ['display_order', 'addon__name']
        constraints = [
            UniqueConstraint(fields=["service", "addon"], name="uniq_addon_per_service"),
            CheckConstraint(
                check=Q(price_override__gte=0) | Q(price_override__isnull=True),
                name="positive_price_override"
            ),
            CheckConstraint(
                check=Q(duration_override__gt=timedelta(0)) | Q(duration_override__isnull=True),
                name="positive_duration_override"
            )
        ]
        indexes = [
            models.Index(fields=['service'], name='idx_serviceaddon_service',
                        condition=Q(is_active=True))
        ]

    def __str__(self):
        return f"{self.service.name} - {self.addon.name}"
    
    def clean(self):
        super().clean()
        # Validate business alignment
        if self.service.business_id != self.addon.business_id:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                {'addon': 'Add-on must belong to the same business as the service.'}
            )
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

class EmployeeService(SoftDeleteModel):
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='business_employee_services')
    employee = models.ForeignKey('employees.Employee', on_delete=models.CASCADE, related_name='employee_services')
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='employee_services')
    custom_price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, 
                   help_text="Use 0.00 to use service.base_price")
    custom_duration = models.DurationField(null=True, blank=True)

    class Meta:
        app_label = 'services'
        constraints = [
            UniqueConstraint(fields=['business', 'employee', 'service'], 
                            name='uniq_service_per_employee_business'),
            CheckConstraint(
                check=Q(custom_price__gte=0),
                name="positive_custom_price"
            ),
            CheckConstraint(
                check=Q(custom_duration__gt=timedelta(0)) | Q(custom_duration__isnull=True),
                name="positive_custom_duration"
            )
        ]
        indexes = [
            models.Index(fields=['business', 'employee'], 
                        name='idx_employeeservice_biz_emp',
                        condition=Q(is_active=True))
        ]

    def __str__(self):
        return f"{self.employee.first_name} {self.employee.last_name} - {self.service.name}"

    @property
    def price(self):
        """Return custom price if set (not zero), otherwise return price based on stylist level or service base price"""
        if self.custom_price and self.custom_price > 0:
            return self.custom_price
        
        # If employee has a stylist level, check for level-specific pricing
        if self.employee.stylist_level:
            stylist_level_pricing = StylistLevelService.objects.filter(
                business=self.business,
                service=self.service,
                stylist_level=self.employee.stylist_level,
                is_active=True,
                is_offered=True
            ).first()
            
            if stylist_level_pricing and stylist_level_pricing.price > 0:
                return stylist_level_pricing.price
                
        return self.service.base_price
    
    @property
    def duration(self):
        """Return custom duration if set, otherwise return duration based on stylist level or service base duration"""
        if self.custom_duration:
            return self.custom_duration
        
        # If employee has a stylist level, check for level-specific duration
        if self.employee.stylist_level:
            stylist_level_pricing = StylistLevelService.objects.filter(
                business=self.business,
                service=self.service,
                stylist_level=self.employee.stylist_level,
                is_active=True,
                is_offered=True
            ).first()
            
            if stylist_level_pricing and stylist_level_pricing.duration:
                return stylist_level_pricing.duration
                
        return self.service.base_duration
    
    @property
    def is_service_offered(self):
        """Check if this service is offered for the employee's stylist level"""
        # If no stylist level, assume the service is offered
        if not self.employee.stylist_level:
            return True
            
        # Check if there's a StylistLevelService record and if it's offered
        return StylistLevelService.objects.filter(
            business=self.business,
            service=self.service,
            stylist_level=self.employee.stylist_level,
            is_active=True,
            is_offered=True
        ).exists()
    
    def clean(self):
        super().clean()
        # Validate employee belongs to the business
        if self.employee.business_id != self.business_id:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                {'employee': 'Employee must belong to the specified business.'}
            )
        
        # Validate service belongs to the business
        if self.service.business_id != self.business_id:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                {'service': 'Service must belong to the specified business.'}
            )
            
        # Validate the service is offered for this employee's stylist level
        if self.employee.stylist_level and not self.is_service_offered:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                {'service': f'This service is not offered for the "{self.employee.stylist_level.name}" stylist level.'}
            )
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

class StylistLevelService(SoftDeleteModel):
    """
    Model to define default pricing and duration for services based on stylist level.
    Also controls whether a service is available for a given stylist level.
    These act as defaults for EmployeeService price and duration if no custom values are set.
    """
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='stylist_level_pricing')
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='stylist_level_services')
    
    # Use the StylistLevel model from business app
    stylist_level = models.ForeignKey(
        'business.StylistLevel',
        on_delete=models.CASCADE,
        related_name='service_pricing',
        verbose_name='Stylist Level'
    )
    
    # Price and duration settings
    price = models.DecimalField(max_digits=10, decimal_places=2)
    duration = models.DurationField(help_text='Duration of the service for this stylist level')
    
    # Availability control
    is_offered = models.BooleanField(
        default=True,
        help_text='Whether this stylist level can offer this service'
    )
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'services'
        verbose_name = 'Stylist Level Service'
        verbose_name_plural = 'Stylist Level Services'
        ordering = ['service', 'stylist_level']
        constraints = [
            UniqueConstraint(
                fields=['business', 'service', 'stylist_level'],
                name='uniq_pricing_per_level_service'
            ),
            CheckConstraint(
                check=Q(price__gte=0),
                name="positive_level_price"
            ),
            CheckConstraint(
                check=Q(duration__gt=timedelta(0)),
                name="positive_level_duration"
            )
        ]
        indexes = [
            models.Index(
                fields=['business', 'service', 'stylist_level'],
                name='idx_level_pricing',
                condition=Q(is_active=True)
            ),
            models.Index(
                fields=['business', 'service', 'stylist_level', 'is_offered'],
                name='idx_level_service_offering',
                condition=Q(is_active=True, is_offered=True)
            )
        ]

    def __str__(self):
        status = "offered" if self.is_offered else "not offered"
        return f"{self.service.name} - {self.stylist_level.name} - ${self.price} ({status})"

    def clean(self):
        super().clean()
        # Validate service belongs to the business
        if self.service.business_id != self.business_id:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                {'service': 'Service must belong to the specified business.'}
            )
        
        # Validate stylist_level belongs to the same business
        if self.stylist_level.business_id != self.business_id:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                {'stylist_level': 'Stylist level must belong to the specified business.'}
            )
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

class StylistLevelAddOn(SoftDeleteModel):
    """
    Model to define default pricing and duration for add-ons based on stylist level.
    Also controls whether an add-on is available for a given stylist level.
    These act as defaults for EmployeeAddOn price and duration if no custom values are set.
    """
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='stylist_level_addon_pricing')
    addon = models.ForeignKey(AddOn, on_delete=models.CASCADE, related_name='stylist_level_addons')
    
    # Use the StylistLevel model from business app
    stylist_level = models.ForeignKey(
        'business.StylistLevel',
        on_delete=models.CASCADE,
        related_name='addon_pricing',
        verbose_name='Stylist Level'
    )
    
    # Price and duration settings
    price = models.DecimalField(max_digits=10, decimal_places=2)
    duration = models.DurationField(help_text='Duration of the add-on for this stylist level')
    
    # Availability control
    is_offered = models.BooleanField(
        default=True,
        help_text='Whether this stylist level can offer this add-on'
    )
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'services'
        verbose_name = 'Stylist Level Add-On'
        verbose_name_plural = 'Stylist Level Add-Ons'
        ordering = ['addon', 'stylist_level']
        constraints = [
            UniqueConstraint(
                fields=['business', 'addon', 'stylist_level'],
                name='uniq_pricing_per_level_addon'
            ),
            CheckConstraint(
                check=Q(price__gte=0),
                name="positive_level_addon_price"
            ),
            CheckConstraint(
                check=Q(duration__gt=timedelta(0)),
                name="positive_level_addon_duration"
            )
        ]
        indexes = [
            models.Index(
                fields=['business', 'addon', 'stylist_level'],
                name='idx_level_addon_pricing',
                condition=Q(is_active=True)
            ),
            models.Index(
                fields=['business', 'addon', 'stylist_level', 'is_offered'],
                name='idx_level_addon_offering',
                condition=Q(is_active=True, is_offered=True)
            )
        ]

    def __str__(self):
        status = "offered" if self.is_offered else "not offered"
        return f"{self.addon.name} - {self.stylist_level.name} - ${self.price} ({status})"

    def clean(self):
        super().clean()
        # Validate addon belongs to the business
        if self.addon.business_id != self.business_id:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                {'addon': 'Add-on must belong to the specified business.'}
            )
        
        # Validate stylist_level belongs to the same business
        if self.stylist_level.business_id != self.business_id:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                {'stylist_level': 'Stylist level must belong to the specified business.'}
            )
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

class EmployeeAddOn(SoftDeleteModel):
    """
    Model to define custom pricing and duration for add-ons per employee.
    """
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='business_employee_addons')
    employee = models.ForeignKey('employees.Employee', on_delete=models.CASCADE, related_name='employee_addons')
    addon = models.ForeignKey(AddOn, on_delete=models.CASCADE, related_name='employee_addons')
    custom_price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, 
                   help_text="Use 0.00 to use addon.base_price")
    custom_duration = models.DurationField(null=True, blank=True)

    class Meta:
        app_label = 'services'
        constraints = [
            UniqueConstraint(fields=['business', 'employee', 'addon'], 
                            name='uniq_addon_per_employee_business'),
            CheckConstraint(
                check=Q(custom_price__gte=0),
                name="positive_custom_addon_price"
            ),
            CheckConstraint(
                check=Q(custom_duration__gt=timedelta(0)) | Q(custom_duration__isnull=True),
                name="positive_custom_addon_duration"
            )
        ]
        indexes = [
            models.Index(fields=['business', 'employee'], 
                        name='idx_employeeaddon_biz_emp',
                        condition=Q(is_active=True))
        ]
        verbose_name = 'Employee Add-On'
        verbose_name_plural = 'Employee Add-Ons'

    def __str__(self):
        return f"{self.employee.first_name} {self.employee.last_name} - {self.addon.name}"

    @property
    def price(self):
        """Return custom price if set (not zero), otherwise return price based on stylist level or addon base price"""
        if self.custom_price and self.custom_price > 0:
            return self.custom_price
        
        # If employee has a stylist level, check for level-specific pricing
        if self.employee.stylist_level:
            stylist_level_pricing = StylistLevelAddOn.objects.filter(
                business=self.business,
                addon=self.addon,
                stylist_level=self.employee.stylist_level,
                is_active=True,
                is_offered=True
            ).first()
            
            if stylist_level_pricing and stylist_level_pricing.price > 0:
                return stylist_level_pricing.price
                
        return self.addon.base_price
    
    @property
    def duration(self):
        """Return custom duration if set, otherwise return duration based on stylist level or addon base duration"""
        if self.custom_duration:
            return self.custom_duration
        
        # If employee has a stylist level, check for level-specific duration
        if self.employee.stylist_level:
            stylist_level_pricing = StylistLevelAddOn.objects.filter(
                business=self.business,
                addon=self.addon,
                stylist_level=self.employee.stylist_level,
                is_active=True,
                is_offered=True
            ).first()
            
            if stylist_level_pricing and stylist_level_pricing.duration:
                return stylist_level_pricing.duration
                
        return self.addon.base_duration
    
    @property
    def is_addon_offered(self):
        """Check if this add-on is offered for the employee's stylist level"""
        # If no stylist level, assume the add-on is offered
        if not self.employee.stylist_level:
            return True
            
        # Check if there's a StylistLevelAddOn record and if it's offered
        return StylistLevelAddOn.objects.filter(
            business=self.business,
            addon=self.addon,
            stylist_level=self.employee.stylist_level,
            is_active=True,
            is_offered=True
        ).exists()
    
    def clean(self):
        super().clean()
        # Validate employee belongs to the business
        if self.employee.business_id != self.business_id:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                {'employee': 'Employee must belong to the specified business.'}
            )
        
        # Validate add-on belongs to the business
        if self.addon.business_id != self.business_id:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                {'addon': 'Add-on must belong to the specified business.'}
            )
            
        # Validate the add-on is offered for this employee's stylist level
        if self.employee.stylist_level and not self.is_addon_offered:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                {'addon': f'This add-on is not offered for the "{self.employee.stylist_level.name}" stylist level.'}
            )
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)


class AddonSuggestionRule(SoftDeleteModel):
    """
    Model to define rules for suggesting add-ons to customers based on various criteria.
    These rules help automate add-on suggestions during appointment booking or service selection.
    """
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='addon_suggestion_rules')
    suggested_addon = models.ForeignKey(
        AddOn,
        on_delete=models.CASCADE,
        related_name='suggestion_rules',
        help_text='The add-on to suggest'
    )

    # Rule criteria
    new_client_only = models.BooleanField(
        default=False,
        help_text='Only suggest this add-on to new clients'
    )
    last_addon = models.ForeignKey(
        AddOn,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='triggers_suggestions',
        help_text='Suggest the add-on if customer previously had this add-on'
    )
    min_days_since = models.IntegerField(
        null=True,
        blank=True,
        help_text='Minimum days since last add-on (inclusive)'
    )
    max_days_since = models.IntegerField(
        null=True,
        blank=True,
        help_text='Maximum days since last add-on (exclusive)'
    )

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'services'
        verbose_name = 'Add-on Suggestion Rule'
        verbose_name_plural = 'Add-on Suggestion Rules'
        ordering = ['business', 'suggested_addon__name']
        constraints = [
            # Ensure min_days_since is not negative
            CheckConstraint(
                check=Q(min_days_since__gte=0) | Q(min_days_since__isnull=True),
                name='positive_min_days_since'
            ),
            # Ensure max_days_since is positive
            CheckConstraint(
                check=Q(max_days_since__gt=0) | Q(max_days_since__isnull=True),
                name='positive_max_days_since'
            ),
            # Ensure min_days_since is less than max_days_since when both are set
            CheckConstraint(
                check=Q(min_days_since__lt=models.F('max_days_since')) |
                      Q(min_days_since__isnull=True) |
                      Q(max_days_since__isnull=True),
                name='min_less_than_max_days'
            ),
            # Ensure at least one rule criteria is specified
            CheckConstraint(
                check=Q(new_client_only=True) | Q(last_addon__isnull=False) |
                      (Q(min_days_since__isnull=False) | Q(max_days_since__isnull=False)),
                name='at_least_one_criteria'
            )
        ]
        indexes = [
            models.Index(
                fields=['business', 'suggested_addon'],
                name='idx_addon_sugg_biz_addon',
                condition=Q(is_active=True)
            ),
            models.Index(
                fields=['business', 'last_addon'],
                name='idx_addon_sugg_last_addon',
                condition=Q(is_active=True, last_addon__isnull=False)
            )
        ]

    def __str__(self):
        criteria_parts = []
        if self.new_client_only:
            criteria_parts.append("new clients only")
        if self.last_addon:
            days_info = ""
            if self.min_days_since is not None or self.max_days_since is not None:
                if self.min_days_since is not None and self.max_days_since is not None:
                    days_info = f" ({self.min_days_since}-{self.max_days_since-1} days ago)"
                elif self.min_days_since is not None:
                    days_info = f" ({self.min_days_since}+ days ago)"
                elif self.max_days_since is not None:
                    days_info = f" (<{self.max_days_since} days ago)"
            criteria_parts.append(f"after {self.last_addon.name}{days_info}")

        criteria_str = " & ".join(criteria_parts) if criteria_parts else "always"
        return f"Suggest {self.suggested_addon.name} - {criteria_str} ({self.business.name})"

    def clean(self):
        super().clean()
        from django.core.exceptions import ValidationError

        # Validate suggested_addon belongs to the business
        if self.suggested_addon and self.suggested_addon.business_id != self.business_id:
            raise ValidationError({
                'suggested_addon': 'Suggested add-on must belong to the specified business.'
            })

        # Validate last_addon belongs to the business (if specified)
        if self.last_addon and self.last_addon.business_id != self.business_id:
            raise ValidationError({
                'last_addon': 'Last add-on must belong to the specified business.'
            })

        # Validate day range logic
        if (self.min_days_since is not None and self.max_days_since is not None and
            self.min_days_since >= self.max_days_since):
            raise ValidationError({
                'max_days_since': 'Maximum days must be greater than minimum days.'
            })

        # Validate that at least one criteria is specified
        has_time_criteria = self.min_days_since is not None or self.max_days_since is not None
        if not self.new_client_only and not self.last_addon and not has_time_criteria:
            raise ValidationError(
                'At least one rule criteria must be specified (new client only, last add-on, or time range).'
            )

        # Day ranges can now be used independently for time-based suggestions

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)


class LashServiceSuggestionRule(SoftDeleteModel):
    """
    Model to define rules for suggesting next lash services based on the last service performed.
    This helps automate service suggestions for follow-up appointments based on timing.
    """
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='lash_service_suggestion_rules')
    last_service_style_group = models.ForeignKey(
        StyleGroup,
        on_delete=models.CASCADE,
        related_name='triggers_service_suggestions',
        help_text='Style group ID of the last service for efficient querying'
    )
    last_service_style_group_name = models.CharField(
        max_length=50,
        help_text='Denormalized style group name for display and filtering (e.g., Classic, Styling, Volume, Real Mink)'
    )
    next_service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        related_name='suggested_as_next_service',
        help_text='The service to suggest next'
    )
    min_days_since = models.IntegerField(
        help_text='Minimum days since last service (inclusive)'
    )
    max_days_since = models.IntegerField(
        help_text='Maximum days since last service (exclusive)'
    )

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'services'
        verbose_name = 'Lash Service Suggestion Rule'
        verbose_name_plural = 'Lash Service Suggestion Rules'
        ordering = ['business', 'last_service_style_group__name', 'min_days_since']
        constraints = [
            # Ensure min_days_since is not negative
            CheckConstraint(
                check=Q(min_days_since__gte=0),
                name='positive_min_days_since_service'
            ),
            # Ensure max_days_since is positive
            CheckConstraint(
                check=Q(max_days_since__gt=0),
                name='positive_max_days_since_service'
            ),
            # Ensure min_days_since is less than max_days_since
            CheckConstraint(
                check=Q(min_days_since__lt=models.F('max_days_since')),
                name='min_less_than_max_days_service'
            ),
            # Composite primary key constraint for style group rules
            UniqueConstraint(
                fields=['business', 'last_service_style_group', 'next_service', 'min_days_since'],
                name='unique_style_group_suggestion_rule'
            )
        ]
        indexes = [
            models.Index(
                fields=['business', 'last_service_style_group'],
                name='idx_service_sugg_biz_style',
                condition=Q(is_active=True)
            ),
            models.Index(
                fields=['business', 'min_days_since', 'max_days_since'],
                name='idx_service_sugg_timing',
                condition=Q(is_active=True)
            )
        ]

    def __str__(self):
        last_part = f"After any {self.last_service_style_group.name} service"

        return (f"{last_part} → "
                f"suggest {self.next_service.short_name or self.next_service.name} "
                f"({self.min_days_since}-{self.max_days_since-1} days) - {self.business.name}")

    @property
    def last_service_short_name(self):
        """Get the short name of the style group"""
        return f"Any {self.last_service_style_group.name} service"

    @property
    def next_service_short_name(self):
        """Get the short name of the next service"""
        return self.next_service.short_name or self.next_service.name

    def clean(self):
        super().clean()
        from django.core.exceptions import ValidationError

        # last_service_style_group is required (no validation needed since it's not nullable)

        # Validate next_service belongs to the business
        if self.next_service and self.next_service.business_id != self.business_id:
            raise ValidationError({
                'next_service': 'Next service must belong to the specified business.'
            })

        # Validate day range logic
        if self.min_days_since >= self.max_days_since:
            raise ValidationError({
                'max_days_since': 'Maximum days must be greater than minimum days.'
            })

        # Allow self-referencing services (e.g., refill after refill)
        # This is common for lash refill services

    def save(self, *args, **kwargs):
        # Auto-populate the denormalized style group name
        if self.last_service_style_group:
            self.last_service_style_group_name = self.last_service_style_group.name

        self.clean()
        super().save(*args, **kwargs)
