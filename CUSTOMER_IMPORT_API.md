# Customer Import API Documentation

## Overview

The customer import system allows businesses to import customer data from Excel files exported from various salon software platforms. The system provides intelligent field mapping, data validation, and comprehensive error reporting.

## API Endpoints

### 1. Field Mapping Suggestions
**POST** `/api/v1/customers/import/field-mapping-suggestions/`

Get automatic field mapping suggestions based on available columns.

**Request Body:**
```json
{
  "columns": [
    "First Name",
    "Last Name", 
    "Email",
    "Mobile",
    "Points Earned",
    "Tags",
    "Customer Since",
    "Last Visited"
  ]
}
```

**Response:**
```json
{
  "auto_mapping": {
    "first_name": "First Name",
    "last_name": "Last Name",
    "email": "Email",
    "mobile": "Mobile",
    "points_earned": "Points Earned",
    "tags": "Tags",
    "customer_since": "Customer Since",
    "last_visited": "Last Visited"
  },
  "validation": {
    "is_valid": true,
    "missing_required": [],
    "missing_recommended": [],
    "invalid_mappings": [],
    "mapped_fields": 8,
    "total_available": 8
  },
  "suggestions": {
    "birthdate": ["Date of Birth", "DOB"],
    "address": ["Street Address", "Address"]
  },
  "required_fields": ["first_name", "last_name", "email"],
  "recommended_fields": ["mobile", "points_earned", "tags", "customer_since"]
}
```

### 2. Start Customer Import
**POST** `/api/v1/customers/import/`

Start the customer import process.

**Request Body:**
```json
{
  "fileName": "customers_export.xlsx",
  "fileSize": 156789,
  "totalRows": 245,
  "fieldMappings": {
    "first_name": "First Name",
    "last_name": "Last Name",
    "email": "Email",
    "mobile": "Mobile",
    "points_earned": "Points Earned",
    "tags": "Tags"
  },
  "customers": [
    {
      "First Name": "John",
      "Last Name": "Doe",
      "Email": "<EMAIL>",
      "Mobile": "************",
      "Points Earned": "150",
      "Tags": "VIP, Regular"
    }
  ],
  "skipDuplicates": true,
  "updateExisting": false,
  "validateEmails": true
}
```

**Response:**
```json
{
  "jobId": "123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "message": "Import started successfully"
}
```

### 3. Get Import Status
**GET** `/api/v1/customers/import/status/{job_id}/`

Get the current status of an import job.

**Response:**
```json
{
  "jobId": "123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "progress": 100,
  "processedRows": 245,
  "totalRows": 245
}
```

### 4. Get Import Results
**GET** `/api/v1/customers/import/results/{job_id}/`

Get the final results of a completed import job.

**Response:**
```json
{
  "jobId": "123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "imported_count": 240,
  "error_count": 5,
  "total_count": 245,
  "warnings": [
    {
      "row": 15,
      "email": "<EMAIL>",
      "message": "Customer already exists, skipping"
    }
  ],
  "errors": [
    {
      "row": 23,
      "field": "email",
      "value": "invalid-email",
      "message": "Invalid email format"
    }
  ],
  "successful": [
    {
      "email": "<EMAIL>",
      "name": "John Doe",
      "id": 1234
    }
  ]
}
```

## Supported Field Mappings

### Required Fields
- `first_name` - Customer's first name
- `last_name` - Customer's last name  
- `email` - Customer's email address (must be unique)

### Recommended Fields
- `mobile` - Primary phone number
- `points_earned` - Loyalty points (integer)
- `tags` - Comma-separated tags/categories
- `customer_since` - Registration date

### Additional Supported Fields
- `phone` - Day/home phone
- `phone_night` - Evening phone
- `credit_card` - Whether customer has card on file (boolean)
- `birthdate` - Date of birth
- `gender` - Customer gender
- `membership` - Membership type/level
- `referred_by` - Who referred this customer
- `online_booking` - Online booking preference
- `apt_suite` - Apartment/suite number
- `address` - Street address
- `city` - City
- `state` - State/province
- `zip` - ZIP/postal code
- `appointments_booked` - Total appointments
- `classes_booked` - Total classes
- `check_ins` - Total check-ins
- `amount_paid` - Total amount spent
- `no_shows_cancellations` - No shows count
- `employee_seen` - Preferred staff member
- `last_visited` - Last visit date

## Data Processing

### Automatic Data Cleaning
The system automatically cleans and formats data:

- **Names**: Converted to title case
- **Emails**: Converted to lowercase
- **Phone Numbers**: Formatted with country codes
- **Boolean Fields**: Converted from various text formats
- **Tags**: Split by commas and trimmed
- **Numbers**: Parsed and validated

### Data Storage
- **Core info** → User model (name, email, phone)
- **Profile info** → CustomerProfile model (card on file)
- **Business relationship** → BusinessCustomer model (loyalty points, preferences, tags)
- **Additional data** → Stored in structured notes field

### Duplicate Handling
- **Email uniqueness**: System prevents duplicate emails
- **Existing customers**: Automatically skipped with warnings
- **Data preservation**: All original data preserved in notes

## Frontend Integration Example

```javascript
// 1. Get field mapping suggestions
const suggestions = await importApi.getFieldMappingSuggestions({
  columns: availableColumns
});

// 2. Start import with user's field mappings
const importJob = await importApi.startCustomerImport({
  fileName: file.name,
  fileSize: file.size,
  totalRows: customers.length,
  fieldMappings: userSelectedMappings,
  customers: customerData,
  skipDuplicates: true
});

// 3. Poll for status updates
const pollStatus = async (jobId) => {
  const status = await importApi.getImportStatus(jobId);
  if (status.status === 'completed') {
    const results = await importApi.getImportResults(jobId);
    // Handle results...
  }
};
```

## Error Handling

### Common Errors
- **Missing required fields**: Email, first name, or last name not mapped
- **Invalid email format**: Email doesn't match standard format
- **Duplicate customers**: Customer with same email already exists
- **Invalid field mappings**: Mapped to non-existent columns

### Error Response Format
```json
{
  "row": 15,
  "field": "email", 
  "value": "invalid-data",
  "message": "Human-readable error description"
}
```

## Security & Permissions

- **Authentication required**: All endpoints require valid JWT token
- **Business scope**: Users can only import to their own business
- **Data isolation**: Import jobs are isolated per business
- **Rate limiting**: Prevents abuse of import endpoints

## Performance Notes

- **Synchronous processing**: Currently processes immediately
- **Transaction safety**: All-or-nothing database transactions
- **Memory efficient**: Processes data in batches
- **Caching**: Results cached for 1 hour for retrieval

## Future Enhancements

- **Async processing**: Large files processed with Celery
- **Progress streaming**: Real-time progress updates via WebSockets
- **File validation**: Pre-upload file format validation
- **Import templates**: Predefined mappings for popular software 