{% extends "base.html" %}
{% load static %}

{% block title %}Profile - ChatBook{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-100">
    <div class="py-10">
        <header>
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h1 class="text-3xl font-bold leading-tight text-gray-900">
                    Account Settings
                </h1>
            </div>
        </header>
        <main>
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="space-y-6 mt-6">
                    <!-- Personal Information -->
                    <div class="bg-white shadow sm:rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                Personal Information
                            </h3>
                            <form action="{% url 'profile' %}" method="POST" class="mt-5 space-y-4">
                                {% csrf_token %}
                                <div class="grid grid-cols-6 gap-6">
                                    <div class="col-span-6 sm:col-span-3">
                                        <label for="first_name" class="block text-sm font-medium text-gray-700">First name</label>
                                        <input type="text" name="first_name" id="first_name" value="{{ user.first_name }}"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    </div>

                                    <div class="col-span-6 sm:col-span-3">
                                        <label for="last_name" class="block text-sm font-medium text-gray-700">Last name</label>
                                        <input type="text" name="last_name" id="last_name" value="{{ user.last_name }}"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    </div>

                                    <div class="col-span-6 sm:col-span-3">
                                        <label for="email" class="block text-sm font-medium text-gray-700">Email address</label>
                                        <input type="email" name="email" id="email" value="{{ user.email }}"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    </div>

                                    <div class="col-span-6 sm:col-span-3">
                                        <label for="phone_number" class="block text-sm font-medium text-gray-700">Phone number</label>
                                        <input type="tel" name="phone_number" id="phone_number" value="{{ user.phone_number }}"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    </div>
                                </div>

                                <div class="flex justify-end">
                                    <button type="submit"
                                        class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Save changes
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div class="bg-white shadow sm:rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                Security Settings
                            </h3>
                            <div class="mt-2 max-w-xl text-sm text-gray-500">
                                <p>Manage your account security and authentication methods.</p>
                            </div>
                            <div class="mt-5 space-y-4">
                                <div>
                                    <a href="{% url 'password_change' %}"
                                        class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Change Password
                                    </a>
                                </div>
                                <div>
                                    <a href="{% url 'mfa_setup' %}"
                                        class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        {% if user.mfa_enabled %}
                                        Configure Two-Factor Authentication
                                        {% else %}
                                        Enable Two-Factor Authentication
                                        {% endif %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Connected Accounts -->
                    <div class="bg-white shadow sm:rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                Connected Accounts
                            </h3>
                            <div class="mt-2 max-w-xl text-sm text-gray-500">
                                <p>Connect your account with social providers for easier login.</p>
                            </div>
                            <div class="mt-5 space-y-4">
                                {% for provider in social_providers %}
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img src="{% static 'images/'|add:provider.id|add:'.svg' %}" alt="{{ provider.name }}"
                                            class="h-6 w-6">
                                        <span class="ml-3 text-sm font-medium text-gray-900">{{ provider.name }}</span>
                                    </div>
                                    {% if provider.connected %}
                                    <button type="button" onclick="disconnectSocialAccount('{{ provider.id }}')"
                                        class="ml-3 inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Disconnect
                                    </button>
                                    {% else %}
                                    <a href="/accounts/{{ provider.id }}/login/"
                                        class="ml-3 inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Connect
                                    </a>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>
{% endblock %}

{% block extra_body %}
<script>
    async function disconnectSocialAccount(provider) {
        if (!confirm('Are you sure you want to disconnect this account?')) {
            return;
        }
        try {
            const response = await fetch(`/accounts/social/disconnect/${provider}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            });
            if (response.ok) {
                window.location.reload();
            } else {
                alert('Failed to disconnect account');
            }
        } catch (error) {
            alert('Failed to disconnect account');
        }
    }
</script>
{% endblock %} 