{% extends "base.html" %}
{% load static %}

{% block title %}MFA Setup - ChatBook{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Two-Factor Authentication Setup
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Add an extra layer of security to your account
            </p>
        </div>

        {% if messages %}
        <div class="rounded-md bg-green-50 p-4">
            <div class="flex">
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-green-800">
                        {% for message in messages %}
                        {{ message }}
                        {% endfor %}
                    </h3>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="bg-white shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    {% if user.mfa_enabled %}
                    MFA is currently enabled
                    {% else %}
                    Enable Two-Factor Authentication
                    {% endif %}
                </h3>
                <div class="mt-2 max-w-xl text-sm text-gray-500">
                    <p>
                        {% if user.mfa_enabled %}
                        Your account is protected with an additional layer of security. You'll need to enter a verification code when signing in.
                        {% else %}
                        When you enable two-factor authentication, you'll be asked to enter a verification code in addition to your password when signing in.
                        {% endif %}
                    </p>
                </div>
                <form action="{% url 'mfa_setup' %}" method="POST" class="mt-5">
                    {% csrf_token %}
                    {% if user.mfa_enabled %}
                    <input type="hidden" name="enable" value="false">
                    <button type="submit"
                        class="inline-flex items-center justify-center px-4 py-2 border border-transparent font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:text-sm">
                        Disable MFA
                    </button>
                    {% else %}
                    <div class="space-y-4">
                        <div>
                            <label for="method" class="block text-sm font-medium text-gray-700">
                                Verification Method
                            </label>
                            <select id="method" name="method"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="email" {% if user.preferred_mfa_method == 'email' %}selected{% endif %}>
                                    Email ({{ user.email }})
                                </option>
                                <option value="sms" {% if user.preferred_mfa_method == 'sms' %}selected{% endif %}>
                                    SMS ({{ user.phone_number }})
                                </option>
                            </select>
                        </div>
                        <input type="hidden" name="enable" value="true">
                        <button type="submit"
                            class="inline-flex items-center justify-center px-4 py-2 border border-transparent font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:text-sm">
                            Enable MFA
                        </button>
                    </div>
                    {% endif %}
                </form>
            </div>
        </div>

        <div class="mt-4 text-center">
            <a href="{% url 'profile' %}" class="font-medium text-indigo-600 hover:text-indigo-500">
                Back to profile
            </a>
        </div>
    </div>
</div>
{% endblock %} 