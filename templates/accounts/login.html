{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}Login - ChatBook{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow">
        <div>
            <h2 class="text-center text-3xl font-extrabold text-gray-900">
                Sign in to your account
            </h2>
        </div>

        {% if messages %}
        <div class="rounded-md bg-red-50 p-4">
            <div class="flex">
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">
                        {% for message in messages %}
                        {{ message }}
                        {% endfor %}
                    </h3>
                </div>
            </div>
        </div>
        {% endif %}

        <form class="mt-8 space-y-6" action="/auth/login/" method="POST">
            {% csrf_token %}
            <div class="space-y-4">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-1">
                        Phone number or email <span class="text-red-500">*</span>
                    </label>
                    <input id="username" name="username" type="text" required
                        class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        placeholder="Phone number or email">
                </div>
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                        Password <span class="text-red-500">*</span>
                    </label>
                    <input id="password" name="password" type="password" required
                        class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        placeholder="••••••••">
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="remember-me" name="remember-me" type="checkbox"
                        class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                        Remember me
                    </label>
                </div>

                <div class="text-sm">
                    <a href="#" id="request-otp" class="font-medium text-indigo-600 hover:text-indigo-500">
                        Login with OTP
                    </a>
                </div>
            </div>

            <div>
                <button type="submit"
                    class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Sign in
                </button>
            </div>
        </form>

        <!-- Business Registration Link -->
        <div class="mt-6 text-center">
            <p class="text-sm text-gray-600 mb-2">Are you a business owner?</p>
            <a href="{% url 'business:register' %}" 
               class="w-full inline-block text-center py-2 px-4 border border-indigo-600 rounded-md shadow-sm text-sm font-medium text-indigo-600 hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Register your Business
            </a>
        </div>

        <div class="mt-6">
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-white text-gray-500">
                        Or continue with
                    </span>
                </div>
            </div>

            <div class="mt-6 flex justify-center">
                <a href="/accounts/google/login/"
                    class="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="flex items-center">
                        <img src="{% static 'images/google.svg' %}" alt="Google" class="h-5 w-5 mr-2">
                        <span>Sign in with Google</span>
                    </span>
                </a>
            </div>
        </div>

        <div class="text-sm text-center text-gray-600">
            Don't have an account?
            <a href="/auth/signup/" class="font-medium text-indigo-600 hover:text-indigo-500">
                Sign up
            </a>
        </div>
    </div>
</div>

<!-- OTP Modal -->
<div id="otp-modal" class="hidden fixed z-10 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
            <div>
                <div class="mt-3 text-center sm:mt-5">
                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                        Enter your phone number
                    </h3>
                    <div class="mt-2">
                        <input type="tel" id="phone-number" name="phone_number"
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="+1234567890">
                    </div>
                </div>
            </div>
            <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                <button type="button" id="request-otp-submit"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:col-start-2 sm:text-sm">
                    Request OTP
                </button>
                <button type="button" id="close-modal"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:col-start-1 sm:text-sm">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_body %}
<script>
    // OTP Modal functionality
    const otpModal = document.getElementById('otp-modal');
    const requestOtpButton = document.getElementById('request-otp');
    const closeModalButton = document.getElementById('close-modal');
    const requestOtpSubmitButton = document.getElementById('request-otp-submit');
    const phoneNumberInput = document.getElementById('phone-number');

    requestOtpButton.addEventListener('click', (e) => {
        e.preventDefault();
        otpModal.classList.remove('hidden');
    });

    closeModalButton.addEventListener('click', () => {
        otpModal.classList.add('hidden');
    });

    requestOtpSubmitButton.addEventListener('click', async () => {
        const phoneNumber = phoneNumberInput.value;
        try {
            const response = await fetch('/api/v1/auth/request-otp/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({ phone_number: phoneNumber })
            });
            const data = await response.json();
            if (response.ok) {
                // Hide phone input, show OTP input
                otpModal.querySelector('.mt-2').innerHTML = `
                    <input type="text" id="otp" name="otp"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        placeholder="Enter OTP">
                `;
                otpModal.querySelector('#modal-title').textContent = 'Enter OTP';
                requestOtpSubmitButton.textContent = 'Verify OTP';
                requestOtpSubmitButton.removeEventListener('click', arguments.callee);
                requestOtpSubmitButton.addEventListener('click', verifyOtp);
            } else {
                alert(data.error || 'Failed to send OTP');
            }
        } catch (error) {
            alert('Failed to send OTP');
        }
    });

    async function verifyOtp() {
        const otp = document.getElementById('otp').value;
        try {
            const response = await fetch('/api/v1/auth/verify-otp/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    phone_number: phoneNumberInput.value,
                    otp: otp
                })
            });
            const data = await response.json();
            if (response.ok) {
                window.location.href = '/';
            } else {
                alert(data.error || 'Invalid OTP');
            }
        } catch (error) {
            alert('Failed to verify OTP');
        }
    }
</script>
{% endblock %} 