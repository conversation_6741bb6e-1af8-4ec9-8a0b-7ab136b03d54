{% extends "base.html" %}
{% load static %}

{% block title %}Verify MFA - ChatBook{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Two-Factor Authentication
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Please enter the verification code sent to your {{ request.user.preferred_mfa_method }}.
            </p>
        </div>

        {% if messages %}
        <div class="rounded-md bg-red-50 p-4">
            <div class="flex">
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">
                        {% for message in messages %}
                        {{ message }}
                        {% endfor %}
                    </h3>
                </div>
            </div>
        </div>
        {% endif %}

        <form class="mt-8 space-y-6" action="{% url 'mfa_verify' %}" method="POST">
            {% csrf_token %}
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="code" class="sr-only">Verification code</label>
                    <input id="code" name="code" type="text" required
                        class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                        placeholder="Enter verification code">
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="trust-device" name="trust_device" type="checkbox"
                        class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <label for="trust-device" class="ml-2 block text-sm text-gray-900">
                        Trust this device for 30 days
                    </label>
                </div>
            </div>

            <div>
                <button type="submit"
                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Verify
                </button>
            </div>
        </form>

        <div class="text-center">
            <a href="{% url 'login' %}" class="font-medium text-indigo-600 hover:text-indigo-500">
                Back to login
            </a>
        </div>
    </div>
</div>
{% endblock %} 