{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" href="{% static 'admin/css/custom_admin.css' %}">
{% endblock %}

{% block bodyclass %}login{% endblock %}

{% block usertools %}{% endblock %}

{% block nav-global %}{% endblock %}

{% block nav-sidebar %}{% endblock %}

{% block content_title %}{% endblock %}

{% block breadcrumbs %}{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        {% if form.errors and not form.non_field_errors %}
        <div class="error-message">
            {% if form.errors.items|length == 1 %}{% translate "Please correct the error below." %}{% else %}{% translate "Please correct the errors below." %}{% endif %}
        </div>
        {% endif %}

        {% if form.non_field_errors %}
        {% for error in form.non_field_errors %}
        <div class="error-message">
            {{ error }}
        </div>
        {% endfor %}
        {% endif %}

        <form action="{{ app_path }}" method="post" id="login-form">{% csrf_token %}
            <div class="form-row">
                {{ form.username.errors }}
                <label for="id_username" class="required">{{ form.username.label }}</label>
                {{ form.username }}
            </div>
            <div class="form-row">
                {{ form.password.errors }}
                <label for="id_password" class="required">{{ form.password.label }}</label>
                {{ form.password }}
                <input type="hidden" name="next" value="{{ next }}">
            </div>
            <div class="submit-row">
                <input type="submit" value="{% translate 'Log in' %}">
            </div>
            <div class="password-reset-link">
                <a href="{% url 'admin_password_reset' %}">{% translate 'Forgotten your password?' %}</a>
            </div>
        </form>
    </div>
</div>
{% endblock %} 