{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static 'admin/css/custom_admin.css' %}">
{% endblock %}

{% block extrahead %}
{{ block.super }}
<script type="text/javascript" src="{% static 'admin/js/sidebar_position.js' %}"></script>
{% endblock %}

{% block branding %}
<h1 id="site-name">
    <a href="{% url 'admin:index' %}">
        {{ site_header|default:_('ChatBook Administration') }}
    </a>
</h1>
{% endblock %}

{% block nav-global %}{% endblock %}

{% block content %}
<div id="content-main">
    {% if app_list %}
        {% for app in app_list %}
            <div class="app-{{ app.app_label }} module">
                <table>
                    <caption>
                        <a href="{{ app.app_url }}" class="section">{{ app.name }}</a>
                    </caption>
                    {% for model in app.models %}
                        <tr class="model-{{ model.object_name|lower }}">
                            {% if model.admin_url %}
                                <th scope="row"><a href="{{ model.admin_url }}">{{ model.name }}</a></th>
                            {% else %}
                                <th scope="row">{{ model.name }}</th>
                            {% endif %}

                            {% if model.add_url %}
                                <td><a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a></td>
                            {% else %}
                                <td>&nbsp;</td>
                            {% endif %}

                            {% if model.admin_url %}
                                <td><a href="{{ model.admin_url }}" class="changelink">{% trans 'Change' %}</a></td>
                            {% else %}
                                <td>&nbsp;</td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </table>
            </div>
        {% endfor %}
    {% else %}
        <p>{% trans "You don't have permission to view or edit anything." %}</p>
    {% endif %}
</div>
{% endblock %} 