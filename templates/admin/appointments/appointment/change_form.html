{% extends "admin/change_form.html" %}
{% load i18n admin_urls %}

{% block content %}
    {% if custom_validation_error %}
        <div class="alert alert-error" style="background-color: #f8d7da; color: #721c24; padding: 12px; margin-bottom: 20px; border: 1px solid #f5c6cb; border-radius: 4px;">
            <strong>Error:</strong> {{ custom_validation_error }}
        </div>
    {% endif %}
    
    {% if adminform.form.non_field_errors %}
        <div class="alert alert-error" style="background-color: #f8d7da; color: #721c24; padding: 12px; margin-bottom: 20px; border: 1px solid #f5c6cb; border-radius: 4px;">
            <strong>Error:</strong>
            <ul>
                {% for error in adminform.form.non_field_errors %}
                    <li>{{ error }}</li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}
    
    <!-- Source field fix -->
    <input type="hidden" name="source" id="id_source_fix" value="admin">
    
    {{ block.super }}
{% endblock %}

{% block after_related_objects %}
    {{ block.super }}
    <script type="text/javascript">
        (function($) {
            $(document).ready(function() {
                // Fix the source field issue by ensuring it has a value
                $('form').submit(function() {
                    // Set the source field value if it's empty
                    var sourceField = $('input[name="source"]');
                    if (sourceField.length > 0) {
                        if (!sourceField.val()) {
                            sourceField.val('admin');
                        }
                    } else {
                        // If the field doesn't exist, add it
                        $(this).append('<input type="hidden" name="source" value="admin">');
                    }
                });
                
                // Add a validation check before form submission
                $('form#appointmentservice_set-group input[type="submit"]').click(function(e) {
                    var serviceSelected = false;
                    $('select.service-select').each(function() {
                        if ($(this).val()) {
                            serviceSelected = true;
                            return false; // Break the loop if we found at least one service
                        }
                    });
                    
                    if (!serviceSelected) {
                        e.preventDefault();
                        alert('Please select at least one service for this appointment');
                        // Highlight the service section
                        $('div.inline-group h2:contains("Appointment Services")').css('background-color', '#f8d7da');
                        // Focus on the first service select
                        $('select.service-select:first').focus();
                        return false;
                    }
                });
                
                // Highlight required fields
                $('label.required').closest('.form-row').addClass('required-field');
                $('style').append('.required-field label { font-weight: bold; } .required-field .errorlist { color: #ba2121; font-weight: bold; }');
            });
        })(django.jQuery);
    </script>
{% endblock %} 