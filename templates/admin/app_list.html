{% load i18n static %}

{% if app_list %}
  {% for app in app_list %}
    <div class="app-section">
      <h2>{{ app.name }}</h2>
      <div class="model-list">
        {% for model in app.models %}
          <div class="model-item">
            <div class="model-name">
              {% if model.admin_url %}
                <a href="{{ model.admin_url }}">{{ model.name }}</a>
              {% else %}
                {{ model.name }}
              {% endif %}
            </div>
            <div class="model-actions">
              {% if model.add_url %}
                <a href="{{ model.add_url }}" class="action-button add-button">
                  <span class="icon">+</span>
                  {% trans 'Add' %}
                </a>
              {% endif %}
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  {% endfor %}
{% endif %}

<style>
.app-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 24px;
    overflow: hidden;
}

.app-section h2 {
    background: var(--primary);
    color: white;
    margin: 0;
    padding: 16px 20px;
    font-size: 18px;
    font-weight: 500;
}

.model-list {
    padding: 12px;
}

.model-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 8px;
    border-bottom: 1px solid #eee;
}

.model-item:last-child {
    border-bottom: none;
}

.model-name a {
    color: var(--text);
    text-decoration: none;
    font-weight: 500;
}

.model-name a:hover {
    color: var(--primary);
}

.model-actions {
    display: flex;
    gap: 8px;
}

.action-button {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.2s;
}

.add-button {
    background: var(--primary);
    color: white;
}

.add-button:hover {
    background: #1557b0;
    text-decoration: none;
}

.icon {
    font-size: 16px;
    line-height: 1;
}
</style> 