from django.shortcuts import render, redirect
from django.views.generic import FormView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.contrib.auth import login
from django.db import transaction
from django.utils.translation import gettext_lazy as _

from .forms import BusinessRegistrationForm
from .models import Business

class BusinessRegistrationView(FormView):
    """
    View for self-service business registration. 
    Creates a new business and admin user.
    """
    template_name = 'business/registration.html'
    form_class = BusinessRegistrationForm
    success_url = reverse_lazy('business:registration_success')
    
    def form_valid(self, form):
        try:
            with transaction.atomic():
                # Create the business and user
                business, user = form.save()
                
                # Log the user in
                login(self.request, user)
                
                messages.success(self.request, _('Your business has been registered successfully!'))
                return super().form_valid(form)
        except Exception as e:
            messages.error(self.request, _('An error occurred: {0}').format(str(e)))
            return self.form_invalid(form)

class BusinessRegistrationSuccessView(TemplateView):
    """
    Success page after business registration.
    """
    template_name = 'business/registration_success.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['next_steps'] = [
            {'title': _('Complete your profile'), 'url': reverse_lazy('business:settings')},
            {'title': _('Add services'), 'url': reverse_lazy('services:create')},
            {'title': _('Invite employees'), 'url': reverse_lazy('employees:invite')},
        ]
        return context 