{% extends "business/base.html" %}
{% load static %}
{% load i18n %}

{% block sub_title %}{% trans "Registration Successful" %}{% endblock %}

{% block business_title %}{% trans "Registration Successful" %}{% endblock %}
{% block business_description %}{% trans "Your business has been successfully registered" %}{% endblock %}

{% block business_css %}
<style>
    .success-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        text-align: center;
    }
    
    .success-icon {
        font-size: 5rem;
        color: #28a745;
        margin-bottom: 2rem;
    }
    
    .next-steps {
        margin-top: 3rem;
        padding: 2rem;
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        text-align: left;
    }
    
    .step-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding: 1rem;
        border-radius: 0.25rem;
        background-color: white;
        transition: all 0.2s;
    }
    
    .step-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .step-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background-color: #007bff;
        color: white;
        border-radius: 50%;
        margin-right: 1rem;
        font-weight: bold;
    }
    
    .step-content {
        flex: 1;
    }
    
    .step-title {
        font-weight: bold;
        margin-bottom: 0.25rem;
    }
</style>
{% endblock %}

{% block business_content %}
<div class="success-container">
    <div class="success-icon">
        <i class="fas fa-check-circle"></i>
    </div>
    
    <h1>{% trans "Congratulations!" %}</h1>
    <p class="lead">{% trans "Your business has been successfully registered." %}</p>
    
    <div class="mt-4">
        <p>{% trans "Your account has been created and you are now logged in as the business administrator." %}</p>
        <p>{% trans "Now it's time to set up your business and start accepting appointments!" %}</p>
    </div>
    
    <div class="next-steps">
        <h3 class="mb-4">{% trans "Next Steps" %}</h3>
        
        {% for i, step in next_steps|enumerate %}
        <a href="{{ step.url }}" class="step-item text-decoration-none">
            <div class="step-number">{{ forloop.counter }}</div>
            <div class="step-content">
                <div class="step-title">{{ step.title }}</div>
            </div>
            <div class="step-arrow">
                <i class="fas fa-chevron-right"></i>
            </div>
        </a>
        {% endfor %}
    </div>
    
    <div class="mt-5">
        <a href="{% url 'dashboard:index' %}" class="btn btn-primary btn-lg">{% trans "Go to Dashboard" %}</a>
    </div>
</div>
{% endblock %} 