{% extends "business/base.html" %}
{% load static %}
{% load i18n %}

{% block sub_title %}{% trans "Register Your Business" %}{% endblock %}

{% block business_title %}{% trans "Register Your Business" %}{% endblock %}
{% block business_description %}{% trans "Create your account and set up your business in minutes" %}{% endblock %}

{% block business_css %}
<style>
    .registration-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }
    
    .field-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        border-radius: 0.5rem;
        background-color: #f8f9fa;
    }
    
    .section-title {
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-check {
        margin-top: 1.5rem;
    }
</style>
{% endblock %}

{% block business_content %}
<div class="registration-container">
    {% if messages %}
    <div class="message-container">
        {% for message in messages %}
            <div class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %}" role="alert">
                {{ message }}
            </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <form method="post" class="registration-form">
        {% csrf_token %}
        
        {# Business Information Section #}
        <div class="field-section">
            <h3 class="section-title">{% trans "Business Information" %}</h3>
            
            <div class="form-group">
                <label for="{{ form.business_name.id_for_label }}">{{ form.business_name.label }}</label>
                {{ form.business_name }}
                {% if form.business_name.errors %}
                    <div class="invalid-feedback d-block">{{ form.business_name.errors }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="{{ form.business_description.id_for_label }}">{{ form.business_description.label }}</label>
                {{ form.business_description }}
                {% if form.business_description.errors %}
                    <div class="invalid-feedback d-block">{{ form.business_description.errors }}</div>
                {% endif %}
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.business_phone.id_for_label }}">{{ form.business_phone.label }}</label>
                        {{ form.business_phone }}
                        {% if form.business_phone.errors %}
                            <div class="invalid-feedback d-block">{{ form.business_phone.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.business_email.id_for_label }}">{{ form.business_email.label }}</label>
                        {{ form.business_email }}
                        {% if form.business_email.errors %}
                            <div class="invalid-feedback d-block">{{ form.business_email.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="{{ form.business_website.id_for_label }}">{{ form.business_website.label }}</label>
                {{ form.business_website }}
                {% if form.business_website.errors %}
                    <div class="invalid-feedback d-block">{{ form.business_website.errors }}</div>
                {% endif %}
            </div>
        </div>
        
        {# Your Information Section #}
        <div class="field-section">
            <h3 class="section-title">{% trans "Your Information" %}</h3>
            <p class="text-muted mb-4">{% trans "You'll be the administrator of the business account" %}</p>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.first_name.id_for_label }}">{{ form.first_name.label }}</label>
                        {{ form.first_name }}
                        {% if form.first_name.errors %}
                            <div class="invalid-feedback d-block">{{ form.first_name.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.last_name.id_for_label }}">{{ form.last_name.label }}</label>
                        {{ form.last_name }}
                        {% if form.last_name.errors %}
                            <div class="invalid-feedback d-block">{{ form.last_name.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.email.id_for_label }}">{{ form.email.label }}</label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="invalid-feedback d-block">{{ form.email.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.phone_number.id_for_label }}">{{ form.phone_number.label }}</label>
                        {{ form.phone_number }}
                        {% if form.phone_number.errors %}
                            <div class="invalid-feedback d-block">{{ form.phone_number.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.password.id_for_label }}">{{ form.password.label }}</label>
                        {{ form.password }}
                        {% if form.password.errors %}
                            <div class="invalid-feedback d-block">{{ form.password.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.password_confirm.id_for_label }}">{{ form.password_confirm.label }}</label>
                        {{ form.password_confirm }}
                        {% if form.password_confirm.errors %}
                            <div class="invalid-feedback d-block">{{ form.password_confirm.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        {# Terms and Submission #}
        <div class="field-section">
            <div class="form-check">
                {{ form.terms_accepted }}
                <label for="{{ form.terms_accepted.id_for_label }}">{{ form.terms_accepted.label }}</label>
                {% if form.terms_accepted.errors %}
                    <div class="invalid-feedback d-block">{{ form.terms_accepted.errors }}</div>
                {% endif %}
            </div>
            
            {% if form.non_field_errors %}
                <div class="alert alert-danger mt-3">
                    {% for error in form.non_field_errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}
            
            <div class="form-group mt-4 text-center">
                <button type="submit" class="btn btn-primary btn-lg px-5">{% trans "Register Business" %}</button>
            </div>
            
            <div class="text-center mt-3">
                <p class="text-muted">
                    {% trans "Already have an account?" %} <a href="{% url 'login' %}">{% trans "Log in" %}</a>
                </p>
            </div>
        </div>
    </form>
</div>
{% endblock %} 