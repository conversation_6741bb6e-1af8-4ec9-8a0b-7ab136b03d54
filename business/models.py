from django.db import models, transaction
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from django.utils import timezone as django_timezone
from timezone_field import TimeZoneField
from django.utils.text import slugify
from utils.timezone_validators import validate_business_timezone, validate_working_hours_timezone
import uuid
import random
import string

class Business(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255)
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='owned_businesses')
    description = models.TextField(blank=True)
    website = models.URLField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=django_timezone.now)
    updated_at = models.DateTimeField(default=django_timezone.now)
    slug = models.SlugField(max_length=255, unique=True, blank=True)

    class Meta:
        verbose_name = 'Business'
        verbose_name_plural = 'Businesses'
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # Automatically populate slug if it is missing or blank
        if not self.slug:
            # Ensure we have a name before trying to slugify
            if not self.name:
                raise ValueError("Business 'name' must be set before saving to generate slug.")
            # Use the classmethod to generate a unique slug based on the name
            self.slug = self.generate_unique_slug(self.name)

        if not self.pk:
            self.created_at = django_timezone.now()
        self.updated_at = django_timezone.now()
        super().save(*args, **kwargs)
        
    @classmethod
    def create_with_admin(cls, business_data, user_data, identifier):
        """
        Create a business and an admin user in a single transaction
        
        Args:
            business_data (dict): Business data
            user_data (dict): User data
            identifier (str): The identifier used (email or phone)
            
        Returns:
            tuple: (business, user) The created business and user objects
        """
        from accounts.models import User, Role
        
        with transaction.atomic():
            # Create the user
            user = User.objects.create_user(identifier=identifier, **user_data)
            
            # Generate a slug for the business if not provided
            if 'slug' not in business_data or not business_data['slug']:
                business_data['slug'] = cls.generate_unique_slug(business_data['name'])
                
            # Create the business
            business = cls.objects.create(owner=user, **business_data)
            
            # Assign the admin role to the user
            admin_role = Role.objects.get(name='admin')
            user.roles.add(admin_role, through_defaults={'business': business})
            
            # Create default business settings
            BusinessSettings.objects.create(business=business)
            
            return business, user
    
    @classmethod
    def generate_unique_slug(cls, name):
        """
        Generate a unique slug for a business based on its name
        
        Args:
            name (str): Business name
            
        Returns:
            str: A unique slug
        """
        base_slug = slugify(name)
        slug = base_slug
        
        # Check if slug already exists
        count = 0
        while cls.objects.filter(slug=slug).exists():
            count += 1
            # For first conflict add a random string
            if count == 1:
                random_string = ''.join(random.choices(string.ascii_lowercase + string.digits, k=5))
                slug = f"{base_slug}-{random_string}"
            # For subsequent conflicts just increment a number
            else:
                slug = f"{base_slug}-{count}"
        
        return slug

class AccessLevel(models.Model):
    id = models.BigAutoField(primary_key=True)
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='access_levels')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    level = models.IntegerField(help_text=_('Higher levels have more permissions'), default=0)
    permissions = models.JSONField(
        default=dict,
        help_text=_('JSON object defining permissions for this access level')
    )
    created_at = models.DateTimeField(default=django_timezone.now)
    updated_at = models.DateTimeField(default=django_timezone.now)

    class Meta:
        verbose_name = 'Access Level'
        verbose_name_plural = 'Access Levels'
        unique_together = ('business', 'level')
        ordering = ['-level', 'name']

    def __str__(self):
        return f"{self.name} ({self.business.name})"

    def save(self, *args, **kwargs):
        if not self.pk:
            self.created_at = django_timezone.now()
        self.updated_at = django_timezone.now()
        super().save(*args, **kwargs)

class BusinessUser(models.Model):
    id = models.BigAutoField(primary_key=True)
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='business_users')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='business_memberships')
    access_level = models.ForeignKey(AccessLevel, on_delete=models.PROTECT, related_name='users')
    is_active = models.BooleanField(default=True)
    is_primary = models.BooleanField(
        default=False,
        help_text=_('Primary users have full access to business settings and can manage other users')
    )
    custom_permissions = models.JSONField(
        default=dict,
        help_text=_('Additional custom permissions for this user, overriding access level defaults')
    )
    created_at = models.DateTimeField(default=django_timezone.now)
    updated_at = models.DateTimeField(default=django_timezone.now)

    class Meta:
        verbose_name = 'Business User'
        verbose_name_plural = 'Business Users'
        unique_together = ('business', 'user')
        ordering = ['business', 'user']

    def __str__(self):
        return f"{self.user.email} - {self.business.name} ({self.access_level.name})"

    def save(self, *args, **kwargs):
        if not self.pk:
            self.created_at = django_timezone.now()
        self.updated_at = django_timezone.now()
        super().save(*args, **kwargs)

    def has_permission(self, permission_key):
        """
        Check if user has a specific permission, considering both access level and custom permissions
        """
        # Check custom permissions first (they override access level)
        if permission_key in self.custom_permissions:
            return self.custom_permissions[permission_key]
        
        # Primary users have all permissions
        if self.is_primary:
            return True
            
        # Fall back to access level permissions
        return self.access_level.permissions.get(permission_key, False)

class BusinessSettings(models.Model):
    """
    PROXY MODEL for OnlineBookingRules that provides a different admin view.
    """
    business = models.OneToOneField(Business, on_delete=models.CASCADE, primary_key=True)

    class Meta:
        verbose_name = 'Business Settings'
        verbose_name_plural = 'Business Settings'
        managed = False  # No database table for this model
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.business_id:
            try:
                self._rules = OnlineBookingRules.objects.get(business_id=self.business_id)
            except OnlineBookingRules.DoesNotExist:
                self._rules = None
                
    def __str__(self):
        return f"Settings for {self.business.name}"

    @property
    def timezone(self):
        # Get timezone from OnlineBookingRules
        return self._rules.timezone if self._rules else 'UTC'
    
    @property
    def currency(self):
        # Get currency from OnlineBookingRules
        return self._rules.currency if self._rules else 'USD'
        
    @property
    def booking_lead_time(self):
        # Convert hours to minutes
        return self._rules.min_hours_before * 60 if self._rules else 0
        
    @property
    def booking_window(self):
        return self._rules.max_days_in_advance if self._rules else 30
        
    @property
    def appointment_interval(self):
        return self._rules.appointment_interval if self._rules else 15
    
    @property
    def created_at(self):
        return self._rules.created_at if self._rules else django_timezone.now()
    
    @property
    def updated_at(self):
        return self._rules.updated_at if self._rules else django_timezone.now()

class Location(models.Model):
    id = models.BigAutoField(primary_key=True)
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='locations')
    name = models.CharField(max_length=255)
    address_line1 = models.CharField(max_length=255, null=True, blank=True)
    address_line2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, null=True, blank=True)
    state = models.CharField(max_length=100, null=True, blank=True)
    country = models.CharField(max_length=100, null=True, blank=True)
    postal_code = models.CharField(max_length=20, null=True, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=django_timezone.now)
    updated_at = models.DateTimeField(default=django_timezone.now)

    class Meta:
        verbose_name = 'Location'
        verbose_name_plural = 'Locations'
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.business.name}"

    def save(self, *args, **kwargs):
        if not self.pk:
            self.created_at = django_timezone.now()
        self.updated_at = django_timezone.now()
        super().save(*args, **kwargs)

class OnlineBookingRules(models.Model):
    """
    Core model for all booking-related settings.
    This is the source of truth for booking settings.
    """
    business = models.OneToOneField(Business, on_delete=models.CASCADE, related_name='booking_rules')
    # Fields previously in BusinessSettings
    timezone = models.CharField(
        max_length=50,
        default='UTC',
        help_text=_('Timezone for the business'),
        validators=[validate_business_timezone]
    )
    currency = models.CharField(max_length=3, default='USD', help_text=_('Currency for pricing'))
    max_days_in_advance = models.IntegerField(
        default=30,
        help_text=_('Maximum days in advance a booking can be made'),
        validators=[MinValueValidator(1), MaxValueValidator(365)]
    )
    min_hours_before = models.IntegerField(
        default=24,
        help_text=_('Minimum hours before a booking can be made'),
        validators=[MinValueValidator(0), MaxValueValidator(168)]  # Max 1 week
    )
    appointment_interval = models.IntegerField(
        default=15,
        help_text=_('Interval between available online appointments (in minutes)'),
        validators=[MinValueValidator(5), MaxValueValidator(60)]
    )
    # Existing fields in OnlineBookingRules
    allow_cancellation = models.BooleanField(default=True)
    cancellation_hours_before = models.IntegerField(default=24)
    cancellation_policy = models.TextField(
        blank=True, 
        null=True,
        help_text=_('Detailed cancellation policy to show customers during the booking process')
    )
    allow_rescheduling = models.BooleanField(default=True)
    rescheduling_hours_before = models.IntegerField(default=24)
    require_payment = models.BooleanField(default=False)
    deposit_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)

    # Smart Booking Rules (inline toggles)
    enable_bookend_slots = models.BooleanField(
        default=False,
        help_text=_('Show only the first and last available time slots of the day per stylist')
    )
    enable_gapless_booking = models.BooleanField(
        default=False,
        help_text=_('Fill gaps in availability by snapping to actual appointment end times')
    )
    enable_tentative_hold = models.BooleanField(
        default=False,
        help_text=_('Show partial time slots when remaining time is within tolerance threshold')
    )
    tentative_hold_tolerance = models.IntegerField(
        default=30,
        help_text=_('Tolerance threshold in minutes for tentative hold bookings'),
        validators=[MinValueValidator(5), MaxValueValidator(120)]
    )

    created_at = models.DateTimeField(default=django_timezone.now)
    updated_at = models.DateTimeField(default=django_timezone.now)

    class Meta:
        verbose_name = 'Online booking rules'
        verbose_name_plural = 'Online booking rules'

    def __str__(self):
        return f'Booking rules for {self.business.name}'

    def save(self, *args, **kwargs):
        if not self.pk:
            self.created_at = django_timezone.now()
        self.updated_at = django_timezone.now()
        super().save(*args, **kwargs)

    def get_enabled_smart_rules(self):
        """Get all enabled smart booking rules for this business"""
        enabled_rules = []
        if self.enable_bookend_slots:
            enabled_rules.append({'rule_type': 1, 'rule_name': 'Bookend Slots'})
        if self.enable_gapless_booking:
            enabled_rules.append({'rule_type': 2, 'rule_name': 'Gapless Booking'})
        if self.enable_tentative_hold:
            enabled_rules.append({'rule_type': 3, 'rule_name': 'Tentative Hold', 'tolerance_minutes': self.tentative_hold_tolerance})
        return enabled_rules

    def get_smart_rule_by_type(self, rule_type):
        """Get a specific smart booking rule by type if it's enabled"""
        if rule_type == 1 and self.enable_bookend_slots:
            return {'rule_type': 1, 'rule_name': 'Bookend Slots'}
        elif rule_type == 2 and self.enable_gapless_booking:
            return {'rule_type': 2, 'rule_name': 'Gapless Booking'}
        elif rule_type == 3 and self.enable_tentative_hold:
            return {'rule_type': 3, 'rule_name': 'Tentative Hold', 'tolerance_minutes': self.tentative_hold_tolerance}
        return None

    def has_smart_rule(self, rule_type):
        """Check if a specific smart booking rule is enabled"""
        if rule_type == 1:
            return self.enable_bookend_slots
        elif rule_type == 2:
            return self.enable_gapless_booking
        elif rule_type == 3:
            return self.enable_tentative_hold
        return False


class SmartBookingRule(models.Model):
    """
    Model for configurable smart booking rules that modify availability calculation.
    Each rule can be enabled/disabled and has specific parameters.
    """
    RULE_TYPE_CHOICES = [
        (1, 'Bookend Slots'),
        (2, 'Gapless Booking'),
        (3, 'Tentative Hold'),
    ]

    RULE_DESCRIPTIONS = {
        1: 'Show only the first and last available time slots of the day per stylist',
        2: 'Fill gaps in availability by snapping to actual appointment end times',
        3: 'Show partial time slots when remaining time is within tolerance threshold'
    }

    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='smart_booking_rules')
    rule_type = models.IntegerField(
        choices=RULE_TYPE_CHOICES,
        help_text=_('Type of smart booking rule')
    )
    is_enabled = models.BooleanField(
        default=False,
        help_text=_('Whether this rule is currently active')
    )

    # Rule-specific parameters
    tolerance_minutes = models.IntegerField(
        default=30,
        help_text=_('Tolerance threshold in minutes (used by Tentative Hold rule)'),
        validators=[MinValueValidator(5), MaxValueValidator(120)]
    )

    created_at = models.DateTimeField(default=django_timezone.now)
    updated_at = models.DateTimeField(default=django_timezone.now)

    class Meta:
        verbose_name = 'Smart booking rule'
        verbose_name_plural = 'Smart booking rules'
        unique_together = ('business', 'rule_type')
        ordering = ['rule_type']

    def __str__(self):
        return f'{self.get_rule_type_display()} - {self.business.name}'

    def save(self, *args, **kwargs):
        if not self.pk:
            self.created_at = django_timezone.now()
        self.updated_at = django_timezone.now()
        super().save(*args, **kwargs)

    @property
    def rule_name(self):
        """Get the display name of the rule type"""
        return self.get_rule_type_display()

    @property
    def rule_description(self):
        """Get the predefined description for this rule type"""
        return self.RULE_DESCRIPTIONS.get(self.rule_type, '')


class BusinessCustomer(models.Model):
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='business_customers')
    customer = models.ForeignKey('customers.CustomerProfile', on_delete=models.CASCADE, related_name='business_relationships')
    
    # Clean notes field for employee notes only
    notes = models.TextField(blank=True, help_text="Employee notes about this customer")
    
    # Core relationship data
    loyalty_points = models.IntegerField(default=0)
    opt_in_marketing = models.BooleanField(default=True)
    email_reminders = models.BooleanField(default=True)
    sms_reminders = models.BooleanField(default=True)
    tags = models.ManyToManyField('customers.CustomerTag', blank=True, related_name='business_customers')
    
    # Form completion tracking
    signed_forms = models.JSONField(
        default=list,
        help_text="List of form template IDs that this customer has signed"
    )
    forms_completed_at = models.DateTimeField(
        null=True, 
        blank=True,
        help_text="Timestamp when all required forms were completed"
    )
    
    # Business-specific customer data from import
    customer_since = models.CharField(max_length=100, blank=True, null=True, help_text="Original customer since date from import")
    last_visited = models.CharField(max_length=100, blank=True, null=True, help_text="Last visit date from import")
    membership_type = models.CharField(max_length=100, blank=True, null=True, help_text="Membership type from import")
    referred_by = models.CharField(max_length=255, blank=True, null=True, help_text="Referral source from import")
    
    # Business preferences from import
    online_booking_allowed = models.BooleanField(null=True, blank=True, help_text="Online booking preference from import")
    credit_card_info = models.CharField(max_length=255, blank=True, null=True, help_text="Credit card info from import")
    
    # Business statistics from import
    appointments_booked = models.IntegerField(null=True, blank=True, help_text="Total appointments from import")
    classes_booked = models.IntegerField(null=True, blank=True, help_text="Total classes from import")
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Total amount paid from import")
    no_shows_cancellations = models.IntegerField(null=True, blank=True, help_text="No shows/cancellations from import")
    employee_seen = models.TextField(blank=True, null=True, help_text="Employees seen from import")
    
    # Import metadata
    imported_at = models.DateTimeField(null=True, blank=True, help_text="When this customer was imported")
    import_source = models.CharField(max_length=100, blank=True, null=True, help_text="Source of import (filename, etc)")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Business customer'
        verbose_name_plural = 'Business customers'
        unique_together = ('business', 'customer')
    
    def __str__(self):
        return f"{self.customer.user.email} - {self.business.name}"
    
    def save(self, *args, **kwargs):
        if not self.pk:
            self.created_at = django_timezone.now()
        self.updated_at = django_timezone.now()
        super().save(*args, **kwargs)
    
    def mark_form_signed(self, form_template_id):
        """Mark a form as signed by this customer"""
        if form_template_id not in self.signed_forms:
            self.signed_forms.append(form_template_id)
            self.save(update_fields=['signed_forms', 'updated_at'])
    
    def has_signed_form(self, form_template_id):
        """Check if customer has signed a specific form"""
        return form_template_id in self.signed_forms
    
    def get_missing_required_forms(self):
        """Get list of required forms that haven't been submitted"""
        from forms.models import BusinessRequiredForm, FormSubmission
        
        required_forms = BusinessRequiredForm.objects.filter(
            business=self.business,
            is_required=True
        ).values_list('form_template_id', flat=True)
        
        # Check which required forms have been submitted
        submitted_forms = FormSubmission.objects.filter(
            business_customer=self,
            form_template_id__in=required_forms,
            status__in=['Submitted', 'Approved']
        ).values_list('form_template_id', flat=True)
        
        missing_forms = [form_id for form_id in required_forms if form_id not in submitted_forms]
        
        return missing_forms
    
    def has_all_required_forms_signed(self):
        """Check if customer has submitted all required forms"""
        missing_forms = self.get_missing_required_forms()
        return len(missing_forms) == 0
    
    def update_completion_status(self):
        """Update forms_completed_at timestamp if all forms are signed"""
        if self.has_all_required_forms_signed() and not self.forms_completed_at:
            self.forms_completed_at = django_timezone.now()
            self.save(update_fields=['forms_completed_at', 'updated_at'])

class StylistLevel(models.Model):
    """
    Model to allow businesses to define their own stylist levels/tiers
    """
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='stylist_levels')
    name = models.CharField(max_length=100, help_text="Name of the stylist level (e.g., Junior, Senior, Master)")
    description = models.TextField(blank=True)
    level_order = models.PositiveIntegerField(
        default=0, 
        help_text="Order of this level (lower numbers = less experienced)"
    )
    is_default = models.BooleanField(
        default=False,
        help_text="Is this the default level for new employees?"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Stylist Level'
        verbose_name_plural = 'Stylist Levels'
        ordering = ['business', 'level_order']
        constraints = [
            models.UniqueConstraint(
                fields=['business', 'name'],
                name='unique_stylist_level_name_per_business'
            )
        ]
    
    def __str__(self):
        return f"{self.name} - {self.business.name}"
    
    def save(self, *args, **kwargs):
        # Ensure only one default per business
        if self.is_default:
            # Reset other defaults for this business
            StylistLevel.objects.filter(
                business=self.business, 
                is_default=True
            ).exclude(pk=self.pk).update(is_default=False)
        
        super().save(*args, **kwargs)

# Forms-related models have been moved to the forms app
# See forms/models.py for FormTemplate, FormSubmission, and Signature models


class BusinessNotificationSettings(models.Model):
    """
    Business-level notification settings for email and text notifications.
    Controls when and how notifications are sent to customers.
    """
    business = models.OneToOneField(
        Business,
        on_delete=models.CASCADE,
        related_name='notification_settings'
    )

    # Appointment Detail Notifications
    appointment_detail_enabled = models.BooleanField(
        default=True,
        help_text=_('Send notifications when an appointment is booked (Email, Text, Push)')
    )

    # Confirmation Request Settings
    confirmation_request_enabled = models.BooleanField(
        default=True,
        help_text=_('Automatically send notifications asking customers to confirm they will show up (Email, Text, Push)')
    )
    confirmation_hours_before = models.IntegerField(
        default=72,
        help_text=_('Hours before appointment to send confirmation request'),
        validators=[MinValueValidator(1), MaxValueValidator(168)]  # Max 1 week
    )

    # Appointment Reminder Settings
    appointment_reminder_enabled = models.BooleanField(
        default=True,
        help_text=_('Automatically send notifications reminding customers about their appointment (Email, Text, Push)')
    )
    reminder_hours_before = models.IntegerField(
        default=24,
        help_text=_('Hours before appointment to send reminder'),
        validators=[MinValueValidator(1), MaxValueValidator(168)]  # Max 1 week
    )

    # Custom Email Messages
    new_appointment_message = models.TextField(
        blank=True,
        max_length=1000,
        help_text=_('Custom message to include in new appointment emails (max 1000 characters)')
    )

    # Cancellation & No Show Policy
    cancellation_no_show_policy_message = models.TextField(
        blank=True,
        max_length=2000,
        help_text=_('Additional cancellation and no-show policy message to include in appointment emails (max 2000 characters)')
    )

    created_at = models.DateTimeField(default=django_timezone.now)
    updated_at = models.DateTimeField(default=django_timezone.now)

    class Meta:
        verbose_name = 'Business Notification Settings'
        verbose_name_plural = 'Business Notification Settings'

    def __str__(self):
        return f'Notification settings for {self.business.name}'

    def get_required_cancellation_policy(self):
        """
        Get the required cancellation policy text that cannot be edited.
        """
        return (
            "We ask that you please reschedule at least 24 hours before the beginning of your appointment, "
            "or you may be charged a cancellation fee of 100% of the price of your scheduled appointment."
        )

    def get_full_cancellation_policy(self):
        """
        Get the complete cancellation policy including required and custom sections.
        """
        required_policy = self.get_required_cancellation_policy()
        if self.cancellation_no_show_policy_message.strip():
            return f"{required_policy}\n\n{self.cancellation_no_show_policy_message.strip()}"
        return required_policy

    def save(self, *args, **kwargs):
        if not self.pk:
            self.created_at = django_timezone.now()
        self.updated_at = django_timezone.now()
        super().save(*args, **kwargs)
