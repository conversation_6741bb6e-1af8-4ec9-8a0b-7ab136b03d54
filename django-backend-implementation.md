# Django Backend Integration for AWS Notification System

This document outlines the steps to integrate your Django (Python 3.12) application with AWS services (SNS, SQS, Lambda) for push notifications to iOS devices. The infrastructure is deployed on ECS with a PostgreSQL RDS and uses AWS Secrets Manager.

## Prerequisites

- Python 3.12
- PostgreSQL on Amazon RDS
- AWS SDK for Python (`boto3`)

## 1. Database Configuration

Configure `settings.py` to connect to RDS using environment variables:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ['DB_NAME'],
        'USER': os.environ['DB_USER'],
        'PASSWORD': os.environ['DB_PASSWORD'],
        'HOST': os.environ['DB_HOST'],
        'PORT': '5432',
        'OPTIONS': {'sslmode': 'require'},
    }
}
```

Optionally fetch from Secrets Manager if using a JSON secret.

## 2. Device Token Management

Create a `DeviceToken` model in `models.py`:

```python
from django.db import models
from django.conf import settings

class DeviceToken(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='device_tokens')
    token = models.CharField(max_length=200, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    last_active = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
```

Implement an API endpoint to register tokens:

```python
# views.py
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import DeviceToken

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def register_device(request):
    token = request.data.get('device_token')
    if token:
        DeviceToken.objects.update_or_create(user=request.user, token=token, defaults={'is_active': True})
        return Response({'detail': 'Device registered'}, status=200)
    return Response({'error': 'No token provided'}, status=400)
```

## 3. Publishing to SNS

Install `boto3` and configure AWS region via environment:

```bash
pip install boto3
```

In your notification utility (e.g., `notifications.py`):

```python
import os
import json
import boto3
from .models import DeviceToken

sns_client = boto3.client('sns', region_name=os.environ['AWS_REGION'])
TOPIC_ARN = os.environ['SNS_TOPIC_ARN']

def notify_user(user, alert_text):
    tokens = DeviceToken.objects.filter(user=user, is_active=True).values_list('token', flat=True)
    for token in tokens:
        message = json.dumps({'device_token': token, 'alert': alert_text})
        sns_client.publish(TopicArn=TOPIC_ARN, Message=message)
```

Use this function when an appointment changes, a waitlist entry is added, or a form is submitted. For example, in a Django signal:

```python
# signals.py
from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Appointment
from .notifications import notify_user

@receiver(post_save, sender=Appointment)
def appointment_changed(sender, instance, created, **kwargs):
    if not created and instance.status == 'rescheduled':
        alert = f'Your appointment on {instance.start_time} was rescheduled'
        notify_user(instance.customer, alert)
```

### 3.1 Notifying Multiple Recipients

In cases where you need to notify more than one party (e.g., both the **stylist** and the **customer**), simply call `notify_user` for each recipient. For clarity and reusability, you can add a helper that accepts a list of users:

```python
def notify_users(users, alert_text):
    for user in users:
        notify_user(user, alert_text)
```

Then, when an appointment is booked:

```python
from .notifications import notify_users

# Example usage:
alert = f"New appointment on {appointment.start_time} with {appointment.service_name}"
recipients = [appointment.customer, appointment.stylist]  # or a subset
notify_users(recipients, alert)
```

This wrapper keeps your code clean and makes it trivial to adjust the recipient list per event type.

## 3.1. User Type and Role Classification

The notification system uses a two-tier classification system:

**User Types (for template selection):**
- **business**: Business-side users (employees, business owners, admins)
- **customer**: Customer-side users

**Roles (for specific user identification):**
- **customer**: Regular customers booking appointments
- **employee**: Staff members (stylists, receptionists, etc.)
- **business_owner**: Users who own/manage businesses
- **admin**: System administrators

**Template Mapping:**
- `business` user type → `notification_templates/business_templates/`
- `customer` user type → `notification_templates/customer_templates/`

**Recipient Filtering:**
The system automatically filters recipients based on event type:
- **appointment.booked**: Notify Business & Customer
- **appointment.changed**: Notify Business & Customer
- **appointment.cancelled**: Notify Business & Customer
- **appointment.confirmation_requested**: Customer Only
- **appointment.reminder**: Customer Only

### Single SNS Topic Architecture

We use a **single SNS topic** with user type classification rather than separate topics because:

1. **Shared Infrastructure**: Business and customer notifications use the same delivery channels (email, SMS, push)
2. **Common Logic**: Notification preferences and delivery logic are similar across user types
3. **Simplified Maintenance**: One Lambda function, one deployment pipeline
4. **Future Flexibility**: Easy to add new user types without infrastructure changes

### Message Format with User Type and Role

```json
{
  "notification_type": "multi_recipient",
  "event_type": "appointment.changed",
  "recipients": [
    {
      "user_id": "customer-uuid",
      "email": "<EMAIL>",
      "user_type": "customer",
      "role": "customer",
      "email_template_key": "notification_templates/customer_templates/appointment_changed.html",
      "message": "Your appointment has been rescheduled...",
      "device_tokens": ["token1", "token2"],
      "email_enabled": true,
      "sms_enabled": true,
      "push_enabled": true
    },
    {
      "user_id": "employee-uuid",
      "email": "<EMAIL>",
      "user_type": "business",
      "role": "employee",
      "email_template_key": "notification_templates/business_templates/appointment_updated.html",
      "message": "Appointment with John Doe has been rescheduled...",
      "device_tokens": ["token3"],
      "email_enabled": true,
      "sms_enabled": false,
      "push_enabled": true
    }
  ],
  "business_context": {
    "business_id": 1
  },
  "appointment_data": {
    // ... appointment details
  }
}
```

### Lambda Function Routing

The Lambda function can route notifications based on user type:

```javascript
const { recipients, event_type, appointment_data } = messageData;

// Handle multiple recipients in a single message
for (const recipient of recipients) {
  const {
    user_type,
    role,
    email_template_key,
    message,
    email,
    phone_number,
    device_tokens,
    email_enabled,
    sms_enabled,
    push_enabled
  } = recipient;

  // Send email using the provided template key
  if (email_enabled && email) {
    await sendEmailNotification(email, message, email_template_key);
  }

  // Send SMS if enabled
  if (sms_enabled && phone_number) {
    await sendSMSNotification(phone_number, message);
  }

  // Send push notifications if enabled
  if (push_enabled && device_tokens.length > 0) {
    for (const token of device_tokens) {
      await sendPushNotification(token, message, appointment_data);
    }
  }

  // Additional channels based on role
  if (role === 'employee') {
    // Employees might get additional Slack notifications
    await sendSlackNotification(message);
  }
}
```

## 4. AWS Secrets Manager for DB Credentials

In ECS task definition, map the DB secret to environment variables:

```json
{
  "secrets": [
    { "name": "DB_USER", "valueFrom": "<DB_SECRET_ARN>:username" },
    { "name": "DB_PASSWORD", "valueFrom": "<DB_SECRET_ARN>:password" },
    { "name": "DB_NAME", "valueFrom": "<DB_SECRET_ARN>:dbname" },
    { "name": "DB_HOST", "valueFrom": "<DB_SECRET_ARN>:host" }
  ]
}
```

## 5. Environment Variables

Ensure the following env vars are set in ECS for the Django container:

- `AWS_REGION` – e.g. `us-west-2`
- `SNS_TOPIC_ARN` – ARN of the SNS topic
- Database env vars (`DB_USER`, `DB_PASSWORD`, etc.)

## 6. Testing and Monitoring

- Deploy the CDK stack and ECS service.
- Register a device with the `/api/devices/register/` endpoint.
- Trigger an event (e.g., reschedule an appointment) and verify the device receives a push.
- Monitor CloudWatch Logs for the Lambda to ensure messages are processed.
- Check the SQS queue for any unprocessed or dead-lettered messages.

By following these steps, your Django backend will seamlessly integrate with AWS to send real-time push notifications via APNs without blocking user requests.
