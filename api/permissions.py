from rest_framework import permissions

class PublicReadOnlyPrivateWrite(permissions.BasePermission):
    """
    Custom permission:
    - Allow read access to any user (both authenticated and anonymous users)
    - Allow write access only to authenticated users
    """
    
    def has_permission(self, request, view):
        # Allow GET, HEAD, OPTIONS requests to anyone
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # For write methods, require authentication
        return request.user and request.user.is_authenticated

class IsAdminUserOrReadOnly(permissions.BasePermission):
    """
    Custom permission:
    - Allow read access to any user
    - Allow write access only to admin users
    """
    
    def has_permission(self, request, view):
        # Allow GET, HEAD, OPTIONS requests to anyone
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # For write methods, require admin status
        return request.user and request.user.is_staff 