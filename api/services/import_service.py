"""
Customer import service for handling various salon software export formats.
Provides field mapping and data transformation capabilities.
"""
import json
from datetime import datetime
from typing import Dict, Any, List, Optional


class CustomerImportMappingService:
    """
    Service to handle customer data field mapping and transformation
    for various salon software export formats.
    """
    
    # Standard field mappings for common salon software exports
    COMMON_FIELD_MAPPINGS = {
        # Core customer fields
        'first_name': ['First Name', 'FirstName', 'first_name', 'fname'],
        'last_name': ['Last Name', 'LastName', 'last_name', 'lname'],
        'email': ['Email', 'Email Address', 'email', 'EmailAddress'],
        'mobile': ['Mobile', 'Phone', 'Cell Phone', 'Mobile Phone', 'phone_mobile'],
        'phone': ['Day', 'Day Phone', 'Phone', 'phone_home'],
        'phone_night': ['Night', 'Night Phone', 'phone_evening'],
        
        # Profile fields
        'credit_card': ['Credit Card', 'Card on File', 'card_on_file', 'CC'],
        'points_earned': ['Points Earned', 'Loyalty Points', 'Points', 'loyalty_points'],
        'tags': ['Tags', 'Categories', 'Labels'],
        
        # Additional info fields
        'birthdate': ['Birthdate', 'Date of Birth', 'DOB', 'Birthday'],
        'gender': ['Gender', 'Sex'],
        'customer_since': ['Customer Since', 'Registration Date', 'Joined'],
        'last_visited': ['Last Visited', 'Last Visit', 'Last Appointment'],
        'membership': ['Membership', 'Member Type', 'Membership Level'],
        'referred_by': ['Refered By', 'Referred By', 'Referral'],
        'online_booking': ['Online Booking', 'Online Booking Enabled'],
        
        # Address fields
        'apt_suite': ['Apt/Suite', 'Apartment', 'Suite', 'Unit'],
        'address': ['Address', 'Street Address', 'Street'],
        'city': ['City'],
        'state': ['State', 'Province'],
        'zip': ['Zip', 'Postal Code', 'ZIP Code'],
        
        # Statistics
        'appointments_booked': ['Appointments Booked', 'Total Appointments'],
        'classes_booked': ['Classes Booked', 'Total Classes'],
        'amount_paid': ['Amount Paid', 'Total Spent', 'Revenue'],
        'no_shows_cancellations': ['No Shows/Cancellations', 'No Shows'],
        'employee_seen': ['Employee Seen', 'Preferred Staff', 'Staff']
    }
    
    def auto_map_fields(self, available_columns: List[str]) -> Dict[str, str]:
        """
        Automatically map available columns to standard fields based on common patterns.
        
        Args:
            available_columns: List of column names from the import file
            
        Returns:
            Dictionary mapping target fields to source columns
        """
        field_mapping = {}
        
        for target_field, possible_sources in self.COMMON_FIELD_MAPPINGS.items():
            for source_field in possible_sources:
                if source_field in available_columns:
                    field_mapping[target_field] = source_field
                    break
        
        return field_mapping
    
    def get_required_fields(self) -> List[str]:
        """Get list of required fields for customer import"""
        return ['first_name', 'last_name', 'email']
    
    def get_recommended_fields(self) -> List[str]:
        """Get list of recommended fields for better customer data"""
        return ['mobile', 'points_earned', 'tags', 'customer_since']
    
    def validate_mapping(self, field_mapping: Dict[str, str], available_columns: List[str]) -> Dict[str, Any]:
        """
        Validate field mapping and return validation results.
        
        Args:
            field_mapping: Dictionary mapping target fields to source columns
            available_columns: List of available columns in the source data
            
        Returns:
            Dictionary with validation results
        """
        required_fields = self.get_required_fields()
        recommended_fields = self.get_recommended_fields()
        
        missing_required = []
        missing_recommended = []
        invalid_mappings = []
        
        # Check required fields
        for field in required_fields:
            if field not in field_mapping or not field_mapping[field]:
                missing_required.append(field)
        
        # Check recommended fields
        for field in recommended_fields:
            if field not in field_mapping or not field_mapping[field]:
                missing_recommended.append(field)
        
        # Check invalid mappings
        for target_field, source_field in field_mapping.items():
            if source_field and source_field not in available_columns:
                invalid_mappings.append({
                    'target_field': target_field,
                    'source_field': source_field
                })
        
        return {
            'is_valid': len(missing_required) == 0 and len(invalid_mappings) == 0,
            'missing_required': missing_required,
            'missing_recommended': missing_recommended,
            'invalid_mappings': invalid_mappings,
            'mapped_fields': len([f for f in field_mapping.values() if f]),
            'total_available': len(available_columns)
        }
    
    def transform_customer_data(self, raw_data: Dict[str, Any], field_mapping: Dict[str, str]) -> Dict[str, Any]:
        """
        Transform raw customer data using field mapping.
        
        Args:
            raw_data: Raw customer data from import file
            field_mapping: Field mapping configuration
            
        Returns:
            Transformed customer data
        """
        mapped_data = {}
        
        for target_field, source_field in field_mapping.items():
            if source_field and source_field in raw_data:
                value = raw_data[source_field]
                mapped_data[target_field] = self.clean_field_value(target_field, value)
        
        return mapped_data
    
    def clean_field_value(self, field_name: str, value: Any) -> Any:
        """
        Clean and normalize field values based on field type.
        
        Args:
            field_name: Name of the target field
            value: Raw value from source data
            
        Returns:
            Cleaned value
        """
        if value is None or (isinstance(value, str) and not value.strip()):
            return None
        
        # Convert to string for processing
        str_value = str(value).strip()
        
        # Field-specific cleaning
        if field_name in ['first_name', 'last_name']:
            return str_value.title()
        
        elif field_name == 'email':
            return str_value.lower()
        
        elif field_name in ['mobile', 'phone', 'phone_night']:
            return self.clean_phone_number(str_value)
        
        elif field_name == 'points_earned':
            try:
                return int(float(str_value))
            except (ValueError, TypeError):
                return 0
        
        elif field_name == 'credit_card':
            # For credit card, preserve the actual value (e.g., "ending in 3637")
            # Only return empty/None for clearly empty values
            if str_value.lower() in ['---', 'no', 'false', 'none', 'n/a', '']:
                return None
            return str_value
        
        elif field_name == 'online_booking':
            # For online booking, convert to boolean
            if str_value.lower() in ['allow', 'yes', 'true', '1', 'on', 'enabled', 'y']:
                return True
            elif str_value.lower() in ['---', 'no', 'false', '0', 'off', 'disabled', 'n']:
                return False
            return None
        
        elif field_name == 'tags':
            # Split comma-separated tags and clean them
            if str_value:
                return [tag.strip() for tag in str_value.split(',') if tag.strip()]
            return []
        
        return str_value
    
    def clean_phone_number(self, phone: str) -> str:
        """Clean and format phone number"""
        if not phone:
            return ''
        
        # Remove all non-digit characters except +
        import re
        cleaned = re.sub(r'[^\d+]', '', phone)
        
        # Add country code for US numbers if missing
        if cleaned and not cleaned.startswith('+'):
            if len(cleaned) == 10:
                cleaned = f'+1{cleaned}'
            elif len(cleaned) == 11 and cleaned.startswith('1'):
                cleaned = f'+{cleaned}'
        
        return cleaned
    

    
    def get_field_suggestions(self, available_columns: List[str]) -> Dict[str, List[str]]:
        """
        Get suggestions for unmapped fields based on available columns.
        
        Args:
            available_columns: List of available columns from import file
            
        Returns:
            Dictionary of field suggestions
        """
        suggestions = {}
        
        for target_field, possible_sources in self.COMMON_FIELD_MAPPINGS.items():
            matches = []
            for source_field in available_columns:
                # Check for exact matches
                if source_field in possible_sources:
                    matches.append(source_field)
                # Check for partial matches (case-insensitive)
                else:
                    for possible_source in possible_sources:
                        if (possible_source.lower() in source_field.lower() or 
                            source_field.lower() in possible_source.lower()):
                            matches.append(source_field)
                            break
            
            if matches:
                suggestions[target_field] = matches[:3]  # Limit to top 3 suggestions
        
        return suggestions 