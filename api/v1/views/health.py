"""
Health check endpoints for AWS services monitoring.
Based on DJANGO_AWS_INTEGRATION.md recommendations.
"""
from django.http import JsonResponse
from django.views import View
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from config.aws import aws_config
import logging

logger = logging.getLogger(__name__)


class AWSHealthCheckView(APIView):
    """
    Health check endpoint for AWS services.
    Based on DJANGO_AWS_INTEGRATION.md implementation.
    """
    
    authentication_classes = []  # No authentication required for health checks
    permission_classes = []
    
    def get(self, request):
        """Check AWS services health status"""
        health_status = {
            's3_accessible': self._check_s3_access(),
            'sqs_accessible': self._check_sqs_access(),
            'sns_accessible': self._check_sns_access(),
        }
        
        # Add configuration status
        config_status = {
            's3_bucket_configured': bool(aws_config.s3_bucket_name),
            'sqs_queue_configured': bool(aws_config.sqs_file_processing_queue_url),
            'sns_topic_configured': bool(aws_config.sns_topic_arn),
            'aws_profile_set': bool(aws_config.profile),
            'aws_region': aws_config.region,
        }
        
        # Overall health status
        all_healthy = all(health_status.values())
        status_code = status.HTTP_200_OK if all_healthy else status.HTTP_503_SERVICE_UNAVAILABLE
        
        response_data = {
            'status': 'healthy' if all_healthy else 'unhealthy',
            'aws_services': health_status,
            'configuration': config_status,
            'timestamp': request.META.get('HTTP_X_FORWARDED_FOR', 'unknown')
        }
        
        return Response(response_data, status=status_code)
    
    def _check_s3_access(self):
        """Check if S3 bucket is accessible."""
        try:
            if not aws_config.s3_bucket_name:
                return False
            aws_config.s3_client.head_bucket(Bucket=aws_config.s3_bucket_name)
            return True
        except Exception as e:
            logger.warning(f"S3 health check failed: {e}")
            return False
    
    def _check_sqs_access(self):
        """Check if SQS queue is accessible."""
        try:
            if not aws_config.sqs_file_processing_queue_url:
                return False
            aws_config.sqs_client.get_queue_attributes(
                QueueUrl=aws_config.sqs_file_processing_queue_url
            )
            return True
        except Exception as e:
            logger.warning(f"SQS health check failed: {e}")
            return False
    
    def _check_sns_access(self):
        """Check if SNS topic is accessible."""
        try:
            if not aws_config.sns_topic_arn:
                return True  # Not configured, so consider it "healthy"
            
            aws_config.sns_client.get_topic_attributes(TopicArn=aws_config.sns_topic_arn)
            return True
        except Exception as e:
            logger.warning(f"SNS health check failed: {e}")
            return False


class GeneralHealthCheckView(View):
    """
    General application health check endpoint.
    """
    
    def get(self, request):
        """Simple health check for load balancers"""
        return JsonResponse({
            'status': 'healthy',
            'application': 'chatbook-backend'
        }) 