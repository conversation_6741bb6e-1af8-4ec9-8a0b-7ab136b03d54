from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from waitlist.models import WaitlistEntry
from .serializers import WaitlistEntrySerializer, WaitlistEntryCreateSerializer


class WaitlistEntryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing waitlist entries
    """
    queryset = WaitlistEntry.objects.all()
    serializer_class = WaitlistEntrySerializer

    def get_serializer_class(self):
        if self.action == 'create':
            return WaitlistEntryCreateSerializer
        return WaitlistEntrySerializer

    def get_queryset(self):
        """
        Optionally filter by status
        """
        queryset = WaitlistEntry.objects.all()
        status_filter = self.request.query_params.get('status', None)
        if status_filter is not None:
            queryset = queryset.filter(status=status_filter)
        return queryset

    @action(detail=True, methods=['post'])
    def expire(self, request, pk=None):
        """
        Mark a waitlist entry as expired
        """
        entry = self.get_object()
        entry.status = 'expired'
        entry.save()
        return Response({'status': 'Entry marked as expired'})

    @action(detail=True, methods=['post'])
    def reactivate(self, request, pk=None):
        """
        Reactivate an expired waitlist entry
        """
        entry = self.get_object()
        entry.status = 'current'
        entry.expired_at = None
        entry.save()
        return Response({'status': 'Entry reactivated'})
