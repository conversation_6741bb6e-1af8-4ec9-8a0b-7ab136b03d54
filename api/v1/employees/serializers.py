from rest_framework import serializers
from employees.models import Employee, EmployeeWorkingHours, EmployeeCalendarConfig
from django.contrib.auth import get_user_model
from datetime import datetime

User = get_user_model()

class UserBasicSerializer(serializers.ModelSerializer):
    """Basic serializer for User model to be used in Employee serializers"""
    class Meta:
        model = User
        fields = ['id', 'email', 'first_name', 'last_name', 'phone_number']
        read_only_fields = ['id', 'email']

class EmployeeWorkingHoursSerializer(serializers.ModelSerializer):
    """Serializer for EmployeeWorkingHours model"""
    start_time = serializers.TimeField(format='%H:%M')
    end_time = serializers.TimeField(format='%H:%M')
    
    class Meta:
        model = EmployeeWorkingHours
        fields = [
            'id', 
            'day', 
            'start_time', 
            'end_time', 
            'is_active'
        ]
        read_only_fields = ['id']

class EmployeeSimplifiedSerializer(serializers.ModelSerializer):
    """Simplified serializer for Employee model for list view"""
    full_name = serializers.CharField(read_only=True)
    stylist_level_display = serializers.SerializerMethodField()
    
    class Meta:
        model = Employee
        fields = [
            'id',
            'full_name',
            'stylist_level',
            'stylist_level_display',
            'is_active'
        ]
    
    def get_stylist_level_display(self, obj):
        """Get the stylist level name"""
        if obj.stylist_level:
            return obj.stylist_level.name
        return "No Level"

class EmployeeDetailSimplifiedSerializer(serializers.ModelSerializer):
    """Serializer for Employee detail view with simplified response"""
    full_name = serializers.CharField(read_only=True)
    stylist_level_display = serializers.SerializerMethodField()
    role = serializers.CharField(source='employee_type', read_only=True)
    
    class Meta:
        model = Employee
        fields = [
            'id',
            'full_name',
            'stylist_level',
            'stylist_level_display',
            'is_active',
            'role'
        ]
    
    def get_stylist_level_display(self, obj):
        """Get the stylist level name"""
        if obj.stylist_level:
            return obj.stylist_level.name
        return "No Level"

class EmployeeBasicSerializer(serializers.ModelSerializer):
    """Basic serializer for Employee model with minimal fields"""
    user_details = UserBasicSerializer(source='user', read_only=True)
    full_name = serializers.CharField(read_only=True)
    stylist_level_display = serializers.SerializerMethodField()
    
    class Meta:
        model = Employee
        fields = [
            'id',
            'user',
            'user_details',
            'full_name',
            'business',
            'stylist_level',
            'stylist_level_display',
            'is_active'
        ]
        read_only_fields = ['id', 'full_name']
    
    def get_stylist_level_display(self, obj):
        """Get the stylist level name"""
        if obj.stylist_level:
            return obj.stylist_level.name
        return "No Level"

class EmployeeSerializer(serializers.ModelSerializer):
    """Standard serializer for Employee model"""
    user_details = UserBasicSerializer(source='user', read_only=True)
    full_name = serializers.CharField(read_only=True)
    working_hours = EmployeeWorkingHoursSerializer(many=True, read_only=True)
    stylist_level_display = serializers.SerializerMethodField()
    employee_type_display = serializers.CharField(source='get_employee_type_display', read_only=True)
    access_level_display = serializers.CharField(source='get_access_level_display', read_only=True)
    
    class Meta:
        model = Employee
        fields = [
            'id',
            'user',
            'user_details',
            'full_name',
            'business',
            'stylist_level',
            'stylist_level_display',
            'profile_image',
            'calendar_sync_enabled',
            'calendar_provider',
            'calendar_id',
            'accept_online_bookings',
            'employee_type',
            'employee_type_display',
            'access_level',
            'access_level_display',
            'is_active',
            'created_at',
            'updated_at',
            'working_hours'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'full_name']
    
    def get_stylist_level_display(self, obj):
        """Get the stylist level name"""
        if obj.stylist_level:
            return obj.stylist_level.name
        return "No Level"

class EmployeeDetailSerializer(EmployeeSerializer):
    """Detailed serializer for Employee model including all related data"""
    # Could extend with additional related data if needed in the future
    class Meta(EmployeeSerializer.Meta):
        pass 

class EmployeePermissionsSerializer(serializers.Serializer):
    """Serializer for employee permissions"""
    permissions = serializers.SerializerMethodField()
    
    def get_permissions(self, obj):
        """
        Get the employee's permissions based on their access level
        
        Returns a dictionary with permission keys and boolean values
        """
        # If employee has no access level, they have no permissions
        if not obj.access_level:
            return {}
            
        # The permissions field is a JSONField storing a dictionary
        # directly use the permissions dictionary
        return obj.access_level.permissions

class EmployeeCalendarConfigSerializer(serializers.ModelSerializer):
    """Serializer for EmployeeCalendarConfig model"""
    # Convert enum values directly to their display values
    week_start_day = serializers.SerializerMethodField()
    default_view = serializers.SerializerMethodField()
    
    class Meta:
        model = EmployeeCalendarConfig
        fields = [
            'id',
            'week_start_day',
            'default_view',
            'calendar_resolution',
            'display_hour_start',
            'display_hour_end',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
        
    def get_week_start_day(self, obj):
        """Return the display name directly instead of the code"""
        return obj.get_week_start_day_display()
        
    def get_default_view(self, obj):
        """Return the display name directly instead of the code"""
        return obj.get_default_view_display()
        
    def to_internal_value(self, data):
        """
        Convert input data to internal values, handling the direct display names
        and converting them back to their internal codes
        """
        # Create a copy of the data to modify
        internal_data = data.copy() if isinstance(data, dict) else {}
        
        # Handle week_start_day input as display name
        if 'week_start_day' in data:
            week_start_value = data['week_start_day'].lower()
            # Handle both code and display formats
            for choice in EmployeeCalendarConfig.WeekStartDay.choices:
                if week_start_value == choice[0].lower() or week_start_value == choice[1].lower():
                    internal_data['week_start_day'] = choice[0]
                    break
                    
        # Handle default_view input as display name
        if 'default_view' in data:
            default_view_value = data['default_view'].lower()
            # Handle both code and display formats
            for choice in EmployeeCalendarConfig.DefaultView.choices:
                if default_view_value == choice[0].lower() or default_view_value == choice[1].lower():
                    internal_data['default_view'] = choice[0]
                    break
        
        # Process the rest of the data normally
        return super().to_internal_value(internal_data) 