from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view, permission_classes
from django.shortcuts import get_object_or_404
from employees.models import Employee, EmployeeWorkingHours, EmployeeCalendarConfig
from appointments.models import Appointment
from datetime import datetime, timedelta
import logging
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.core.exceptions import ValidationError
from .serializers import (
    EmployeeSerializer, EmployeeWorkingHoursSerializer,
    EmployeeDetailSimplifiedSerializer, EmployeePermissionsSerializer,
    EmployeeCalendarConfigSerializer
)
from ..services.serializers import DetailedAppointmentSerializer
import json

logger = logging.getLogger(__name__)

class EmployeeCurrentUserView(APIView):
    """API endpoint for getting the current employee user's details"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        try:
            # Get the employee profile for the current user
            employee = get_object_or_404(Employee, user=request.user)
            
            # Use the simplified serializer for the response
            serializer = EmployeeDetailSimplifiedSerializer(employee)
            return Response(serializer.data)
        except Exception as e:
            logger.exception(f"Error fetching employee profile: {str(e)}")
            return Response({
                "error": "An error occurred while fetching employee profile",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class EmployeeProfileView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        try:
            # Get the employee profile for the current user
            employee = get_object_or_404(Employee, user=request.user)
            
            # Format working hours as a dictionary keyed by day name
            working_hours_dict = {}
            for day in ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']:
                # Default values in case no working hours are set for this day
                working_hours_dict[day] = {
                    "start": "09:00",
                    "end": "17:00",
                    "is_active": False
                }
            
            # Override with actual working hours from the database
            for hours in employee.working_hours.all():
                day_name = hours.day.lower()
                working_hours_dict[day_name] = {
                    "start": hours.start_time.strftime("%H:%M"),
                    "end": hours.end_time.strftime("%H:%M"),
                    "is_active": hours.is_active
                }
            
            # Format response as expected by iOS app
            response_data = {
                "id": employee.id,
                "email": employee.email,
                "first_name": employee.first_name,
                "last_name": employee.last_name,
                "role": employee.user.user_type,  # Get the user_type from the User model
                "working_hours": working_hours_dict
            }
            
            return Response(response_data)
        except Exception as e:
            logger.exception(f"Error fetching employee profile: {str(e)}")
            return Response({
                "error": "An error occurred while fetching employee profile",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@method_decorator(csrf_exempt, name='dispatch')
class EmployeeWorkingHoursView(APIView):
    """
    API endpoint for managing employee working hours
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """
        Get all working hours for the authenticated employee
        """
        try:
            # Get the employee profile for the current user
            employee = get_object_or_404(Employee, user=request.user)
            
            # Get all working hours for the employee
            hours_queryset = EmployeeWorkingHours.objects.filter(employee=employee)
            
            # Use the serializer to format the response
            serializer = EmployeeWorkingHoursSerializer(hours_queryset, many=True)
            
            return Response({
                'working_hours': serializer.data
            })
        except Exception as e:
            logger.exception(f"Error fetching working hours: {str(e)}")
            return Response({
                'error': 'An error occurred while fetching working hours',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def post(self, request, *args, **kwargs):
        """
        Update working hours for the authenticated employee
        """
        try:
            # Get the employee profile for the current user
            employee = get_object_or_404(Employee, user=request.user)
            
            # Parse the request data
            working_hours_data = request.data.get('working_hours', {})
            
            if not working_hours_data:
                return Response({
                    'error': 'No working hours data provided'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            logger.info(f"Updating working hours for employee: {employee.email}")
            logger.debug(f"Working hours data: {working_hours_data}")
            
            # Update each day's working hours
            updated_hours = []
            for day, hours_data in working_hours_data.items():
                # Validate the day
                valid_days = [choice[0] for choice in EmployeeWorkingHours.Day.choices]
                if day not in valid_days:
                    return Response({
                        'error': f"Invalid day: {day}",
                        'valid_days': valid_days
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # Get or create working hours for this day
                hours, created = EmployeeWorkingHours.objects.get_or_create(
                    employee=employee,
                    day=day
                )
                
                # Update with provided data
                if 'start' in hours_data and hours_data['start']:
                    hours.start_time = hours_data['start']
                if 'end' in hours_data and hours_data['end']:
                    hours.end_time = hours_data['end']
                if 'is_active' in hours_data:
                    hours.is_active = hours_data['is_active']
                
                # Save changes
                try:
                    hours.save()
                    # Use the serializer to format the response
                    serializer = EmployeeWorkingHoursSerializer(hours)
                    updated_hours.append(serializer.data)
                except ValidationError as ve:
                    return Response({
                        'error': f"Validation error for {day}",
                        'details': str(ve)
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            return Response({
                'message': 'Working hours updated successfully',
                'updated_hours': updated_hours
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.exception(f"Error updating working hours: {str(e)}")
            return Response({
                'error': 'An error occurred while updating working hours',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@method_decorator(csrf_exempt, name='dispatch')
class EmployeePermissionsView(APIView):
    """
    API endpoint for retrieving an employee's permissions
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """
        Get permissions for the authenticated employee
        """
        try:
            # Get the employee profile for the current user
            employee = get_object_or_404(Employee, user=request.user)
            
            # Use the permissions serializer
            serializer = EmployeePermissionsSerializer(employee)
            
            return Response(serializer.data)
        except Exception as e:
            logger.exception(f"Error fetching employee permissions: {str(e)}")
            return Response({
                'error': 'An error occurred while fetching employee permissions',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@method_decorator(csrf_exempt, name='dispatch')
class EmployeeCalendarConfigView(APIView):
    """
    API endpoint for managing employee calendar configuration
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """
        Get calendar configuration for the authenticated employee
        """
        try:
            # Get the employee profile for the current user
            employee = get_object_or_404(Employee, user=request.user)
            
            # Get or create calendar config for this employee
            calendar_config, created = EmployeeCalendarConfig.objects.get_or_create(
                employee=employee
            )
            
            # Use the serializer to format the response
            serializer = EmployeeCalendarConfigSerializer(calendar_config)
            
            return Response(serializer.data)
        except Exception as e:
            logger.exception(f"Error fetching calendar config: {str(e)}")
            return Response({
                'error': 'An error occurred while fetching calendar configuration',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def post(self, request, *args, **kwargs):
        """
        Create or update calendar configuration for the authenticated employee
        """
        try:
            # Get the employee profile for the current user
            employee = get_object_or_404(Employee, user=request.user)
            
            # Get or create calendar config for this employee
            calendar_config, created = EmployeeCalendarConfig.objects.get_or_create(
                employee=employee
            )
            
            # Use the serializer to validate and save data
            serializer = EmployeeCalendarConfigSerializer(
                calendar_config, 
                data=request.data,
                partial=True
            )
            
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.exception(f"Error updating calendar config: {str(e)}")
            return Response({
                'error': 'An error occurred while updating calendar configuration',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    # Support PUT method as well for full updates
    def put(self, request, *args, **kwargs):
        return self.post(request, *args, **kwargs)

@method_decorator(csrf_exempt, name='dispatch')
class EmployeeAppointmentsView(APIView):
    """
    API endpoint for fetching appointments for the current employee
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """
        Get appointments for the authenticated employee within a date range
        
        Query parameters:
        - from: Start date (YYYY-MM-DD) - defaults to today
        - to: End date (YYYY-MM-DD) - defaults to 7 days from start date
        """
        try:
            # Get the employee profile for the current user
            employee = get_object_or_404(Employee, user=request.user)
            
            # Parse date range parameters
            from_date_str = request.query_params.get('from')
            to_date_str = request.query_params.get('to')
            
            # Set default date range if not provided
            today = datetime.now().date()
            
            try:
                from_date = datetime.strptime(from_date_str, '%Y-%m-%d').date() if from_date_str else today
            except ValueError:
                return Response({
                    'error': 'Invalid from date format. Use YYYY-MM-DD.'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                to_date = datetime.strptime(to_date_str, '%Y-%m-%d').date() if to_date_str else from_date + timedelta(days=7)
            except ValueError:
                return Response({
                    'error': 'Invalid to date format. Use YYYY-MM-DD.'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Ensure to_date is not before from_date
            if to_date < from_date:
                return Response({
                    'error': 'End date cannot be before start date.'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Calculate start and end datetime
            from_datetime = datetime.combine(from_date, datetime.min.time())
            to_datetime = datetime.combine(to_date, datetime.max.time())
            
            # Get appointments within the date range for this employee
            # Filter only appointments that should appear on the calendar (not cancelled)
            appointments = Appointment.objects.filter(
                employee=employee,
                start_time__gte=from_datetime,
                start_time__lte=to_datetime
            ).exclude(
                status__in=['cancelled']
            ).order_by('start_time')
            
            # Use the detailed serializer to get full appointment details
            serializer = DetailedAppointmentSerializer(appointments, many=True)
            
            return Response({
                'appointments': serializer.data,
                'date_range': {
                    'from': from_date.strftime('%Y-%m-%d'),
                    'to': to_date.strftime('%Y-%m-%d')
                },
                'employee_id': employee.id
            })
            
        except Exception as e:
            logger.exception(f"Error fetching employee appointments: {str(e)}")
            return Response({
                'error': 'An error occurred while fetching appointments',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 