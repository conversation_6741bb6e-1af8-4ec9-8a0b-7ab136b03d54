import json
import uuid
from datetime import datetime
from rest_framework import status, viewsets, mixins
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JSONParser
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.decorators import action
from django.db import transaction
from django.core.cache import cache
from django.shortcuts import get_object_or_404
from accounts.models import User, Role
from customers.models import CustomerProfile, CustomerTag, CustomerBookingSession
from business.models import BusinessCustomer, Business
from employees.models import Employee
from api.services.import_service import CustomerImportMappingService
from .serializers import CustomerBookingSessionSerializer, BookingSessionDataSerializer
import secrets
import re
from django.conf import settings
from decimal import Decimal
from rest_framework_simplejwt.authentication import JWTAuthentication


class CustomerImportView(APIView):
    """
    API endpoint for importing customer data from Excel files.
    Supports multi-step import process with progress tracking.
    """
    parser_classes = [J<PERSON><PERSON>ars<PERSON>]
    permission_classes = [IsAuthenticated]
    authentication_classes = [JW<PERSON>uthentication]
    
    def post(self, request, *args, **kwargs):
        """Start customer import process"""
        try:
            import_data = request.data
            
            # Debug: Print user and authentication status
            print(f"DEBUG: User authenticated: {request.user.is_authenticated}")
            print(f"DEBUG: User: {request.user}")

            # Get business from request or user
            if request.user.is_authenticated:
                employee = Employee.objects.get(user=request.user)
                business = employee.business
                print(f"DEBUG: Using authenticated user's business: {business.name} (ID: {business.id})")
            else:
                # For testing: accept business_id in payload
                business_id = import_data.get('business_id', 1)  # Default to business 1
                business = Business.objects.get(id=business_id)
                print(f"DEBUG: Using business from payload: {business.name} (ID: {business.id})")

            # Extract import data from request
            import_data = request.data
            customers = import_data.get('customers', [])
            field_mappings = import_data.get('fieldMappings', {})
            
            if not customers:
                return Response({
                    'error': 'No customer data provided'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Allow deterministic job_id during testing (DEBUG only)
            provided_job_id = import_data.get('job_id') or import_data.get('test_job_id')
            if settings.DEBUG and provided_job_id:
                job_id = str(provided_job_id)
            else:
                job_id = str(uuid.uuid4())
            
            # Process customers synchronously for now (could be made async with SQS workers)
            results = self.process_customers(customers, field_mappings, business, job_id)
            
            # Store results in cache for later retrieval
            cache.set(f'import_job_{job_id}', results, timeout=3600)  # 1 hour
            
            return Response({
                'jobId': job_id,
                'status': 'completed',
                'message': 'Import started successfully'
            }, status=status.HTTP_200_OK)
            
        except Employee.DoesNotExist:
            return Response({
                'error': 'Employee profile not found'
            }, status=status.HTTP_403_FORBIDDEN)
        except Exception as e:
            return Response({
                'error': f'Failed to start import: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ImportStatusView(APIView):
    """Get import job status"""
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    
    def get(self, request, job_id, *args, **kwargs):
        """Get status of import job"""
        try:
            # For now, return completed status since we process synchronously
            # In the future, this could check actual job status from SQS/database
            results = cache.get(f'import_job_{job_id}')
            
            if results is None:
                return Response({
                    'error': 'Job not found'
                }, status=status.HTTP_404_NOT_FOUND)
            
            return Response({
                'jobId': job_id,
                'status': 'completed',
                'progress': 100,
                'processedRows': results.get('imported_count', 0) + results.get('error_count', 0),
                'totalRows': results.get('total_count', 0)
            })
            
        except Exception as e:
            return Response({
                'error': f'Failed to get status: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ImportResultsView(APIView):
    """Get import job results"""
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    
    def get(self, request, job_id, *args, **kwargs):
        """Get results of completed import job"""
        try:
            results = cache.get(f'import_job_{job_id}')
            
            if results is None:
                return Response({
                    'error': 'Results not found'
                }, status=status.HTTP_404_NOT_FOUND)
            
            return Response(results)
            
        except Exception as e:
            return Response({
                'error': f'Failed to get results: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class FieldMappingSuggestionsView(APIView):
    """Get field mapping suggestions for customer import"""
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    
    def post(self, request, *args, **kwargs):
        """Get suggested field mappings based on available columns"""
        try:
            available_columns = request.data.get('columns', [])
            
            if not available_columns:
                return Response({
                    'error': 'No columns provided'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            mapping_service = CustomerImportMappingService()
            
            # Get auto-mapping suggestions
            auto_mapping = mapping_service.auto_map_fields(available_columns)
            
            # Get field validation info
            validation = mapping_service.validate_mapping(auto_mapping, available_columns)
            
            # Get additional suggestions for unmapped fields
            suggestions = mapping_service.get_field_suggestions(available_columns)
            
            return Response({
                'auto_mapping': auto_mapping,
                'validation': validation,
                'suggestions': suggestions,
                'required_fields': mapping_service.get_required_fields(),
                'recommended_fields': mapping_service.get_recommended_fields()
            })
            
        except Exception as e:
            return Response({
                'error': f'Failed to generate suggestions: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CustomerImportService:
    """Service class to handle customer import logic"""
    
    def __init__(self):
        self.mapping_service = CustomerImportMappingService()
    
    def process_customers(self, customers, field_mappings, business, job_id):
        """Process customer data and create records"""
        imported_count = 0
        error_count = 0
        errors = []
        warnings = []
        successful = []
        
        customer_role = Role.objects.get(name=Role.Choices.CUSTOMER)
        
        with transaction.atomic():
            for index, customer_data in enumerate(customers):
                try:
                    # Transform customer data using mapping service
                    mapped_data = self.mapping_service.transform_customer_data(customer_data, field_mappings)
                    
                    # Validate required fields
                    validation_error = self.validate_customer_data(mapped_data, index + 1)
                    if validation_error:
                        errors.append(validation_error)
                        error_count += 1
                        continue
                    
                    email = mapped_data['email'].strip().lower()
                    
                    # Check if user already exists
                    existing_user = User.objects.filter(email=email).first()
                    if existing_user:
                        # Check if this user is already linked to the *same* business
                        if BusinessCustomer.objects.filter(business=business, customer__user=existing_user).exists():
                            # TRUE duplicate – already linked → skip
                            warnings.append({
                                'row': index + 1,
                                'email': email,
                                'message': 'Customer already exists in this business, skipping'
                            })
                            continue
                        else:
                            # User exists globally but NOT yet linked to this business → just create relationship
                            # Ensure a customer profile exists and update it with import data
                            if hasattr(existing_user, 'customer_profile'):
                                customer_profile = existing_user.customer_profile
                                # Update profile with import data
                                profile_data = self.extract_profile_data(mapped_data)
                                for field, value in profile_data.items():
                                    if value is not None:  # Only update non-null values
                                        setattr(customer_profile, field, value)
                                customer_profile.save()
                            else:
                                # Create new profile with import data
                                profile_data = self.extract_profile_data(mapped_data)
                                customer_profile = CustomerProfile.objects.create(
                                    user=existing_user,
                                    **profile_data
                                )
                            
                            # Create business-customer link
                            try:
                                business_customer_data = self.extract_business_customer_data(mapped_data, customer_data)
                                business_customer = BusinessCustomer.objects.create(
                                    business=business,
                                    customer=customer_profile,
                                    **business_customer_data
                                )
                            except Exception as bc_error:
                                # Log the specific error for debugging
                                print(f"DEBUG: Error creating BusinessCustomer for existing user {email}: {str(bc_error)}")
                                print(f"DEBUG: Business customer data: {business_customer_data}")
                                raise bc_error
                            
                            # Add customer role if missing
                            if not existing_user.roles.filter(id=customer_role.id).exists():
                                existing_user.roles.add(customer_role)
                            
                            successful.append({
                                'email': email,
                                'name': f"{existing_user.first_name} {existing_user.last_name}".strip(),
                                'id': business_customer.id
                            })
                            imported_count += 1
                            continue
                    
                    # ---- NEW customer path ----
                    # Create user
                    user_data = self.extract_user_data(mapped_data)
                    user = User.objects.create_customer_user(**user_data)
                    user.roles.add(customer_role)
                    
                    # Create customer profile
                    profile_data = self.extract_profile_data(mapped_data)
                    customer_profile = CustomerProfile.objects.create(
                        user=user,
                        **profile_data
                    )
                    
                    # Create business customer relationship
                    try:
                        business_customer_data = self.extract_business_customer_data(mapped_data, customer_data)
                        business_customer = BusinessCustomer.objects.create(
                            business=business,
                            customer=customer_profile,
                            **business_customer_data
                        )
                    except Exception as bc_error:
                        # Log the specific error for debugging
                        print(f"DEBUG: Error creating BusinessCustomer for {email}: {str(bc_error)}")
                        print(f"DEBUG: Business customer data: {business_customer_data}")
                        raise bc_error
                    
                    # Debug: Print business customer creation
                    print(f"DEBUG: Created BusinessCustomer ID {business_customer.id} for {email} in business {business.name}")
                    
                    # Handle tags using mapping service
                    self.process_tags(mapped_data, business_customer)
                    
                    successful.append({
                        'email': email,
                        'name': f"{mapped_data.get('first_name', '')} {mapped_data.get('last_name', '')}".strip(),
                        'id': business_customer.id
                    })
                    
                    imported_count += 1
                    
                except Exception as e:
                    import traceback
                    error_message = f'Error creating customer: {str(e)}'
                    if not str(e):  # If the exception string is empty, use the exception type
                        error_message = f'Error creating customer: {type(e).__name__}'
                    
                    print(f"DEBUG: Full error for row {index + 1}: {traceback.format_exc()}")
                    print(f"DEBUG: Mapped data: {mapped_data}")
                    
                    errors.append({
                        'row': index + 1,
                        'message': error_message
                    })
                    error_count += 1
        
        result = {
            'jobId': job_id,
            'status': 'completed',
            'imported_count': imported_count,
            'error_count': error_count,
            'total_count': len(customers),
            'warnings': warnings,
            'errors': errors,
            'successful': successful
        }
        
        # Debug: Print import results
        print(f"DEBUG: Import completed for business {business.name}")
        print(f"DEBUG: Total customers processed: {len(customers)}")
        print(f"DEBUG: Successfully imported: {imported_count}")
        print(f"DEBUG: Errors: {error_count}")
        print(f"DEBUG: Warnings (duplicates): {len(warnings)}")
        
        return result
    
    def validate_customer_data(self, mapped_data, row_number):
        """Validate customer data"""
        # Check required fields
        if not mapped_data.get('email'):
            return {
                'row': row_number,
                'field': 'email',
                'message': 'Email is required'
            }
        
        # Validate email format
        email = mapped_data['email'].strip()
        if not re.match(r'^[^\s@]+@[^\s@]+\.[^\s@]+$', email):
            return {
                'row': row_number,
                'field': 'email',
                'value': email,
                'message': 'Invalid email format'
            }
        
        return None
    
    def extract_user_data(self, mapped_data):
        """Extract user data for User model"""
        temp_password = secrets.token_urlsafe(12)
        
        return {
            'identifier': mapped_data['email'].strip(),
            'email': mapped_data['email'].strip().lower(),
            'phone_number': mapped_data.get('mobile', ''),  # Already cleaned by mapping service
            'first_name': mapped_data.get('first_name', ''),  # Already cleaned by mapping service
            'last_name': mapped_data.get('last_name', ''),   # Already cleaned by mapping service
            'password': temp_password
        }
    
    def extract_profile_data(self, mapped_data):
        """Extract customer profile data"""
        # Check if there's actual credit card info (not just boolean)
        credit_card_info = mapped_data.get('credit_card')
        has_card_on_file = bool(credit_card_info and credit_card_info not in ['---', 'no', 'false', 'none', 'n/a', ''])
        
        return {
            'card_on_file': has_card_on_file,
            
            # General customer information
            'birthdate': mapped_data.get('birthdate'),
            'gender': mapped_data.get('gender'),
            
            # Address information
            'address_street': mapped_data.get('address'),
            'address_apt_suite': mapped_data.get('apt_suite'),
            'address_city': mapped_data.get('city'),
            'address_state': mapped_data.get('state'),
            'address_zip': mapped_data.get('zip'),
        }
    
    def extract_business_customer_data(self, mapped_data, original_data):
        """Extract business customer relationship data"""
        from django.utils import timezone
        
        return {
            # Core data
            'loyalty_points': mapped_data.get('points_earned', 0),
            'opt_in_marketing': True,  # Default for imports
            'email_reminders': True,
            'sms_reminders': True,
            'notes': '',  # Keep clean for employee notes
            
            # Business-specific import fields
            'customer_since': mapped_data.get('customer_since'),
            'last_visited': mapped_data.get('last_visited'),
            'membership_type': mapped_data.get('membership'),
            'referred_by': mapped_data.get('referred_by'),
            
            # Business preferences
            'online_booking_allowed': mapped_data.get('online_booking'),
            'credit_card_info': mapped_data.get('credit_card'),
            
            # Statistics
            'appointments_booked': self._safe_int(mapped_data.get('appointments_booked')),
            'classes_booked': self._safe_int(mapped_data.get('classes_booked')),
            'amount_paid': self._safe_decimal(mapped_data.get('amount_paid')),
            'no_shows_cancellations': self._safe_int(mapped_data.get('no_shows_cancellations')),
            'employee_seen': mapped_data.get('employee_seen'),
            
            # Metadata
            'imported_at': timezone.now(),
            'import_source': getattr(self, 'import_filename', 'unknown'),
        }
    
    def _safe_int(self, value):
        """Convert value to int safely"""
        if value is None:
            return None
        
        # Clean the value first
        str_value = str(value).strip()
        if not str_value or str_value in ['', '---', 'N/A', 'null', 'None']:
            return None
            
        try:
            import re
            # Remove non-numeric characters except decimal point and negative sign
            cleaned_value = re.sub(r'[^\d.-]', '', str_value)
            if not cleaned_value or cleaned_value in ['.', '-', '.-']:
                return None
            return int(float(cleaned_value))
        except (ValueError, TypeError, Exception):
            return None
    
    def _safe_decimal(self, value):
        """Convert value to decimal safely"""
        if value is None:
            return None
        
        # Clean the value first
        str_value = str(value).strip()
        if not str_value or str_value in ['', '---', 'N/A', 'null', 'None']:
            return None
            
        try:
            from decimal import Decimal
            import re
            # Remove currency symbols, commas, and other non-numeric characters except decimal point and negative sign
            cleaned_value = re.sub(r'[^\d.-]', '', str_value)
            if not cleaned_value or cleaned_value in ['.', '-', '.-']:
                return None
            return Decimal(cleaned_value)
        except (ValueError, TypeError, Exception):
            return None
    
    def process_tags(self, mapped_data, business_customer):
        """Process and assign tags"""
        tags_list = mapped_data.get('tags', [])
        if tags_list and isinstance(tags_list, list):
            for tag_name in tags_list:
                # Get or create tag
                tag, created = CustomerTag.objects.get_or_create(
                    name=tag_name,
                    defaults={
                        'description': f'Automatically created during import', 
                        'color': '#007bff'
                    }
                )
                business_customer.tags.add(tag)


# Mix the service into the view
CustomerImportView.process_customers = CustomerImportService().process_customers


class CustomerBookingSessionViewSet(mixins.CreateModelMixin,
                                   mixins.RetrieveModelMixin,
                                   mixins.UpdateModelMixin,
                                   mixins.DestroyModelMixin,
                                   viewsets.GenericViewSet):
    """
    ViewSet for managing customer booking sessions
    Provides endpoints to save/retrieve booking progress data
    """
    serializer_class = CustomerBookingSessionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Return booking sessions for the current user"""
        return CustomerBookingSession.objects.filter(user=self.request.user)

    def convert_decimals_to_strings(self, data):
        """Recursively convert Decimal objects to strings for JSON serialization"""
        if isinstance(data, dict):
            return {key: self.convert_decimals_to_strings(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self.convert_decimals_to_strings(item) for item in data]
        elif isinstance(data, Decimal):
            return str(data)
        else:
            return data

    def get_object(self):
        """Get booking session by business ID for the current user"""
        business_id = self.kwargs.get('pk')
        if not business_id:
            business_id = self.request.query_params.get('business_id')

        if not business_id:
            from rest_framework.exceptions import ValidationError
            raise ValidationError("business_id is required")

        # Get or create booking session for this user and business
        business = get_object_or_404(Business, id=business_id)
        session, created = CustomerBookingSession.objects.get_or_create(
            user=self.request.user,
            business=business,
            defaults={
                'booking_data': {},
                'current_step': 'service-selection'
            }
        )
        return session

    @action(detail=False, methods=['get'], url_path='current')
    def get_current_session(self, request):
        """Get current booking session for a specific business"""
        business_id = request.query_params.get('business_id')
        if not business_id:
            return Response(
                {'error': 'business_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            business = Business.objects.get(id=business_id)

            # Log customer detection
            print(f"🔍 Retrieving booking session:")
            print(f"   User: {request.user.email} (ID: {request.user.id})")
            print(f"   Business: {business.name} (ID: {business.id})")

            # First, clean up expired sessions
            expired_count = CustomerBookingSession.cleanup_expired_sessions()
            if expired_count > 0:
                print(f"🧹 Cleaned up {expired_count} expired booking sessions")

            # Get or create session for this specific user and business
            session, created = CustomerBookingSession.objects.get_or_create(
                user=request.user,
                business=business,
                defaults={
                    'booking_data': {},
                    'current_step': 'service-selection'
                }
            )

            if created:
                print(f"📝 Created new booking session for {request.user.email}")
            else:
                print(f"✅ Found existing booking session for {request.user.email}")
                print(f"   Current step: {session.current_step}")
                print(f"   Data keys: {list(session.booking_data.keys()) if session.booking_data else 'Empty'}")

                # Validate that the session data belongs to the current user
                if session.booking_data.get('customerInfo', {}).get('email'):
                    session_email = session.booking_data['customerInfo']['email']
                    if session_email != request.user.email:
                        print(f"⚠️ Email mismatch in session! Session: {session_email}, User: {request.user.email}")
                        print(f"🧹 Clearing mismatched session data")
                        session.booking_data = {}
                        session.current_step = 'service-selection'
                        session.save()

                # Log consent status for debugging
                consent_data = session.booking_data.get('consentData', {})
                print(f"📋 Consent Status: {session.consent_status}")
                if consent_data.get('consentAgreed'):
                    print(f"✅ User has signed consent - signature present: {bool(consent_data.get('signature'))}")
                    print(f"   Consent timestamp: {consent_data.get('consentTimestamp')}")
                    print(f"   Backend status: {session.consent_status}")
                else:
                    print(f"📝 User has not signed consent yet")
                    print(f"   Backend status: {session.consent_status}")

            serializer = self.get_serializer(session)
            return Response(serializer.data)

        except Business.DoesNotExist:
            return Response(
                {'error': 'Business not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['post'], url_path='update-data')
    def update_booking_data(self, request):
        """Update booking data for current session"""
        business_id = request.data.get('business_id')
        if not business_id:
            return Response(
                {'error': 'business_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            business = Business.objects.get(id=business_id)
            session, created = CustomerBookingSession.objects.get_or_create(
                user=request.user,
                business=business,
                defaults={
                    'booking_data': {},
                    'current_step': 'service-selection'
                }
            )

            print(f"💾 Updating booking session for {request.user.email}")
            print(f"   Business: {business.name}")
            print(f"   Current step: {session.current_step}")

            # Validate the booking data
            data_serializer = BookingSessionDataSerializer(data=request.data.get('booking_data', {}))
            if data_serializer.is_valid():
                # Update the booking data
                old_data = session.booking_data.copy()
                validated_data = data_serializer.validated_data

                # Convert any Decimal values to strings for JSON serialization
                validated_data = self.convert_decimals_to_strings(validated_data)

                session.booking_data.update(validated_data)

                # Update current step if provided
                if 'current_step' in request.data:
                    session.current_step = request.data['current_step']
                    print(f"   New step: {session.current_step}")

                # Check if consent data is being saved and mark as signed
                if 'consentData' in validated_data:
                    consent_data = validated_data['consentData']
                    if consent_data.get('consentAgreed') and consent_data.get('signature'):
                        print(f"🔏 Consent form completed - marking as SIGNED for {request.user.email}")
                        session.mark_consent_signed()

                session.save()

                print(f"✅ Successfully updated booking session for {request.user.email}")
                print(f"   Updated keys: {list(data_serializer.validated_data.keys())}")

                serializer = self.get_serializer(session)
                return Response(serializer.data)
            else:
                print(f"❌ Invalid booking data for {request.user.email}: {data_serializer.errors}")
                return Response(
                    {'error': 'Invalid booking data', 'details': data_serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )

        except Business.DoesNotExist:
            return Response(
                {'error': 'Business not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['delete'], url_path='clear')
    def clear_session(self, request):
        """Clear booking session for a specific business"""
        business_id = request.query_params.get('business_id')
        if not business_id:
            return Response(
                {'error': 'business_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            business = Business.objects.get(id=business_id)
            CustomerBookingSession.objects.filter(
                user=request.user,
                business=business
            ).delete()

            return Response({'message': 'Booking session cleared successfully'})

        except Business.DoesNotExist:
            return Response(
                {'error': 'Business not found'},
                status=status.HTTP_404_NOT_FOUND
            )