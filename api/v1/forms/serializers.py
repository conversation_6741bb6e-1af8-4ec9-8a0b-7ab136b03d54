"""
Forms-related serializers for templates, submissions, and signatures.

This module consolidates all forms-related serializers in one place for better organization.
"""
from rest_framework import serializers
from forms.models import FormTemplate, FormSubmission, Signature


class FormTemplateSerializer(serializers.ModelSerializer):
    """
    Serializer for FormTemplate model.
    Handles form template creation, updates, and retrieval.
    """
    
    class Meta:
        model = FormTemplate
        fields = [
            'id', 'name', 'document_type', 'status', 
            'content', 'business', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
        
    def validate_status(self, value):
        """Validate status field"""
        if value not in ['Published', 'Draft']:
            raise serializers.ValidationError("Status must be either 'Published' or 'Draft'")
        return value


class FormSubmissionSerializer(serializers.ModelSerializer):
    """
    Serializer for FormSubmission model.
    Handles form submission creation and retrieval.
    """
    form_template_name = serializers.CharField(source='form_template.name', read_only=True)
    business_name = serializers.CharField(source='business_customer.business.name', read_only=True)
    customer_name = serializers.SerializerMethodField()
    submitted_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = FormSubmission
        fields = [
            'id', 'form_template', 'form_template_name', 'business_customer', 'business_name',
            'customer_name', 'submitted_by', 'submitted_by_name',
            'content', 'status', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
        
    def get_customer_name(self, obj):
        """Get customer display name"""
        if obj.business_customer and obj.business_customer.customer and obj.business_customer.customer.user:
            user = obj.business_customer.customer.user
            full_name = f"{user.first_name} {user.last_name}".strip()
            return full_name or user.email
        return None
        
    def get_submitted_by_name(self, obj):
        """Get submitted by user display name"""
        if obj.submitted_by:
            full_name = f"{obj.submitted_by.first_name} {obj.submitted_by.last_name}".strip()
            return full_name or obj.submitted_by.email
        return None


class SignatureSerializer(serializers.ModelSerializer):
    """
    Serializer for Signature model.
    Handles electronic signature creation and retrieval.
    """
    business_name = serializers.CharField(source='business.name', read_only=True)
    signer_name = serializers.SerializerMethodField()
    form_submission_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Signature
        fields = [
            'id', 'business', 'business_name', 'user', 'signer_type', 
            'customer', 'employee', 'signer_name', 'signature_data', 
            'form_submission', 'form_submission_name', 'ip_address', 
            'user_agent', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
        
    def get_signer_name(self, obj):
        """Get signer display name based on signer type"""
        if obj.signer_type == 'customer' and obj.customer:
            user = obj.customer.user
            full_name = f"{user.first_name} {user.last_name}".strip()
            return full_name or user.email
        elif obj.signer_type == 'employee' and obj.employee:
            user = obj.employee.user
            full_name = f"{user.first_name} {user.last_name}".strip()
            return full_name or user.email
        elif obj.user:
            full_name = f"{obj.user.first_name} {obj.user.last_name}".strip()
            return full_name or obj.user.email
        return None
        
    def get_form_submission_name(self, obj):
        """Get form submission template name"""
        if obj.form_submission and obj.form_submission.form_template:
            return obj.form_submission.form_template.name
        return None
        
    def validate(self, data):
        """
        Validate that either customer or employee is provided based on signer_type
        """
        signer_type = data.get('signer_type')
        customer = data.get('customer')
        employee = data.get('employee')
        
        if signer_type == 'customer' and not customer:
            raise serializers.ValidationError("Customer must be provided for customer signatures")
        
        if signer_type == 'employee' and not employee:
            raise serializers.ValidationError("Employee must be provided for employee signatures")
            
        return data


# Simplified serializers for nested use
class FormTemplateSimpleSerializer(serializers.ModelSerializer):
    """Simplified serializer for nested use in other serializers"""
    
    class Meta:
        model = FormTemplate
        fields = ['id', 'name', 'document_type', 'status']


class FormSubmissionSimpleSerializer(serializers.ModelSerializer):
    """Simplified serializer for nested use in other serializers"""
    form_template_name = serializers.CharField(source='form_template.name', read_only=True)
    
    class Meta:
        model = FormSubmission
        fields = ['id', 'form_template_name', 'status', 'created_at']


class SignatureSimpleSerializer(serializers.ModelSerializer):
    """Simplified serializer for nested use in other serializers"""
    signer_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Signature
        fields = ['id', 'signer_type', 'signer_name', 'created_at']
        
    def get_signer_name(self, obj):
        """Get signer display name"""
        if obj.signer_type == 'customer' and obj.customer:
            return str(obj.customer)
        elif obj.signer_type == 'employee' and obj.employee:
            return str(obj.employee)
        return str(obj.user)
