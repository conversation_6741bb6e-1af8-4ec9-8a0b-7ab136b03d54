# Django AWS Integration Guide

This document explains how your Django server integrates with the AWS infrastructure deployed by this CDK stack. The infrastructure provides scalable file storage, message queuing, and notification services.

## 🏗️ Infrastructure Overview

The CDK stack deploys the following AWS services:

```mermaid
graph TB
    Django[Django Server]
    S3[S3 Bucket<br/>File Storage]
    SQS[SQS Queue<br/>Message Queue]
    DLQ[Dead Letter Queue<br/>Failed Messages]
    SNS[SNS Topic<br/>Notifications]
    
    Django -->|Upload Files| S3
    Django -->|Send Messages| SQS
    Django -->|Process Messages| SQS
    SQS -->|Failed Messages| DLQ
    Django -->|Send Notifications| SNS
    SNS -->|Email Alerts| Email[Email Recipients]
```

## 📦 AWS Components

### 1. S3 Bucket (`ChatbookBackendBucket`)
**Purpose**: Secure storage for uploaded files (Excel, CSV, etc.)

**Features**:
- Versioning enabled
- Server-side encryption (S3-managed)
- Block public access
- Auto-delete for development environment

**Django Integration**:
```python
import boto3
from django.conf import settings

# S3 client setup
s3_client = boto3.client('s3', region_name='us-west-2')
BUCKET_NAME = 'chatbook-backend-imports-dev-{account}-{region}'

# Upload file to S3
def upload_file_to_s3(file_obj, file_key):
    try:
        s3_client.upload_fileobj(
            file_obj,
            BUCKET_NAME,
            file_key,
            ExtraArgs={
                'ServerSideEncryption': 'AES256',
                'ContentType': file_obj.content_type
            }
        )
        return f"s3://{BUCKET_NAME}/{file_key}"
    except Exception as e:
        logger.error(f"S3 upload failed: {e}")
        return None

# Download file from S3
def download_file_from_s3(file_key):
    try:
        response = s3_client.get_object(Bucket=BUCKET_NAME, Key=file_key)
        return response['Body'].read()
    except Exception as e:
        logger.error(f"S3 download failed: {e}")
        return None
```

### 2. SQS Queue (`ProcessFileQueue`)
**Purpose**: Asynchronous message queue for file processing tasks

**Features**:
- 4-day message retention
- 5-minute visibility timeout
- Dead letter queue integration
- Up to 5 retry attempts

**Django Integration**:
```python
import boto3
import json
from django.conf import settings

# SQS client setup
sqs_client = boto3.client('sqs', region_name='us-west-2')
QUEUE_URL = 'https://sqs.us-west-2.amazonaws.com/{account}/ProcessFileQueue'

# Send message to queue
def queue_file_processing(file_id, file_path, user_id, processing_type='import'):
    message_body = {
        'file_id': file_id,
        'file_path': file_path,
        'user_id': user_id,
        'processing_type': processing_type,
        'timestamp': datetime.utcnow().isoformat(),
        'retry_count': 0
    }
    
    try:
        response = sqs_client.send_message(
            QueueUrl=QUEUE_URL,
            MessageBody=json.dumps(message_body),
            MessageAttributes={
                'file_type': {
                    'StringValue': processing_type,
                    'DataType': 'String'
                },
                'user_id': {
                    'StringValue': str(user_id),
                    'DataType': 'String'
                }
            }
        )
        return response['MessageId']
    except Exception as e:
        logger.error(f"Failed to queue message: {e}")
        return None

# Process messages from queue (background task)
def process_queue_messages():
    try:
        response = sqs_client.receive_message(
            QueueUrl=QUEUE_URL,
            MaxNumberOfMessages=10,
            WaitTimeSeconds=20,  # Long polling
            MessageAttributeNames=['All']
        )
        
        messages = response.get('Messages', [])
        for message in messages:
            try:
                # Process the message
                body = json.loads(message['Body'])
                process_file_task(body)
                
                # Delete message after successful processing
                sqs_client.delete_message(
                    QueueUrl=QUEUE_URL,
                    ReceiptHandle=message['ReceiptHandle']
                )
            except Exception as e:
                logger.error(f"Message processing failed: {e}")
                # Message will be retried or sent to DLQ
                
    except Exception as e:
        logger.error(f"Queue polling failed: {e}")
```

### 3. Dead Letter Queue (`ProcessFileDLQ`)
**Purpose**: Stores messages that failed processing after 5 attempts

**Features**:
- 14-day retention period
- Manual inspection and reprocessing

**Django Integration**:
```python
# Monitor dead letter queue
def check_failed_messages():
    DLQ_URL = 'https://sqs.us-west-2.amazonaws.com/{account}/ProcessFileDLQ'
    
    try:
        response = sqs_client.receive_message(
            QueueUrl=DLQ_URL,
            MaxNumberOfMessages=10
        )
        
        failed_messages = response.get('Messages', [])
        for message in failed_messages:
            # Log failed message for manual review
            body = json.loads(message['Body'])
            logger.error(f"Failed processing: {body}")
            
            # Optionally notify administrators
            send_admin_notification(f"File processing failed: {body['file_id']}")
            
    except Exception as e:
        logger.error(f"DLQ check failed: {e}")
```

### 4. SNS Topic (`ImportFailureTopic`)
**Purpose**: Notification system for processing failures and alerts

**Django Integration**:
```python
import boto3

# SNS client setup
sns_client = boto3.client('sns', region_name='us-west-2')
TOPIC_ARN = 'arn:aws:sns:us-west-2:{account}:ImportFailureTopic'

# Send notification
def send_failure_notification(file_id, error_message, user_email=None):
    message = {
        'file_id': file_id,
        'error': error_message,
        'timestamp': datetime.utcnow().isoformat(),
        'severity': 'ERROR'
    }
    
    try:
        response = sns_client.publish(
            TopicArn=TOPIC_ARN,
            Message=json.dumps(message),
            Subject=f'File Processing Failed - {file_id}',
            MessageAttributes={
                'file_id': {
                    'DataType': 'String',
                    'StringValue': str(file_id)
                },
                'severity': {
                    'DataType': 'String',
                    'StringValue': 'ERROR'
                }
            }
        )
        return response['MessageId']
    except Exception as e:
        logger.error(f"SNS notification failed: {e}")
        return None
```

## 🔧 Django Settings Configuration

Add these settings to your Django configuration:

```python
# settings.py
import os

# AWS Configuration
AWS_REGION = 'us-west-2'
AWS_S3_BUCKET_NAME = os.environ.get('S3_BUCKET_NAME', 'chatbook-backend-imports-dev-{account}-{region}')
AWS_SQS_QUEUE_URL = os.environ.get('SQS_QUEUE_URL', 'https://sqs.us-west-2.amazonaws.com/{account}/ProcessFileQueue')
AWS_SNS_TOPIC_ARN = os.environ.get('SNS_TOPIC_ARN', 'arn:aws:sns:us-west-2:{account}:ImportFailureTopic')

# AWS Credentials (use IAM roles in production)
AWS_ACCESS_KEY_ID = os.environ.get('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')

# Celery configuration for background processing
CELERY_BROKER_URL = 'redis://localhost:6379/0'  # or your preferred broker
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
```

## 🚀 Django Implementation Examples

### File Upload View
```python
from django.views import View
from django.http import JsonResponse
import uuid

class FileUploadView(View):
    def post(self, request):
        uploaded_file = request.FILES.get('file')
        if not uploaded_file:
            return JsonResponse({'error': 'No file provided'}, status=400)
        
        # Generate unique file key
        file_key = f"uploads/{uuid.uuid4()}/{uploaded_file.name}"
        
        # Upload to S3
        s3_url = upload_file_to_s3(uploaded_file, file_key)
        if not s3_url:
            return JsonResponse({'error': 'Upload failed'}, status=500)
        
        # Create database record
        file_record = FileUpload.objects.create(
            user=request.user,
            filename=uploaded_file.name,
            s3_key=file_key,
            s3_url=s3_url,
            status='uploaded'
        )
        
        # Queue for processing
        message_id = queue_file_processing(
            file_id=file_record.id,
            file_path=file_key,
            user_id=request.user.id
        )
        
        if message_id:
            file_record.status = 'queued'
            file_record.save()
        
        return JsonResponse({
            'file_id': file_record.id,
            'status': file_record.status,
            'message_id': message_id
        })
```

### Background Processing Task (Celery)
```python
from celery import shared_task
import time

@shared_task
def poll_sqs_queue():
    """Celery task to continuously poll SQS queue"""
    while True:
        try:
            process_queue_messages()
            time.sleep(5)  # Poll every 5 seconds
        except Exception as e:
            logger.error(f"Queue processing error: {e}")
            time.sleep(30)  # Back off on error

@shared_task
def process_file_task(message_data):
    """Process individual file from queue message"""
    file_id = message_data['file_id']
    file_path = message_data['file_path']
    
    try:
        # Update status
        file_record = FileUpload.objects.get(id=file_id)
        file_record.status = 'processing'
        file_record.save()
        
        # Download file from S3
        file_content = download_file_from_s3(file_path)
        if not file_content:
            raise Exception("Failed to download file from S3")
        
        # Process file (your business logic here)
        results = process_csv_or_excel(file_content, file_record.filename)
        
        # Update with results
        file_record.status = 'completed'
        file_record.processed_records = results['processed_count']
        file_record.error_records = results['error_count']
        file_record.save()
        
    except Exception as e:
        # Handle failure
        file_record.status = 'failed'
        file_record.error_message = str(e)
        file_record.save()
        
        # Send failure notification
        send_failure_notification(file_id, str(e))
        
        logger.error(f"File processing failed for {file_id}: {e}")
```

## 🔐 IAM Permissions Required

Your Django application needs these AWS permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject"
            ],
            "Resource": "arn:aws:s3:::chatbook-backend-imports-dev-*/*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "sqs:SendMessage",
                "sqs:ReceiveMessage",
                "sqs:DeleteMessage",
                "sqs:GetQueueAttributes"
            ],
            "Resource": [
                "arn:aws:sqs:us-west-2:*:ProcessFileQueue",
                "arn:aws:sqs:us-west-2:*:ProcessFileDLQ"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "sns:Publish"
            ],
            "Resource": "arn:aws:sns:us-west-2:*:ImportFailureTopic"
        }
    ]
}
```

## 📊 Monitoring and Logging

### Django Logging Configuration
```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'aws_integration.log',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'aws_integration': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### Health Check Endpoints
```python
from django.http import JsonResponse
from django.views import View

class AWSHealthCheckView(View):
    def get(self, request):
        health_status = {
            's3_accessible': self.check_s3_access(),
            'sqs_accessible': self.check_sqs_access(),
            'sns_accessible': self.check_sns_access(),
        }
        
        all_healthy = all(health_status.values())
        status_code = 200 if all_healthy else 503
        
        return JsonResponse(health_status, status=status_code)
    
    def check_s3_access(self):
        try:
            s3_client.head_bucket(Bucket=BUCKET_NAME)
            return True
        except:
            return False
    
    def check_sqs_access(self):
        try:
            sqs_client.get_queue_attributes(QueueUrl=QUEUE_URL)
            return True
        except:
            return False
    
    def check_sns_access(self):
        try:
            sns_client.get_topic_attributes(TopicArn=TOPIC_ARN)
            return True
        except:
            return False
```

## 🚀 Deployment

After deploying the CDK stack, get the output values:

```bash
# Deploy CDK stack
cdk deploy

# Get stack outputs
aws cloudformation describe-stacks --stack-name AwsCdkChatbookBackendStack --query 'Stacks[0].Outputs'
```

Use the outputs in your Django environment:
- `UploadedImportsBucketName` → `S3_BUCKET_NAME`
- `ProcessFileQueueUrl` → `SQS_QUEUE_URL`
- `ImportFailureTopicArn` → `SNS_TOPIC_ARN`

## 📚 Next Steps

1. **Install Dependencies**: `pip install boto3 celery redis`
2. **Configure AWS Credentials**: Use IAM roles or environment variables
3. **Set up Celery**: Configure background task processing
4. **Test Integration**: Use health check endpoints
5. **Monitor**: Set up CloudWatch dashboards for queue metrics

This infrastructure provides a robust foundation for scalable file processing with your Django application! 