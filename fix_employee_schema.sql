-- Disable foreign key checks temporarily
PRAG<PERSON> foreign_keys=OFF;

-- Check if employee table has the correct schema
PRAGMA table_info(employees_employee);

-- Create a new table with the correct schema
CREATE TABLE IF NOT EXISTS new_employees_employee (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    stylist_level VARCHAR(20) NOT NULL,
    profile_image VARCHAR(100) NULL,
    calendar_sync_enabled BOOL NOT NULL,
    calendar_provider VARCHAR(50) NULL,
    calendar_id VARCHAR(255) NULL,
    accept_online_bookings BOOL NOT NULL,
    employee_type VARCHAR(20) NOT NULL,
    access_level VARCHAR(20) NOT NULL,
    is_active BOOL NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    business_id INTEGER NOT NULL REFERENCES business_business (id),
    user_id CHAR(32) NOT NULL REFERENCES accounts_user (id)
);

-- Copy data from old table to new table (if old table exists)
INSERT OR IGNORE INTO new_employees_employee 
SELECT 
    id, 
    stylist_level, 
    profile_image, 
    calendar_sync_enabled, 
    calendar_provider, 
    calendar_id, 
    accept_online_bookings, 
    employee_type, 
    access_level, 
    is_active, 
    created_at, 
    updated_at, 
    business_id, 
    user_id 
FROM employees_employee;

-- Drop old table and rename new table
DROP TABLE IF EXISTS employees_employee;
ALTER TABLE new_employees_employee RENAME TO employees_employee;

-- Create indexes
CREATE INDEX IF NOT EXISTS employee_active_idx ON employees_employee (is_active);
CREATE INDEX IF NOT EXISTS employee_level_idx ON employees_employee (stylist_level);

-- Create unique constraint
CREATE UNIQUE INDEX IF NOT EXISTS unique_employee_per_business ON employees_employee (business_id, user_id);

-- Re-enable foreign keys
PRAGMA foreign_keys=ON;

-- Verify the schema
PRAGMA table_info(employees_employee); 