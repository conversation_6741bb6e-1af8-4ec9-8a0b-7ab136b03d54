from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.views.generic.edit import CreateView, UpdateView
from django.urls import reverse_lazy

from .models import Appointment, AppointmentService
from .forms import AppointmentForm

# Create your views here.

class AppointmentCreateView(CreateView):
    model = Appointment
    form_class = AppointmentForm
    success_url = reverse_lazy('admin:appointments_appointment_changelist')
    
    def form_valid(self, form):
        # Always set source to 'admin'
        form.instance.source = 'admin'
        return super().form_valid(form)
    
    def form_invalid(self, form):
        # Print errors for debugging
        print("Form errors:", form.errors)
        return super().form_invalid(form)
