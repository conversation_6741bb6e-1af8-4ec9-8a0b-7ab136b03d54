from django import forms
from django.utils.translation import gettext_lazy as _
from django.forms.widgets import SplitDateTimeWidget
from datetime import datetime, timedelta

from .models import Appointment, AppointmentService, RecurringPattern

class AppointmentSourceField(forms.CharField):
    """Custom CharField that always provides 'admin' as default value."""
    
    def clean(self, value):
        """Override clean to ensure value is 'admin' if not provided."""
        if not value:
            return 'admin'
        return super().clean(value)

class AppointmentForm(forms.ModelForm):
    """Form for Appointment model with guaranteed source field."""
    
    # Override the source field to force a default value
    source = AppointmentSourceField(
        initial='admin',
        required=False,
        widget=forms.HiddenInput()
    )
    
    class Meta:
        model = Appointment
        fields = [
            'customer', 'employee', 'start_time', 'status', 
            'payment_status', 'notes_from_customer', 
            'cancellation_reason', 'recurrence'
        ]
        widgets = {
            'notes_from_customer': forms.Textarea(attrs={'rows': 3}),
            'cancellation_reason': forms.Textarea(attrs={'rows': 3})
        }
    
    def __init__(self, *args, **kwargs):
        # Initialize with source=admin in data
        data = kwargs.get('data')
        if data:
            data = data.copy()
            data['source'] = 'admin'
            kwargs['data'] = data
            
        super().__init__(*args, **kwargs)
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.source = 'admin'  # Force 'admin' value
        
        if commit:
            instance.save()
            self.save_m2m()
        
        return instance 

class AppointmentAdminForm(forms.ModelForm):
    """
    Custom form for Appointment admin with improved datetime handling
    """
    start_datetime = forms.SplitDateTimeField(
        label=_("Start Date and Time"),
        widget=SplitDateTimeWidget(
            date_attrs={'type': 'date', 'class': 'appointment-date'},
            time_attrs={'type': 'time', 'class': 'appointment-time'},
        ),
        help_text=_("The date and time when the appointment starts")
    )
    
    class Meta:
        model = Appointment
        fields = [
            'customer', 'employee', 'start_datetime', 'status', 
            'payment_status', 'source', 'notes_from_customer', 
            'cancellation_reason', 'recurrence'
        ]
        widgets = {
            'customer': forms.Select(attrs={'class': 'customer-select'}),
            'employee': forms.Select(attrs={'class': 'employee-select'}),
            'status': forms.Select(attrs={'class': 'status-select'}),
            'payment_status': forms.Select(attrs={'class': 'payment-status-select'}),
            'source': forms.Select(attrs={'class': 'source-select'}),
            'notes_from_customer': forms.Textarea(attrs={'rows': 3, 'class': 'notes-field'}),
            'cancellation_reason': forms.Textarea(attrs={'rows': 3, 'class': 'cancellation-field'})
        }
    
    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance')
        if instance and instance.pk:
            # If editing an existing instance, initialize start_datetime
            initial = kwargs.get('initial', {})
            initial['start_datetime'] = instance.start_time
            kwargs['initial'] = initial
        
        super().__init__(*args, **kwargs)
        
        # Change label for customer field
        self.fields['customer'].label = _("Customer")
        
        # Make customer field autocomplete-able
        if 'customer' in self.fields:
            self.fields['customer'].widget.attrs.update({
                'class': 'select2',
                'data-placeholder': _("Select a customer")
            })
    
    def clean(self):
        cleaned_data = super().clean()
        
        # Get the start_datetime from the form
        start_datetime = cleaned_data.get('start_datetime')
        if start_datetime:
            # Set the start_time to the datetime value
            cleaned_data['start_time'] = start_datetime
        
        # Check for schedule conflicts
        employee = cleaned_data.get('employee')
        start_time = cleaned_data.get('start_time')
        
        if employee and start_time:
            # Check if this appointment would create a conflict
            instance = self.instance
            if instance.pk:
                # For existing appointments, exclude this one from the conflict check
                conflicts = Appointment.objects.filter(
                    employee=employee,
                    start_time__lt=start_time + timedelta(hours=2),  # Approximate end time
                    start_time__gt=start_time - timedelta(hours=2)   # Allow for appointments close to this one
                ).exclude(pk=instance.pk)
            else:
                # For new appointments, check all existing ones
                conflicts = Appointment.objects.filter(
                    employee=employee,
                    start_time__lt=start_time + timedelta(hours=2),
                    start_time__gt=start_time - timedelta(hours=2)
                )
            
            # Note: this is a simplistic conflict check
            # In real production code, we would check the actual duration of each appointment
            if conflicts.exists():
                self.add_warning(
                    _("Warning: There are other appointments scheduled close to this time for this employee.")
                )
        
        return cleaned_data
    
    def add_warning(self, message):
        """
        Add a warning message to the form without preventing submission
        """
        if not hasattr(self, 'warnings'):
            self.warnings = []
        self.warnings.append(message) 