from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
from datetime import timedelta
import logging
import json
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey

# Use string references to avoid circular imports
# from employees.models import Employee
# from services.models import Service, AddOn

User = get_user_model()
logger = logging.getLogger(__name__)

class RecurringPattern(models.Model):
    FREQUENCY_CHOICES = [
        ('DAILY', 'Daily'),
        ('WEEKLY', 'Weekly'),
        ('MONTHLY', 'Monthly'),
    ]
    
    frequency = models.CharField(max_length=10, choices=FREQUENCY_CHOICES, default='WEEKLY')
    interval = models.IntegerField(default=1)  # e.g., every 2 weeks
    by_day = models.CharField(max_length=100, blank=True, default="--")  # For weekly patterns
    start_date = models.DateField(auto_now_add=True)
    end_date = models.DateField(null=True, blank=True)

    def __str__(self):
        return f"{self.get_frequency_display()} - {self.start_date}"

class Appointment(models.Model):
    """
    Consolidated model for appointments/bookings.
    """
    STATUS_CHOICES = [
        ('requested', 'Requested'),
        ('confirmed', 'Confirmed'),
        ('accepted', 'Accepted'),
        ('checked_in', 'Checked In'),
        ('service_started', 'Service Started'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('no_show', 'No Show'),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('unpaid', 'Unpaid'),
        ('paid', 'Paid'),
        ('refunded', 'Refunded'),
    ]
    
    SOURCE_CHOICES = [
        ('admin', 'Admin Interface'),
        ('online', 'Online Booking'),
    ]
    
    id = models.BigAutoField(primary_key=True)
    customer = models.ForeignKey(
        'business.BusinessCustomer',
        on_delete=models.PROTECT,
        related_name='appointments'
    )
    employee = models.ForeignKey(
        'employees.Employee',
        on_delete=models.PROTECT,
        related_name='appointments'
    )
    services = models.ManyToManyField(
        'services.Service',
        through='AppointmentService',
        related_name='appointments'
    )
    start_time = models.DateTimeField(
        help_text="Start time of the appointment"
    )
    # end_time is now a computed property rather than a field
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='requested'
    )
    payment_status = models.CharField(
        max_length=20,
        choices=PAYMENT_STATUS_CHOICES,
        default='unpaid'
    )
    source = models.CharField(
        max_length=20,
        choices=SOURCE_CHOICES,
        default='admin',
        help_text="Source of the appointment booking"
    )
    notes_from_customer = models.TextField(
        blank=True,
        help_text="Any special notes from the customer for this appointment"
    )
    cancellation_reason = models.TextField(
        blank=True,
        help_text="Reason for cancellation if appointment was cancelled"
    )
    recurrence = models.ForeignKey(
        RecurringPattern,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='appointments'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Appointment"
        verbose_name_plural = "Appointments"
        ordering = ['-start_time']
        indexes = [
            models.Index(fields=['start_time'], name='appointment_start_time_idx'),
            models.Index(fields=['employee', 'start_time'], name='appointment_employee_time_idx'),
            models.Index(fields=['customer', 'start_time'], name='appt_customer_time_idx'),
            models.Index(fields=['status'], name='appointment_status_idx'),
        ]

    def __str__(self):
        return f"{self.customer} - {self.start_time}"

    @property
    def business(self):
        """
        Get the business associated with this appointment.
        
        The business is determined by the employee's business, which should match
        the customer's business. This is validated in the AppointmentSerializer.
        """
        return self.employee.business if self.employee else None
        
    def clean(self):
        """Validate appointment data"""
        # No need to validate end_time vs start_time, as end_time is now computed
        super().clean()
    
    @property
    def total_price(self):
        """Get the total price for all services in this appointment"""
        service_total = sum(service.final_price for service in self.appointment_services.all())
        addon_total = sum(addon.add_on_price for addon in self.appointment_add_ons.all())
        return service_total + addon_total

    @property
    def total_duration(self):
        """
        Get the total duration in minutes for all services in this appointment
        including service durations and buffer times
        """
        # Get service durations including buffer times
        service_duration = 0
        for app_service in self.appointment_services.all():
            # Add the service duration (which already accounts for quantity)
            service_duration += app_service.total_duration
            # Add buffer time for each service, using the appointment-specific buffer_time
            service_duration += app_service.buffer_time
        
        # Add durations for any add-ons
        addon_duration = 0
        for app_addon in self.appointment_add_ons.all():
            # Use the add-on's total_duration property (which accounts for overrides)
            addon_duration += app_addon.total_duration
        
        return service_duration + addon_duration
    
    @property
    def end_time(self):
        """
        Calculate the end time based on start time plus total duration
        following the API logic for calculating service and add-on durations
        """
        if not self.start_time:
            return None
            
        # Calculate total minutes from all services and add-ons
        total_minutes = self.total_duration
        
        # Add the duration to start_time
        return self.start_time + timedelta(minutes=total_minutes)
    
    def check_employee_conflicts(self):
        """Check for overlapping appointments for the same employee"""
        # Since end_time is now a computed property, we need to calculate conflict differently
        if not self.start_time:
            return False

        # Calculate the end time for this appointment
        end_time = self.end_time
            
        conflicts = Appointment.objects.filter(
            employee=self.employee,
            status__in=['requested', 'confirmed', 'in_progress'],
        ).exclude(pk=self.pk)

        # Filter for appointments that overlap with our time range
        # An appointment overlaps if:
        # 1. The other appointment starts before our end time AND
        # 2. The other appointment ends after our start time
        for appointment in conflicts:
            other_start = appointment.start_time
            other_end = appointment.end_time
            
            if other_start < end_time and other_end > self.start_time:
                return True
                
        return False

    def cancel(self, reason=""):
        """Cancel this appointment"""
        self.status = 'cancelled'
        self.cancellation_reason = reason
        self.save()

    def mark_as_no_show(self):
        """Mark this appointment as no show"""
        self.status = 'no_show'
        self.save()

    def confirm(self):
        """Confirm this appointment"""
        self.status = 'confirmed'
        self.save()

    def handle_status_on_reschedule(self, new_start_time):
        """
        Handle status change when appointment is rescheduled.
        If appointment is confirmed and moved to a future date, change status to accepted.
        """
        if self.status == 'confirmed':
            from utils.timezone_utils import get_business_timezone, now_in_timezone
            from django.utils import timezone

            # Get business timezone for proper date comparison
            business = self.employee.business
            business_tz = get_business_timezone(business)
            now_business = now_in_timezone(business_tz)

            # Convert new start time to business timezone for comparison
            if new_start_time.tzinfo is None:
                # If naive datetime, assume UTC
                new_start_time = new_start_time.replace(tzinfo=timezone.utc)

            new_start_business = new_start_time.astimezone(business_tz)

            # If the new time is in the future, change status to accepted
            if new_start_business > now_business:
                self.status = 'accepted'
                return True  # Status was changed

        return False  # Status was not changed

    def complete(self):
        """Mark this appointment as completed"""
        self.status = 'completed'
        self.save()

    def save(self, *args, **kwargs):
        """Override save to handle default values"""
        # Ensure source has a value
        if not self.source:
            self.source = 'admin'
            
        super().save(*args, **kwargs)

class AppointmentService(models.Model):
    """
    Junction table for appointments and services, allowing multiple services per appointment
    with quantity and price tracking.
    """
    appointment = models.ForeignKey(
        Appointment,
        on_delete=models.CASCADE,
        related_name='appointment_services'
    )
    service = models.ForeignKey(
        'services.Service',
        on_delete=models.PROTECT,
        related_name='appointment_services'
    )
    employee_service = models.ForeignKey(
        'services.EmployeeService',
        on_delete=models.SET_NULL,
        null=True,
        related_name='appointment_services'
    )
    quantity = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        help_text="Number of times this service is booked"
    )
    base_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Original price of the service"
    )
    price_override = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Override price for this service (if different from base price)"
    )
    duration = models.PositiveIntegerField(
        help_text="Duration in minutes for this service"
    )
    buffer_time = models.PositiveIntegerField(
        help_text="Buffer time in minutes after this service",
        default=0
    )
    notes = models.TextField(
        blank=True,
        help_text="Any special notes for this service"
    )
    
    class Meta:
        verbose_name = "Appointment Service"
        verbose_name_plural = "Appointment Services"
        ordering = ['appointment', 'service']
    
    def __str__(self):
        return f"{self.service.name} x{self.quantity} for {self.appointment}"
    
    @property
    def final_price(self):
        """Get the final price for this service"""
        if self.price_override:
            return self.price_override * self.quantity
        return self.base_price * self.quantity
    
    @property
    def total_duration(self):
        """Get the total duration for this service"""
        return self.duration * self.quantity
    
    def save(self, *args, **kwargs):
        from django.core.exceptions import ObjectDoesNotExist
        from services.models import EmployeeService, StylistLevelService

        # Check if we should skip automatic pricing calculation
        skip_auto_pricing = kwargs.pop('skip_auto_pricing', False)

        logger.debug(f"[AppointmentService.save] appt={getattr(self, 'appointment_id', None)} svc={getattr(self, 'service_id', None)} skip_auto_pricing={skip_auto_pricing}")

        # Only proceed with automatic pricing if we have the needed relationships and not skipping
        if not skip_auto_pricing and hasattr(self.appointment, 'employee') and self.service:
            emp = self.appointment.employee
            svc = self.service

            try:
                es = EmployeeService.objects.get(employee=emp, service=svc, is_active=True)
                price = es.price
                dur = int(es.duration.total_seconds() // 60)
                self.employee_service = es
                logger.debug(f"[AppointmentService.save] Found EmployeeService with price={price}, duration={dur}")
            except EmployeeService.DoesNotExist:
                # Try stylist-level defaults
                sls = StylistLevelService.objects.filter(
                    business=emp.business,
                    service=svc,
                    stylist_level=emp.stylist_level,
                    is_active=True,
                    is_offered=True
                ).first()

                if sls:
                    price = sls.price
                    dur = int(sls.duration.total_seconds() // 60)
                    logger.debug(f"[AppointmentService.save] Using StylistLevelService with price={price}, duration={dur}")
                else:
                    # Final fallback to service defaults
                    price = svc.base_price
                    dur = int(svc.base_duration.total_seconds() // 60)
                    logger.debug(f"[AppointmentService.save] Using base service with price={price}, duration={dur}")

            # Set the calculated values
            self.base_price = price
            self.duration = dur

            # For buffer_time, only use service default if not explicitly set
            if not self.pk and self.buffer_time == 0:  # New instance with default buffer_time
                self.buffer_time = int(svc.buffer_time.total_seconds() // 60)
                logger.debug(f"[AppointmentService.save] Using service buffer_time={self.buffer_time}")
            else:
                logger.debug(f"[AppointmentService.save] Using custom buffer_time={self.buffer_time}")
        elif skip_auto_pricing:
            logger.debug(f"[AppointmentService.save] Skipping auto pricing - using custom values: price={self.base_price}, duration={self.duration}")

        super().save(*args, **kwargs)

class AppointmentAddOn(models.Model):
    appointment = models.ForeignKey(Appointment, on_delete=models.CASCADE, related_name='appointment_add_ons')
    add_on = models.ForeignKey('services.AddOn', on_delete=models.CASCADE, related_name='appointment_add_ons')
    add_on_price = models.DecimalField(max_digits=10, decimal_places=2)
    duration = models.PositiveIntegerField(
        help_text="Duration in minutes for this add-on",
        default=0
    )
    
    # Track if this is a specific service add-on 
    service_add_on = models.ForeignKey(
        'services.ServiceAddOn',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='appointment_add_ons'
    )
    
    class Meta:
        unique_together = ('appointment', 'add_on')

    def __str__(self):
        return f"{self.appointment} + {self.add_on.name}"
    
    @property
    def total_duration(self):
        """Get the total duration for this add-on in minutes"""
        return self.duration
    
    def save(self, *args, **kwargs):
        from django.core.exceptions import ObjectDoesNotExist
        from services.models import EmployeeAddOn, StylistLevelAddOn

        # Check if we should skip automatic pricing calculation
        skip_auto_pricing = kwargs.pop('skip_auto_pricing', False)

        logger.debug(f"[AppointmentAddOn.save] appt={getattr(self, 'appointment_id', None)} addon={getattr(self, 'add_on_id', None)} skip_auto_pricing={skip_auto_pricing}")

        # Only proceed with automatic pricing if we have the needed relationships and not skipping
        if not skip_auto_pricing and hasattr(self.appointment, 'employee') and self.add_on:
            emp = self.appointment.employee
            addon = self.add_on

            try:
                ea = EmployeeAddOn.objects.get(
                    employee=emp,
                    addon=addon,
                    is_active=True
                )

                price = ea.price
                dur = int(ea.duration.total_seconds() // 60)
                logger.debug(f"[AppointmentAddOn.save] Found EmployeeAddOn with price={price}, duration={dur}")
            except ObjectDoesNotExist:
                # First check for StylistLevelAddOn if employee has a stylist level
                try:
                    if emp.stylist_level:
                        sla = StylistLevelAddOn.objects.filter(
                            business=emp.business,
                            addon=addon,
                            stylist_level=emp.stylist_level,
                            is_active=True
                        ).first()

                        if sla:
                            price = sla.price
                            dur = int(sla.duration.total_seconds() // 60)
                            logger.debug(f"[AppointmentAddOn.save] Using StylistLevelAddOn with price={price}, duration={dur}")
                        else:
                            # Check for service_add_on overrides
                            if self.service_add_on:
                                # Check for price override in ServiceAddOn
                                if self.service_add_on.price_override is not None:
                                    price = self.service_add_on.price_override
                                else:
                                    price = addon.base_price

                                # Check for duration override in ServiceAddOn
                                if self.service_add_on.duration_override:
                                    dur = int(self.service_add_on.duration_override.total_seconds() // 60)
                                else:
                                    dur = int(addon.base_duration.total_seconds() // 60)
                                logger.debug(f"[AppointmentAddOn.save] Using ServiceAddOn overrides with price={price}, duration={dur}")
                            else:
                                # Fallback to add-on defaults
                                price = addon.base_price
                                dur = int(addon.base_duration.total_seconds() // 60)
                                logger.debug(f"[AppointmentAddOn.save] No overrides found, using addon defaults: price={price}, duration={dur}")
                    else:
                        # Check for service_add_on overrides
                        if self.service_add_on:
                            # Check for price override in ServiceAddOn
                            if self.service_add_on.price_override is not None:
                                price = self.service_add_on.price_override
                            else:
                                price = addon.base_price

                            # Check for duration override in ServiceAddOn
                            if self.service_add_on.duration_override:
                                dur = int(self.service_add_on.duration_override.total_seconds() // 60)
                            else:
                                dur = int(addon.base_duration.total_seconds() // 60)
                            logger.debug(f"[AppointmentAddOn.save] Using ServiceAddOn overrides with price={price}, duration={dur}")
                        else:
                            # Fallback to add-on defaults
                            price = addon.base_price
                            dur = int(addon.base_duration.total_seconds() // 60)
                            logger.debug(f"[AppointmentAddOn.save] No stylist level found, using addon defaults: price={price}, duration={dur}")
                except Exception as e:
                    # Final fallback to add-on defaults
                    price = addon.base_price
                    dur = int(addon.base_duration.total_seconds() // 60)
                    logger.debug(f"[AppointmentAddOn.save] Error checking alternatives: {str(e)}. Using addon defaults: price={price}, duration={dur}")

            # Set the calculated values
            self.add_on_price = price
            self.duration = dur
        elif skip_auto_pricing:
            logger.debug(f"[AppointmentAddOn.save] Skipping auto pricing - using custom values: price={self.add_on_price}, duration={self.duration}")

        super().save(*args, **kwargs)


class AppointmentHistory(models.Model):
    """
    Append-only audit trail for appointment changes.
    Tracks all modifications to appointments with user attribution and change deltas.
    """

    ACTION_CHOICES = [
        ('created', 'Created'),
        ('updated', 'Updated'),
        ('status_changed', 'Status Changed'),
        ('payment_updated', 'Payment Updated'),
        ('service_added', 'Service Added'),
        ('service_removed', 'Service Removed'),
        ('service_modified', 'Service Modified'),
        ('addon_added', 'Add-on Added'),
        ('addon_removed', 'Add-on Removed'),
        ('addon_modified', 'Add-on Modified'),
        ('rescheduled', 'Rescheduled'),
        ('cancelled', 'Cancelled'),
        ('deleted', 'Deleted'),
    ]



    # Core fields
    id = models.BigAutoField(primary_key=True)
    appointment = models.ForeignKey(
        Appointment,
        on_delete=models.CASCADE,
        related_name='history',
        help_text="The appointment this history entry relates to"
    )

    # Change tracking
    action = models.CharField(
        max_length=20,
        choices=ACTION_CHOICES,
        help_text="Type of action performed"
    )

    # User attribution - who made the change
    modified_by = models.CharField(
        max_length=255,
        default="System",
        help_text="Name of the user who made this change"
    )

    # Change details
    field_name = models.CharField(
        max_length=100,
        blank=True,
        help_text="Name of the field that was changed (if applicable)"
    )

    old_value = models.JSONField(
        null=True,
        blank=True,
        help_text="Previous value before the change"
    )

    new_value = models.JSONField(
        null=True,
        blank=True,
        help_text="New value after the change"
    )

    # Human-readable change description
    change_summary = models.TextField(
        help_text="Human-readable description of what changed"
    )

    # Additional metadata
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text="IP address of the user who made the change"
    )

    user_agent = models.TextField(
        blank=True,
        default='',
        help_text="User agent string of the client that made the change"
    )

    # Timestamps
    modified_at = models.DateTimeField(
        auto_now_add=True,
        help_text="When this change was made"
    )

    class Meta:
        verbose_name = "Appointment History"
        verbose_name_plural = "Appointment History"
        ordering = ['-modified_at']
        indexes = [
            models.Index(fields=['appointment', '-modified_at'], name='appt_history_appt_time_idx'),
            models.Index(fields=['action', '-modified_at'], name='appt_history_action_time_idx'),
            models.Index(fields=['modified_by', '-modified_at'], name='appt_history_user_time_idx'),
        ]

    def __str__(self):
        user_info = self.get_user_display()
        return f"Appointment {self.appointment.id} - {self.action} by {user_info} at {self.modified_at}"

    def get_user_display(self):
        """Get a human-readable representation of who made the change"""
        return self.modified_by

    def get_change_details(self):
        """Get detailed information about what changed"""
        details = {
            'action': self.get_action_display(),
            'field': self.field_name,
            'old_value': self.old_value,
            'new_value': self.new_value,
            'summary': self.change_summary,
            'timestamp': self.modified_at,
            'user': self.get_user_display(),
        }
        return details
