from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from django import forms
from django.urls import path
from django.forms.widgets import SplitDateTimeWidget
from django.core.exceptions import ValidationError
from datetime import datetime
from .models import Appointment, AppointmentService, AppointmentAddOn, RecurringPattern, AppointmentHistory
from .forms import AppointmentForm

class AppointmentServiceInlineForm(forms.ModelForm):
    class Meta:
        model = AppointmentService
        fields = ('service', 'quantity', 'duration', 'base_price', 'price_override', 'notes')
        widgets = {
            'service': forms.Select(attrs={'class': 'service-select'}),
            'quantity': forms.NumberInput(attrs={'min': 1, 'class': 'quantity-input'}),
            'notes': forms.Textarea(attrs={'rows': 2, 'cols': 40})
        }
        error_messages = {
            'service': {
                'required': 'Please select a service - this is required.',
            },
        }
    
    def clean(self):
        cleaned_data = super().clean()
        service = cleaned_data.get('service')
        
        if not service:
            raise forms.ValidationError("Service is required")
        
        return cleaned_data

class AppointmentServiceInline(admin.TabularInline):
    model = AppointmentService
    form = AppointmentServiceInlineForm
    extra = 1
    min_num = 1  # Require at least one service
    validate_min = True
    fields = ('service', 'quantity', 'duration', 'base_price', 'price_override', 'notes')
    readonly_fields = ('duration', 'base_price')
    
    class Media:
        js = ('js/appointment_services.js',)
    
    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        formset.validate_min = True  # Enforce minimum number of forms
        return formset

class AppointmentAddOnInlineForm(forms.ModelForm):
    class Meta:
        model = AppointmentAddOn
        fields = ('add_on', 'add_on_price', 'duration')
        widgets = {
            'add_on': forms.Select(attrs={'class': 'addon-select'})
        }

class AppointmentAddOnInline(admin.TabularInline):
    model = AppointmentAddOn
    form = AppointmentAddOnInlineForm
    extra = 0
    fields = ('add_on', 'add_on_price', 'duration')
    readonly_fields = ('add_on_price', 'duration')

class AppointmentAdminForm(AppointmentForm):
    # Use SplitDateTimeField instead of the model's DateTimeField for better form handling
    start_datetime = forms.SplitDateTimeField(
        label="Start time",
        widget=SplitDateTimeWidget(
            date_attrs={'class': 'vDateField', 'placeholder': 'YYYY-MM-DD'},
            time_attrs={'class': 'vTimeField', 'placeholder': 'HH:MM'},
            date_format='%Y-%m-%d',
            time_format='%H:%M'
        ),
        help_text="Start time of the appointment (use 24-hour format for time, e.g. 14:30 for 2:30 PM)",
        required=True
    )
    
    class Meta(AppointmentForm.Meta):
        exclude = ['start_time']  # Exclude the model's start_time field
        widgets = {
            'notes_from_customer': forms.Textarea(attrs={'rows': 3, 'cols': 40}),
        }
        help_texts = {
            'employee': 'Select an employee first to enable service selection',
            'customer': 'Select the customer for this appointment',
            'status': 'Current status of the appointment',
            'payment_status': 'Current payment status for this appointment'
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # If editing an existing appointment, initialize the split datetime field
        if self.instance and self.instance.pk and self.instance.start_time:
            self.fields['start_datetime'].initial = self.instance.start_time
    
    def clean(self):
        cleaned_data = super().clean()
        
        # Debug form errors
        if self._errors:
            print("Form validation errors:", self._errors)
        
        # Get the start_datetime from the form
        start_datetime = cleaned_data.get('start_datetime')
        
        if start_datetime:
            # Add it back to cleaned_data under the model field name
            cleaned_data['start_time'] = start_datetime
        
        # Ensure we have employee and customer
        if not cleaned_data.get('employee'):
            self.add_error('employee', 'Employee is required')
        
        if not cleaned_data.get('customer'):
            self.add_error('customer', 'Customer is required')
        
        # Ensure at least one service is provided
        # This will be checked in save_formset but add a warning here
        if hasattr(self, 'is_empty_form') and not self.is_empty_form and not self.data.get('appointmentservice_set-0-service'):
            self._errors['__all__'] = self.error_class(['At least one service must be selected.'])
        
        # Add a notice about form data for debugging
        print("Form Data:", self.data)
        
        return cleaned_data
    
    def save(self, commit=True):
        # Take start_datetime and save it to the model's start_time field
        instance = super().save(commit=False)

        if 'start_datetime' in self.cleaned_data:
            new_start_time = self.cleaned_data['start_datetime']

            # Check if start_time is being updated and handle status change logic
            if instance.pk and instance.start_time != new_start_time:
                # Handle status change when rescheduling
                instance.handle_status_on_reschedule(new_start_time)

            instance.start_time = new_start_time

        if commit:
            instance.save()
            self.save_m2m()

        return instance

class BusinessFilter(admin.SimpleListFilter):
    title = _('Business')
    parameter_name = 'business'
    
    def lookups(self, request, model_admin):
        # Get distinct businesses that have appointments
        from business.models import Business
        businesses = Business.objects.filter(
            id__in=set(model_admin.model.objects.values_list('employee__business', flat=True))
        ).distinct()
        return [(b.id, b.name) for b in businesses]
    
    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(employee__business_id=self.value())
        return queryset


class AppointmentHistoryInline(admin.TabularInline):
    """
    Inline admin for displaying appointment history.
    Read-only to preserve audit trail integrity.
    Shows simplified view without raw database fields.
    """
    model = AppointmentHistory
    extra = 0
    can_delete = False
    fields = ('modified_at', 'action', 'modified_by_display', 'change_summary')
    readonly_fields = ('modified_at', 'action', 'modified_by_display', 'change_summary')
    ordering = ('-modified_at',)

    def modified_by_display(self, obj):
        """Display who made the change in a readable format"""
        return obj.get_user_display()
    modified_by_display.short_description = 'Modified By'

    def has_add_permission(self, request, obj=None):
        """Prevent adding history entries manually"""
        return False

    def has_change_permission(self, request, obj=None):
        """Prevent editing history entries"""
        return False


@admin.register(Appointment)
class AppointmentAdmin(admin.ModelAdmin):
    form = AppointmentAdminForm
    list_display = ('id', 'employee', 'get_customer_name', 'get_business', 'start_time', 'end_time', 'status', 'payment_status', 'source', 'total_duration', 'total_price')
    list_filter = ('status', BusinessFilter, 'employee', 'start_time', 'payment_status', 'source')
    search_fields = ('employee__user__email', 'customer__customer__user__email', 'notes_from_customer')
    ordering = ('-start_time',)
    inlines = [AppointmentServiceInline, AppointmentAddOnInline, AppointmentHistoryInline]
    readonly_fields = ('end_time', 'total_duration_display', 'total_price_display')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('employee', 'customer', 'notes_from_customer'),
            'description': 'Select the employee and customer for this appointment. Employee selection is required to choose services.'
        }),
        ('Scheduling', {
            'fields': ('start_datetime',),
            'description': 'Set the start time for the appointment. The end time will be calculated based on service durations.'
        }),
        ('Summary', {
            'fields': ('end_time', 'total_duration_display', 'total_price_display')
        }),
        ('Status & Payment', {
            'fields': ('status', 'payment_status', 'source', 'cancellation_reason')
        }),
        ('Recurrence', {
            'fields': ('recurrence',),
            'classes': ('collapse',)
        }),
    )
    
    # Add validation errors to the top of the page
    def render_change_form(self, request, context, add=False, change=False, form_url='', obj=None):
        if context.get('adminform'):
            adminform = context['adminform']
            if adminform.form.errors:
                # Print errors to console for debugging
                print(f"Form has errors: {adminform.form.errors}")
                
                # Add a custom error message
                if adminform.form.errors.get('__all__'):
                    context['custom_validation_error'] = "Please correct the errors below before saving."
                    
                    # Check service errors specifically
                    services_missing = False
                    for inline_formset in context.get('inline_admin_formsets', []):
                        for form in inline_formset.formset.forms:
                            if form.errors and 'service' in form.errors:
                                services_missing = True
                                break
                    
                    if services_missing:
                        context['custom_validation_error'] += " At least one service must be selected."
        
        return super().render_change_form(request, context, add, change, form_url, obj)
    
    def get_customer_name(self, obj):
        if obj.customer and obj.customer.customer and obj.customer.customer.user:
            user = obj.customer.customer.user
            return f"{user.first_name} {user.last_name}"
        return "No customer"
    get_customer_name.short_description = "Customer"

    def get_business(self, obj):
        return obj.business.name if obj.business else "No business"
    get_business.short_description = "Business"

    def total_duration_display(self, obj):
        if not obj.pk:  # For new objects
            return '--'
        return f"{obj.total_duration} minutes"
    total_duration_display.short_description = _('Total Duration')

    def total_price_display(self, obj):
        if not obj.pk:  # For new objects
            return '--'
        return f"${obj.total_price}"
    total_price_display.short_description = _('Total Price')
    
    def total_duration(self, obj):
        return f"{obj.total_duration} minutes"
    total_duration.short_description = _('Duration')

    def total_price(self, obj):
        return f"${obj.total_price}"
    total_price.short_description = _('Total Price')
    
    class Media:
        css = {
            'all': ('css/appointment_admin.css',)
        }
        js = ('js/appointment_admin.js',)
    
    def save_model(self, request, obj, form, change):
        # First save the appointment to get an ID
        super().save_model(request, obj, form, change)
        
        # Check for employee conflicts and notify if found
        if obj.check_employee_conflicts():
            self.message_user(
                request, 
                _("Warning: This appointment overlaps with another appointment for the same employee."),
                level='WARNING'
            )
    
    def save_formset(self, request, form, formset, change):
        """Ensure service formsets are properly saved with price and duration data"""
        # First check if we have at least one valid service
        has_valid_service = False
        service_forms = []
        
        # Only check service formsets
        for form_instance in formset.forms:
            if isinstance(form_instance.instance, AppointmentService):
                service_forms.append(form_instance)
                
                # Check if this form has a valid service
                if form_instance.is_valid() and not form_instance.cleaned_data.get('DELETE', False):
                    if form_instance.cleaned_data.get('service'):
                        has_valid_service = True
        
        # If no valid service and this is the AppointmentService formset,
        # raise a validation error
        if not has_valid_service and service_forms:
            # Display a more specific error message
            error_msg = "Error: At least one service is required for an appointment."
            
            # Add error directly to the form
            if not form._errors:
                form._errors = {}
            
            if '__all__' not in form._errors:
                form._errors['__all__'] = form.error_class([error_msg])
            else:
                form._errors['__all__'].append(error_msg)
                
            # Add the error to each service form that's missing a service
            for service_form in service_forms:
                if not service_form.cleaned_data.get('service') and not service_form.cleaned_data.get('DELETE', False):
                    service_form.add_error('service', 'Service selection is required')
            
            # Show error message to user
            self.message_user(
                request,
                error_msg,
                level='ERROR'
            )
            return
            
        # Get instances but don't save them yet
        instances = formset.save(commit=False)
        
        for instance in instances:
            # If this is a service formset (AppointmentService)
            if isinstance(instance, AppointmentService):
                # Make sure service is set and skip if it's not
                if not instance.service_id:
                    continue  # Skip this instance
                
                # Get employee from parent form's instance
                employee = form.instance.employee
                
                # Always refresh price and duration when saving (not just for new records)
                if employee and instance.service:
                    # Try to find employee service
                    try:
                        from services.models import EmployeeService
                        emp_service = EmployeeService.objects.get(
                            employee=employee,
                            service=instance.service,
                            is_active=True
                        )
                        # Set the employee_service reference
                        instance.employee_service = emp_service
                        # Use the EmployeeService property methods which handle stylist level logic
                        instance.base_price = emp_service.price
                        instance.duration = int(emp_service.duration.total_seconds() // 60)
                        print(f"Using employee service pricing for {instance.service.name}: ${instance.base_price}, {instance.duration} min")
                    except EmployeeService.DoesNotExist:
                        # Fallback to service defaults
                        instance.base_price = instance.service.base_price
                        instance.duration = int(instance.service.base_duration.total_seconds() // 60)
                        print(f"Using base service pricing for {instance.service.name}: ${instance.base_price}, {instance.duration} min")
            
            # If this is an add-on formset (AppointmentAddOn)
            elif isinstance(instance, AppointmentAddOn):
                # Make sure add-on is set and skip if it's not
                if not instance.add_on_id:
                    continue  # Skip this instance
                
                # Get employee from parent form's instance
                employee = form.instance.employee
                
                # Always refresh price and duration for add-ons
                if employee and instance.add_on:
                    # Try to find employee add-on
                    try:
                        from services.models import EmployeeAddOn
                        emp_addon = EmployeeAddOn.objects.get(
                            employee=employee,
                            addon=instance.add_on,
                            is_active=True
                        )
                        # Use EmployeeAddOn properties which handle stylist level logic
                        instance.add_on_price = emp_addon.price
                        instance.duration = int(emp_addon.duration.total_seconds() // 60)
                        print(f"Using employee addon pricing for {instance.add_on.name}: ${instance.add_on_price}, {instance.duration} min")
                    except EmployeeAddOn.DoesNotExist:
                        # If no EmployeeAddOn exists, try to get StylistLevelAddOn directly
                        if employee.stylist_level:
                            try:
                                from services.models import StylistLevelAddOn
                                level_addon = StylistLevelAddOn.objects.get(
                                    business=employee.business,
                                    addon=instance.add_on,
                                    stylist_level=employee.stylist_level,
                                    is_active=True,
                                    is_offered=True
                                )
                                instance.add_on_price = level_addon.price
                                instance.duration = int(level_addon.duration.total_seconds() // 60)
                                print(f"Using stylist level addon pricing for {instance.add_on.name}: ${instance.add_on_price}, {instance.duration} min")
                            except StylistLevelAddOn.DoesNotExist:
                                # Fallback to add-on defaults
                                instance.add_on_price = instance.add_on.base_price
                                instance.duration = int(instance.add_on.base_duration.total_seconds() // 60)
                                print(f"Using base addon pricing for {instance.add_on.name}: ${instance.add_on_price}, {instance.duration} min")
                        else:
                            # Fallback to add-on defaults if no stylist level
                            instance.add_on_price = instance.add_on.base_price
                            instance.duration = int(instance.add_on.base_duration.total_seconds() // 60)
                            print(f"Using base addon pricing for {instance.add_on.name}: ${instance.add_on_price}, {instance.duration} min")
            
            # Save the instance
            instance.save()
        
        # Delete objects as required
        for obj in formset.deleted_objects:
            obj.delete()
        
        # Call the formset's save_m2m()
        formset.save_m2m()

@admin.register(AppointmentService)
class AppointmentServiceAdmin(admin.ModelAdmin):
    list_display = ('appointment', 'service', 'employee_service', 'quantity', 'final_price', 'total_duration')
    list_filter = ('service', 'appointment__employee', 'appointment__status')
    search_fields = ('appointment__customer__email', 'service__name')
    ordering = ('appointment', 'service')
    
    fieldsets = (
        ('Appointment Information', {
            'fields': ('appointment', 'service', 'employee_service')
        }),
        ('Quantity & Pricing', {
            'fields': ('quantity', 'base_price', 'price_override')
        }),
        ('Additional Information', {
            'fields': ('duration', 'notes')
        }),
    )

    def final_price(self, obj):
        if obj.price_override:
            return format_html('<span style="color: orange;">${}</span>', obj.final_price)
        return f"${obj.final_price}"
    final_price.short_description = _('Final Price')

@admin.register(AppointmentAddOn)
class AppointmentAddOnAdmin(admin.ModelAdmin):
    list_display = ('appointment', 'add_on', 'add_on_price', 'duration', 'service_add_on')
    list_filter = ('add_on', 'appointment__status')
    search_fields = ('appointment__customer__email', 'add_on__name')
    ordering = ('appointment', 'add_on')
    readonly_fields = ('duration',)
    
    fieldsets = (
        ('Appointment Information', {
            'fields': ('appointment', 'add_on', 'service_add_on')
        }),
        ('Pricing & Duration', {
            'fields': ('add_on_price', 'duration')
        }),
    )

@admin.register(RecurringPattern)
class RecurringPatternAdmin(admin.ModelAdmin):
    list_display = ('frequency', 'interval', 'start_date', 'end_date')
    list_filter = ('frequency',)
    search_fields = ('frequency',)
    ordering = ('-start_date',)


@admin.register(AppointmentHistory)
class AppointmentHistoryAdmin(admin.ModelAdmin):
    """
    Admin interface for viewing appointment history.
    Read-only to preserve audit trail integrity.
    Shows simplified view without raw database fields.
    """
    list_display = ('appointment', 'action', 'modified_by', 'modified_at', 'change_summary')
    list_filter = ('action', 'modified_at')
    search_fields = ('appointment__id', 'change_summary', 'modified_by')
    readonly_fields = (
        'appointment', 'action', 'modified_by', 'change_summary', 'modified_at'
    )
    ordering = ('-modified_at',)

    # Only show essential fields in the form (hide old_value, new_value, field_name)
    fields = (
        'appointment', 'action', 'modified_by', 'change_summary', 'modified_at'
    )

    def has_add_permission(self, request):
        """Prevent adding history entries manually"""
        return False

    def has_change_permission(self, request, obj=None):
        """Prevent editing history entries"""
        return False

    def has_delete_permission(self, request, obj=None):
        """Prevent deleting history entries"""
        return False
