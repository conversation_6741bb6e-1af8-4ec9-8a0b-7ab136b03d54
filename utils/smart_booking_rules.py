"""
Smart Booking Rules Utilities

This module contains utility functions for applying smart booking rules
to modify availability calculation results.
"""

from datetime import datetime, timedelta, time
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


def bookend_slots_filter(availability_data: Dict[str, List[str]], **kwargs) -> Dict[str, List[str]]:
    """
    Apply Bookend Slots rule: return only the first and last available slot of the day per stylist.

    Args:
        availability_data: Dictionary with stylist names as keys and list of ISO timestamp strings as values
        **kwargs: Additional parameters (not used for this rule)

    Returns:
        Dictionary with filtered availability data containing only first and last slots
    """
    filtered_data = {}

    for stylist_name, slots in availability_data.items():
        # Slots are already filtered to available only (ISO timestamp strings)
        available_slots = list(slots)  # Convert to list if needed

        if not available_slots:
            # No available slots, return empty list
            filtered_data[stylist_name] = []
        elif len(available_slots) == 1:
            # Only one slot available, return it
            filtered_data[stylist_name] = available_slots
        else:
            # Multiple slots available, return first and last
            # Sort to ensure proper order (should already be sorted)
            available_slots.sort()
            bookend_slots = [available_slots[0], available_slots[-1]]
            filtered_data[stylist_name] = bookend_slots

    logger.debug(f"Bookend Slots filter applied. Original slots: {sum(len(slots) for slots in availability_data.values())}, "
                f"Filtered slots: {sum(len(slots) for slots in filtered_data.values())}")

    return filtered_data


def gapless_booking_filter(availability_data: Dict[str, List[str]],
                          existing_appointments: Any = None,
                          service_duration: int = 60,
                          **kwargs) -> Dict[str, List[str]]:
    """
    Apply Gapless Booking rule: fill gaps in availability by snapping to actual appointment end times.

    This rule looks for gaps between appointments and creates additional time slots
    that start at the actual end time of previous appointments, rather than being
    constrained to the regular interval grid.

    Args:
        availability_data: Dictionary with stylist names as keys and list of ISO timestamp strings as values
        existing_appointments: QuerySet or list of existing appointments
        service_duration: Duration of the service in minutes
        **kwargs: Additional parameters

    Returns:
        Dictionary with enhanced availability data including gap-filling slots
    """
    if not existing_appointments:
        # No appointments to create gaps, return original data
        return availability_data

    enhanced_data = {}

    for stylist_name, slots in availability_data.items():
        # For now, return original slots since this is a complex feature
        # that would require detailed appointment analysis
        # In a real implementation, this would:
        # 1. Parse existing appointments for this stylist
        # 2. Find gaps between appointments
        # 3. Add new time slots that start at actual appointment end times
        enhanced_data[stylist_name] = list(slots)

    logger.debug(f"Gapless Booking filter applied. (Simplified implementation)")

    return enhanced_data


def tentative_hold_filter(availability_data: Dict[str, List[str]],
                         service_duration: int = 60,
                         tolerance_minutes: int = 30,
                         **kwargs) -> Dict[str, List[str]]:
    """
    Apply Tentative Hold rule: expose partial time slots when remaining time is within tolerance.

    This rule looks at the end of the day and if there's remaining time that's less than
    the full service duration but within the tolerance threshold, it exposes that time
    as a "tentative" booking option.

    Args:
        availability_data: Dictionary with stylist names as keys and list of ISO timestamp strings as values
        service_duration: Duration of the service in minutes
        tolerance_minutes: Tolerance threshold in minutes
        **kwargs: Additional parameters

    Returns:
        Dictionary with availability data including tentative slots
    """
    enhanced_data = {}

    for stylist_name, slots in availability_data.items():
        enhanced_slots = list(slots)

        if not slots:
            enhanced_data[stylist_name] = enhanced_slots
            continue

        # For now, return original slots since this is a complex feature
        # that would require detailed business hours analysis
        # In a real implementation, this would:
        # 1. Parse the last available slot time
        # 2. Check business closing hours
        # 3. Calculate if there's partial time within tolerance
        # 4. Add tentative slots with special marking
        enhanced_data[stylist_name] = enhanced_slots

    logger.debug(f"Tentative Hold filter applied with tolerance {tolerance_minutes} minutes. (Simplified implementation)")

    return enhanced_data


def apply_smart_booking_rules(availability_data: Dict[str, List[str]],
                             rule_ids: List[int],
                             smart_rules: List[Any] = None,
                             **kwargs) -> Dict[str, Any]:
    """
    Apply multiple smart booking rules to availability data.

    Args:
        availability_data: Original availability data (dict with stylist names as keys, ISO timestamp strings as values)
        rule_ids: List of rule IDs to apply
        smart_rules: List of SmartBookingRule instances
        **kwargs: Additional parameters to pass to rule functions

    Returns:
        Dictionary containing both original and smart-filtered availability data
    """
    if not rule_ids or not availability_data:
        return {
            'default': availability_data,
            'smart': None
        }

    # Start with original data
    smart_data = dict(availability_data)
    applied_rules = []

    # Apply rules in order
    for rule_id in rule_ids:
        if rule_id == 1:  # Bookend Slots
            smart_data = bookend_slots_filter(smart_data, **kwargs)
            applied_rules.append({'rule_id': 1, 'rule_name': 'Bookend Slots'})

        elif rule_id == 2:  # Gapless Booking
            smart_data = gapless_booking_filter(smart_data, **kwargs)
            applied_rules.append({'rule_id': 2, 'rule_name': 'Gapless Booking'})

        elif rule_id == 3:  # Tentative Hold
            # Get tolerance from smart_rules if available
            tolerance = 30  # default
            if smart_rules:
                for rule in smart_rules:
                    if rule['rule_type'] == 3:
                        tolerance = rule.get('tolerance_minutes', 30)
                        break

            smart_data = tentative_hold_filter(smart_data, tolerance_minutes=tolerance, **kwargs)
            applied_rules.append({'rule_id': 3, 'rule_name': 'Tentative Hold'})

    return {
        'default': availability_data,
        'smart': {
            'applied_rules': applied_rules,
            'availability': smart_data
        }
    }
