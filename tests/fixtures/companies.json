[{"model": "accounts.company", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"name": "Test Company Inc.", "address": "123 Test Street", "city": "Test City", "state": "TS", "zip_code": "12345", "phone_number": "+***********", "email": "<EMAIL>", "website": "https://testcompany.com", "created_at": "2024-04-13T22:00:00Z", "updated_at": "2024-04-13T22:00:00Z", "is_active": true}}, {"model": "accounts.companysettings", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"company": "550e8400-e29b-41d4-a716-************", "mfa_required": true, "password_expiry_days": 90, "max_failed_attempts": 5, "lockout_duration_minutes": 30, "session_timeout_minutes": 60, "created_at": "2024-04-13T22:00:00Z", "updated_at": "2024-04-13T22:00:00Z"}}, {"model": "accounts.accesslevel", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"company": "550e8400-e29b-41d4-a716-************", "name": "Full Access", "description": "Full system access", "permissions": ["*"], "created_at": "2024-04-13T22:00:00Z", "updated_at": "2024-04-13T22:00:00Z"}}, {"model": "accounts.accesslevel", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"company": "550e8400-e29b-41d4-a716-************", "name": "Limited Access", "description": "Limited system access", "permissions": ["view_reports", "edit_profile"], "created_at": "2024-04-13T22:00:00Z", "updated_at": "2024-04-13T22:00:00Z"}}]