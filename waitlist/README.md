# Waitlist App

This Django app manages waitlist entries for businesses.

## Models

### WaitlistEntry
- **business**: Required foreign key to Business model
- **customer_name**: Customer's full name
- **phone_number**: Customer's phone number
- **email**: Customer's email address
- **services**: ManyToMany relationship to Service model
- **add_ons**: ManyToMany relationship to AddOn model
- **employees**: ManyToMany relationship to Employee model (preferred employees)
- **notes**: Special requests or notes for this waitlist entry
- **status**: Current status (current/expired)
- **priority_rule**: Priority rule (manual)
- **created_at**: Auto-generated creation timestamp
- **updated_at**: Auto-generated update timestamp
- **expired_at**: Optional expiration timestamp

### PreferredWindow
- **entry**: Foreign key to WaitlistEntry
- **start_datetime**: Start of preferred time window
- **end_datetime**: End of preferred time window

## API Response Structure

The waitlist API returns simplified service and add-on data:

**Services include only:**
- `id`, `name`, `price`, `duration` (in minutes), `buffer_time` (in minutes)

**Add-ons include only:**
- `id`, `name`, `price`, `duration` (in minutes)

**Employees include only:**
- `id`, `name` (full name of the employee)

**Calculated fields at top level:**
- `total_price`: Sum of all service and add-on prices
- `total_duration`: Sum of all service durations + buffer times + add-on durations (in minutes)

## Admin Interface

Both models are registered in Django admin with:
- List views with filtering and search
- Inline editing of PreferredWindow entries
- Organized fieldsets for better UX
- Filter horizontal widgets for easy service and add-on selection

## API Endpoints

Available at `/api/v1/waitlist/`:
- GET: List all waitlist entries
- POST: Create new waitlist entry
- GET /{id}/: Retrieve specific entry
- PUT/PATCH /{id}/: Update entry
- DELETE /{id}/: Delete entry
- POST /{id}/expire/: Mark entry as expired
- POST /{id}/reactivate/: Reactivate expired entry

## Usage

1. Access Django admin at `/admin/` to manage waitlist entries
2. Use API endpoints for programmatic access
3. All entries must be linked to a business
