from django.contrib import admin
from .models import WaitlistEntry, PreferredWindow


class PreferredWindowInline(admin.TabularInline):
    model = PreferredWindow
    extra = 1
    fields = ('start_datetime', 'end_datetime')


@admin.register(WaitlistEntry)
class WaitlistEntryAdmin(admin.ModelAdmin):
    list_display = ('customer_name', 'business', 'phone_number', 'email', 'status', 'priority_rule', 'created_at', 'updated_at')
    list_filter = ('business', 'status', 'priority_rule', 'created_at', 'updated_at')
    search_fields = ('customer_name', 'phone_number', 'email', 'business__name')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [PreferredWindowInline]
    filter_horizontal = ('services', 'add_ons', 'employees')  # Nice widget for ManyToMany fields

    fieldsets = (
        ('Business Information', {
            'fields': ('business',)
        }),
        ('Customer Information', {
            'fields': ('customer_name', 'phone_number', 'email')
        }),
        ('Services & Add-ons', {
            'fields': ('services', 'add_ons', 'notes')
        }),
        ('Preferred Employees', {
            'fields': ('employees',)
        }),
        ('Waitlist Details', {
            'fields': ('status', 'priority_rule', 'expired_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PreferredWindow)
class PreferredWindowAdmin(admin.ModelAdmin):
    list_display = ('entry', 'start_datetime', 'end_datetime')
    list_filter = ('start_datetime', 'end_datetime')
    search_fields = ('entry__customer_name', 'entry__phone_number')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('entry')
