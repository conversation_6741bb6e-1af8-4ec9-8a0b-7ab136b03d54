# Chatbook API - Custom Pricing Test Collection

This updated Postman collection includes comprehensive tests for the new custom pricing functionality that allows appointments to have custom pricing for services and add-ons that only affect that specific appointment.

## Files

- `chatbook_api_postman_collection_updated.json` - Updated Postman collection with custom pricing tests
- `chatbook_postman_environment_updated.json` - Environment variables for testing

## Setup Instructions

### 1. Import the Collection and Environment

1. Open Postman
2. Click **Import** 
3. Import `chatbook_api_postman_collection_updated.json`
4. Import `chatbook_postman_environment_updated.json`
5. Select the "Chatbook API - Custom Pricing Test Environment" environment

### 2. Configure Environment Variables

Update these variables in your environment:

- `base_url`: Your Django server URL (default: `http://localhost:8000`)
- `appointment_id`: ID of an existing appointment to test with (default: `5`)
- `customer_id`: ID of a valid customer (default: `1`)
- `employee_id`: ID of a valid employee (default: `1`)
- `user_email`: Email for authentication
- `user_password`: Password for authentication

### 3. Start Your Django Server

Make sure your Django development server is running:

```bash
cd /path/to/chatbook-backend
source venv/bin/activate
python manage.py runserver
```

## Test Scenarios

The collection includes the following test scenarios:

### 1. Get Appointment (Before Update)
- **Purpose**: Check the current state of the appointment
- **What it tests**: Basic appointment retrieval
- **Expected**: Shows current pricing and duration for services/add-ons

### 2. Update Appointment with Custom Add-on Pricing
- **Purpose**: Test custom add-on pricing preservation
- **What it tests**: 
  - Custom `add_on_price`: $29.00
  - Custom `duration`: 25 minutes
- **Expected**: Custom values are preserved, only affecting this appointment

### 3. Update Appointment with Custom Service Pricing
- **Purpose**: Test custom service pricing preservation
- **What it tests**:
  - Custom `base_price`: $120.00
  - Custom `duration`: 90 minutes
- **Expected**: Custom values are preserved, only affecting this appointment

### 4. Verify Standard Pricing Still Works
- **Purpose**: Ensure backward compatibility
- **What it tests**: Automatic pricing when no custom values provided
- **Expected**: System calculates pricing based on employee/stylist rules

### 5. Get Appointment (After Update)
- **Purpose**: Verify final state
- **What it tests**: Final appointment state with all changes
- **Expected**: Shows the results of the last test performed

## How to Run the Tests

### Option 1: Run Individual Tests
1. Select a test request
2. Click **Send**
3. Check the **Test Results** tab for pass/fail status
4. Check the **Console** for detailed output

### Option 2: Run All Tests in Sequence
1. Right-click on "Custom Pricing Tests" folder
2. Select **Run collection**
3. Choose your environment
4. Click **Run Chatbook API - Custom Pricing Tests**
5. Review the test results

## Understanding the Test Results

### ✅ Successful Test Output
```
✅ Custom pricing test passed!
Duration: 25 minutes
Price: $ 29.00
```

### ❌ Failed Test Output
```
AssertionError: Custom duration should be 25 minutes
```

## Key Test Assertions

The tests verify:

1. **HTTP Status**: All requests return 200 OK
2. **Custom Pricing Preservation**: 
   - Custom `duration` values are preserved exactly
   - Custom `add_on_price` values are preserved exactly
   - Custom `base_price` values are preserved exactly
3. **Response Structure**: Required fields are present
4. **Backward Compatibility**: Standard pricing still works
5. **Calculation Accuracy**: Total duration and price are calculated correctly

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: You need to authenticate first
   - Use the login endpoint from the original collection
   - Or set a valid `token` in your environment

2. **404 Not Found**: Check your IDs
   - Verify `appointment_id` exists
   - Verify `customer_id` and `employee_id` exist
   - Verify `add_on` and `service` IDs exist

3. **Test Failures**: Check the specific assertion
   - Look at the console output for details
   - Verify your test data matches expectations

### Debug Tips

1. **Check Console Output**: Tests log detailed information
2. **Verify Environment Variables**: Ensure all IDs are correct
3. **Check Server Logs**: Look for Django debug output
4. **Use Get Requests**: Check appointment state before/after updates

## Expected Behavior

### Before the Fix
- Custom pricing would be ignored
- Duration would revert to employee/stylist defaults
- Price would revert to employee/stylist defaults

### After the Fix
- Custom pricing is preserved exactly as provided
- Only affects the specific appointment being updated
- Other appointments remain unchanged
- Backward compatibility is maintained

## API Endpoints Tested

- `GET /api/appointments/{id}/` - Retrieve appointment details
- `PUT /api/appointments/{id}/` - Update appointment with custom pricing

## Custom Pricing Fields

### For Services (`appointment_services`)
- `base_price`: Custom price for this appointment only
- `duration`: Custom duration in minutes for this appointment only

### For Add-ons (`appointment_add_ons`)
- `add_on_price`: Custom price for this appointment only  
- `duration`: Custom duration in minutes for this appointment only

## Example Request Body

```json
{
    "customer": 1,
    "employee": 1,
    "start_time": "2023-09-15T10:30:00Z",
    "status": "confirmed",
    "payment_status": "unpaid",
    "appointment_add_ons": [
        {
            "add_on": 1,
            "add_on_price": "29.00",  // Custom price
            "duration": 25            // Custom duration
        }
    ]
}
```

This will preserve the custom price ($29.00) and duration (25 minutes) for this specific appointment only.
