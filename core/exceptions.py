"""
Core Exceptions

Global exception classes used across the application.
"""


class ChatbookBaseException(Exception):
    """Base exception for all Chatbook-specific errors."""
    pass


class BusinessLogicError(ChatbookBaseException):
    """Exception raised for business logic violations."""
    pass


class ValidationError(ChatbookBaseException):
    """Exception raised for validation errors."""
    pass


class ConfigurationError(ChatbookBaseException):
    """Exception raised for configuration errors."""
    pass


class ExternalServiceError(ChatbookBaseException):
    """Exception raised when external services fail."""
    pass


# AWS Service Exceptions
class AWSServiceError(ExternalServiceError):
    """Base exception for AWS service errors."""
    pass


class S3ServiceError(AWSServiceError):
    """Exception raised for S3 service errors."""
    pass


class SQSServiceError(AWSServiceError):
    """Exception raised for SQS service errors."""
    pass


# File Processing Exceptions
class FileProcessingError(ChatbookBaseException):
    """Exception raised during file processing."""
    pass


class FileUploadError(FileProcessingError):
    """Exception raised during file upload."""
    pass


class FileValidationError(FileProcessingError):
    """Exception raised during file validation."""
    pass


class ImportError(FileProcessingError):
    """Exception raised during data import."""
    pass


# Authentication and Authorization Exceptions
class AuthenticationError(ChatbookBaseException):
    """Exception raised for authentication failures."""
    pass


class AuthorizationError(ChatbookBaseException):
    """Exception raised for authorization failures."""
    pass


# Business Domain Exceptions
class AppointmentError(BusinessLogicError):
    """Exception raised for appointment-related errors."""
    pass


class CustomerError(BusinessLogicError):
    """Exception raised for customer-related errors."""
    pass


class EmployeeError(BusinessLogicError):
    """Exception raised for employee-related errors."""
    pass


class ServiceError(BusinessLogicError):
    """Exception raised for service-related errors."""
    pass
