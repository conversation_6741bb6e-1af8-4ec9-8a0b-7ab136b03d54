"""
Core Utility Functions

Shared utility functions used across the application.
"""
import uuid
import hashlib
from datetime import datetime
from typing import Any, Dict, List, Optional


def generate_unique_id(prefix: str = "") -> str:
    """
    Generate a unique identifier.
    
    Args:
        prefix: Optional prefix for the ID
        
    Returns:
        Unique identifier string
    """
    unique_id = uuid.uuid4().hex[:10]
    return f"{prefix}_{unique_id}" if prefix else unique_id


def generate_file_key(user_id: int, filename: str, file_type: str = "upload") -> str:
    """
    Generate a consistent S3 key for file storage.
    
    Args:
        user_id: ID of the user uploading the file
        filename: Original filename
        file_type: Type of file (upload, processed, etc.)
        
    Returns:
        S3 key string
    """
    timestamp = datetime.now().strftime("%Y/%m/%d")
    file_id = generate_unique_id("file")
    
    # Clean filename
    clean_filename = filename.replace(" ", "_").replace("/", "_")
    
    return f"{file_type}/{user_id}/{timestamp}/{file_id}_{clean_filename}"


def calculate_file_hash(file_obj) -> str:
    """
    Calculate MD5 hash of a file.
    
    Args:
        file_obj: File object
        
    Returns:
        MD5 hash string
    """
    hash_md5 = hashlib.md5()
    file_obj.seek(0)
    
    for chunk in iter(lambda: file_obj.read(4096), b""):
        hash_md5.update(chunk)
    
    file_obj.seek(0)  # Reset file pointer
    return hash_md5.hexdigest()


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe storage.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    # Remove or replace unsafe characters
    unsafe_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
    sanitized = filename
    
    for char in unsafe_chars:
        sanitized = sanitized.replace(char, '_')
    
    # Remove multiple underscores
    while '__' in sanitized:
        sanitized = sanitized.replace('__', '_')
    
    return sanitized.strip('_')


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human-readable format.
    
    Args:
        size_bytes: File size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def validate_file_extension(filename: str, allowed_extensions: List[str]) -> bool:
    """
    Validate file extension against allowed extensions.
    
    Args:
        filename: Name of the file
        allowed_extensions: List of allowed extensions (e.g., ['.csv', '.xlsx'])
        
    Returns:
        True if extension is allowed, False otherwise
    """
    if not filename:
        return False
    
    file_extension = filename.lower().split('.')[-1]
    return f".{file_extension}" in [ext.lower() for ext in allowed_extensions]


def get_client_ip(request) -> str:
    """
    Get client IP address from request.
    
    Args:
        request: Django request object
        
    Returns:
        Client IP address
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', 'Unknown')
    return ip


def filter_sensitive_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Filter sensitive data from dictionary for logging.
    
    Args:
        data: Dictionary that may contain sensitive data
        
    Returns:
        Dictionary with sensitive fields filtered
    """
    sensitive_fields = [
        'password', 'token', 'secret', 'key', 'authorization',
        'csrf', 'session', 'cookie', 'auth', 'credential'
    ]
    
    filtered = {}
    for key, value in data.items():
        if any(sensitive in key.lower() for sensitive in sensitive_fields):
            filtered[key] = "[FILTERED]"
        else:
            filtered[key] = value
    
    return filtered
