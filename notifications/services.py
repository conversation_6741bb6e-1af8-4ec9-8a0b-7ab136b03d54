import os
import json
import boto3
import logging
from typing import List, Optional
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from botocore.exceptions import ClientError, NoCredentialsError, BotoCoreError
from .models import DeviceToken

User = get_user_model()
logger = logging.getLogger(__name__)


class NotificationService:
    """
    Service class for sending push notifications via AWS SNS.
    Based on the django-backend-implementation.md specification.
    """
    
    def __init__(self):
        self.sns_client = None
        self.topic_arn = None
        self._initialize_sns()
    
    def _initialize_sns(self):
        """Initialize SNS client and topic ARN with comprehensive error handling"""
        try:
            # Get AWS region from settings
            aws_region = getattr(settings, 'AWS_DEFAULT_REGION', 'us-west-2')

            # Initialize SNS client with proper error handling
            try:
                # Check if AWS_PROFILE is set and use session if available
                aws_profile = os.environ.get('AWS_PROFILE') or getattr(settings, 'AWS_PROFILE', None)

                if aws_profile:
                    logger.info(f"Using AWS profile: {aws_profile}")
                    session = boto3.Session(profile_name=aws_profile)
                    self.sns_client = session.client('sns', region_name=aws_region)
                else:
                    logger.info("Using default AWS credentials")
                    self.sns_client = boto3.client('sns', region_name=aws_region)

                logger.info(f"SNS client initialized successfully for region: {aws_region}")
            except NoCredentialsError:
                logger.error("AWS credentials not found. Please configure AWS credentials.")
                self.sns_client = None
                return
            except Exception as e:
                logger.error(f"Failed to create SNS client: {e}")
                self.sns_client = None
                return

            # Get SNS topic ARN from environment (try both variable names)
            self.topic_arn = (
                os.environ.get('SNS_TOPIC_ARN') or
                os.environ.get('AWS_SNS_TOPIC_ARN')
            )

            if not self.topic_arn:
                logger.warning(
                    "SNS_TOPIC_ARN not configured in environment variables. "
                    "Notifications will be disabled. Please set SNS_TOPIC_ARN or AWS_SNS_TOPIC_ARN."
                )
            else:
                logger.info(f"SNS topic ARN configured: {self.topic_arn}")

        except Exception as e:
            logger.error(f"Unexpected error during SNS initialization: {e}")
            self.sns_client = None
    
    def is_available(self) -> bool:
        """Check if notification service is available"""
        return self.sns_client is not None and self.topic_arn is not None
    
    def notify_user(self, user: User, alert_text: str, extra_data: dict = None) -> bool:
        """
        Send notification to a single user with comprehensive error handling.

        Args:
            user: User instance to notify
            alert_text: Notification message text
            extra_data: Additional data to include in notification

        Returns:
            True if notification was sent successfully, False otherwise
        """
        # Validate inputs
        if not user:
            logger.error("Cannot send notification: user is None")
            return False

        if not alert_text or not alert_text.strip():
            logger.error(f"Cannot send notification to user {user.email}: alert_text is empty")
            return False

        if not self.is_available():
            logger.warning(
                f"Notification service not available for user {user.email}, "
                "skipping notification. Check AWS credentials and SNS configuration."
            )
            return False

        try:
            # Get active device tokens for the user with error handling
            try:
                tokens = DeviceToken.objects.filter(
                    user=user,
                    is_active=True
                ).values_list('token', flat=True)
            except Exception as e:
                logger.error(f"Database error retrieving device tokens for user {user.email}: {e}")
                return False

            if not tokens:
                logger.debug(f"No active device tokens found for user {user.email}")
                return False

            logger.info(f"Found {len(tokens)} active device token(s) for user {user.email}")

            success_count = 0
            failed_tokens = []

            for token in tokens:
                try:
                    result = self._send_notification_to_token(token, alert_text, extra_data)
                    if result['success']:
                        success_count += 1
                    else:
                        failed_tokens.append(token[:20] + "...")
                        logger.warning(f"Failed to send to token {token[:20]}...: {result.get('error', 'Unknown error')}")
                except Exception as e:
                    logger.error(f"Error sending notification to token {token[:20]}... for user {user.email}: {e}")
                    failed_tokens.append(token[:20] + "...")

            # Log results
            if success_count > 0:
                logger.info(f"Successfully sent notifications to {success_count}/{len(tokens)} devices for user {user.email}")

            if failed_tokens:
                logger.warning(f"Failed to send notifications to {len(failed_tokens)} device(s) for user {user.email}")

            return success_count > 0

        except Exception as e:
            logger.error(f"Unexpected error sending notification to user {user.email}: {e}", exc_info=True)
            return False
    
    def notify_users(self, users: List[User], alert_text: str, extra_data: dict = None) -> int:
        """
        Send notification to multiple users.
        
        Args:
            users: List of User instances to notify
            alert_text: Notification message text
            extra_data: Additional data to include in notification
            
        Returns:
            Number of users successfully notified
        """
        if not self.is_available():
            logger.warning("Notification service not available, skipping notifications")
            return 0
        
        success_count = 0
        for user in users:
            if self.notify_user(user, alert_text, extra_data):
                success_count += 1
        
        logger.info(f"Successfully notified {success_count}/{len(users)} users")
        return success_count
    
    def _send_notification_to_token(self, device_token: str, alert_text: str, extra_data: dict = None) -> dict:
        """
        Send notification to a specific device token via SNS with comprehensive error handling.

        Args:
            device_token: Device token to send notification to
            alert_text: Notification message text
            extra_data: Additional data to include in notification

        Returns:
            Dict with 'success': bool, 'message_id': str, 'error': str
        """
        if not device_token or not device_token.strip():
            logger.error("Cannot send notification: device_token is empty")
            return {'success': False, 'message_id': None, 'error': 'Empty device token'}

        try:
            # Prepare message payload
            message_data = {
                'device_token': device_token,
                'alert': alert_text,
                'timestamp': json.dumps(
                    __import__('datetime').datetime.now().isoformat()
                )
            }

            # Add extra data if provided
            if extra_data and isinstance(extra_data, dict):
                message_data.update(extra_data)

            # Validate message size (SNS has a 256KB limit)
            message_json = json.dumps(message_data)
            if len(message_json.encode('utf-8')) > 250000:  # Leave some buffer
                logger.error(f"Message too large for SNS: {len(message_json)} bytes")
                return {'success': False, 'message_id': None, 'error': 'Message too large'}

            # Send message to SNS topic with specific error handling
            try:
                response = self.sns_client.publish(
                    TopicArn=self.topic_arn,
                    Message=message_json
                )

                message_id = response.get('MessageId')
                if message_id:
                    logger.debug(f"SNS notification sent successfully to {device_token[:20]}...: {message_id}")
                    return {'success': True, 'message_id': message_id, 'error': None}
                else:
                    logger.error(f"SNS publish returned no MessageId for token {device_token[:20]}...")
                    return {'success': False, 'message_id': None, 'error': 'No MessageId returned from SNS'}

            except ClientError as e:
                error_code = e.response.get('Error', {}).get('Code', 'Unknown')
                error_message = e.response.get('Error', {}).get('Message', str(e))

                if error_code == 'NotFound':
                    logger.error(f"SNS topic not found: {self.topic_arn}")
                elif error_code == 'InvalidParameter':
                    logger.error(f"Invalid parameter in SNS request: {error_message}")
                elif error_code == 'AuthorizationError':
                    logger.error(f"Authorization error for SNS: {error_message}")
                else:
                    logger.error(f"AWS SNS error ({error_code}): {error_message}")
                return {'success': False, 'message_id': None, 'error': f'SNS error: {error_code} - {error_message}'}

            except BotoCoreError as e:
                logger.error(f"AWS SDK error sending notification to {device_token[:20]}...: {e}")
                return {'success': False, 'message_id': None, 'error': f'AWS SDK error: {e}'}

        except json.JSONEncodeError as e:
            logger.error(f"JSON encoding error for notification data: {e}")
            return {'success': False, 'message_id': None, 'error': f'JSON encoding error: {e}'}

        except Exception as e:
            logger.error(f"Unexpected error sending notification to token {device_token[:20]}...: {e}", exc_info=True)
            return {'success': False, 'message_id': None, 'error': f'Unexpected error: {e}'}


# Global instance for easy import
notification_service = NotificationService()


# Convenience functions for backward compatibility and ease of use
def notify_user(user: User, alert_text: str, extra_data: dict = None) -> bool:
    """
    Send notification to a single user.
    Convenience function that uses the global notification service instance.
    """
    return notification_service.notify_user(user, alert_text, extra_data)


def notify_users(users: List[User], alert_text: str, extra_data: dict = None) -> int:
    """
    Send notification to multiple users.
    Convenience function that uses the global notification service instance.
    """
    return notification_service.notify_users(users, alert_text, extra_data)
