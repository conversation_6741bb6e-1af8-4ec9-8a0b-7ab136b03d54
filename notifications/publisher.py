"""
Outbox event publisher for reliable message delivery to SNS.
Polls the outbox table and publishes events to AWS SNS.
"""

import json
import logging
from typing import List, Dict, Any, Optional
from django.utils import timezone
from django.contrib.auth import get_user_model
from .models import OutboxEvent, Devi<PERSON>Token
from .outbox import OutboxService
from .services import notification_service
from .template_config import get_email_template_key, should_notify_user_type, USER_TYPES, ROLES

User = get_user_model()
logger = logging.getLogger(__name__)


class OutboxPublisher:
    """
    Publisher for processing outbox events and sending them to SNS.
    Handles event processing, retry logic, and error handling.
    """
    
    def __init__(self):
        self.notification_service = notification_service
    
    def process_pending_events(self, batch_size: int = 50) -> Dict[str, int]:
        """
        Process pending outbox events in batches.
        
        Args:
            batch_size: Number of events to process in one batch
            
        Returns:
            Dictionary with processing statistics
        """
        stats = {
            'processed': 0,
            'published': 0,
            'failed': 0,
            'skipped': 0
        }
        
        logger.info(f"Starting outbox event processing (batch_size={batch_size})")
        
        # Get pending events
        pending_events = OutboxService.get_pending_events(limit=batch_size)
        
        if not pending_events:
            logger.debug("No pending outbox events found")
            return stats
        
        logger.info(f"Found {len(pending_events)} pending outbox events")
        
        for event in pending_events:
            try:
                stats['processed'] += 1
                
                # Mark as processing to prevent duplicate processing
                event.mark_as_processing()
                
                # Process the event based on its type
                success = self._process_event(event)
                
                if success:
                    stats['published'] += 1
                else:
                    stats['failed'] += 1
                    
            except Exception as e:
                logger.error(f"Unexpected error processing event {event.id}: {e}", exc_info=True)
                event.mark_as_failed(f"Unexpected error: {e}")
                stats['failed'] += 1
        
        logger.info(f"Outbox processing completed: {stats}")
        return stats
    
    def _process_event(self, event: OutboxEvent) -> bool:
        """
        Process a single outbox event.
        
        Args:
            event: OutboxEvent to process
            
        Returns:
            True if successfully processed, False otherwise
        """
        try:
            logger.debug(f"Processing event {event.id}: {event.event_type}")
            
            # Route to appropriate handler based on event type
            if event.event_type.startswith('appointment.'):
                return self._process_appointment_event(event)
            elif event.event_type.startswith('waitlist.'):
                return self._process_waitlist_event(event)
            elif event.event_type.startswith('notification.'):
                return self._process_notification_event(event)
            else:
                logger.warning(f"Unknown event type: {event.event_type}")
                event.mark_as_failed(f"Unknown event type: {event.event_type}")
                return False
                
        except Exception as e:
            logger.error(f"Error processing event {event.id}: {e}", exc_info=True)
            event.mark_as_failed(str(e), {'exception_type': type(e).__name__})
            return False
    
    def _process_appointment_event(self, event: OutboxEvent) -> bool:
        """Process appointment-related events"""
        payload = event.payload
        
        # Extract user information
        customer_data = payload.get('customer')
        employee_data = payload.get('employee')
        
        if not customer_data or not employee_data:
            logger.warning(f"Missing customer or employee data in event {event.id}")
            event.mark_as_failed("Missing customer or employee data")
            return False
        
        # Get users
        try:
            customer_user = User.objects.get(id=customer_data['user_id'])
            employee_user = User.objects.get(id=employee_data['user_id'])
        except User.DoesNotExist as e:
            logger.error(f"User not found for event {event.id}: {e}")
            event.mark_as_failed(f"User not found: {e}")
            return False

        # Check business notification settings
        business_id = payload.get('business_id')
        business_settings = self._get_business_notification_settings(business_id, event.event_type)

        # Check if this type of notification is enabled for the business
        if not self._should_send_notification(event.event_type, business_settings):
            logger.info(f"Notification disabled for event type {event.event_type} in business {business_id}")
            event.mark_as_published(
                published_to="skipped_by_business_settings",
                message_id=f"skipped_{event.id}"
            )
            return True

        # Generate notification messages based on event type
        messages = self._generate_appointment_messages(event, customer_data, employee_data)

        # Send a single multi-recipient notification to SNS
        success, message_id = self._send_appointment_notification(
            event=event,
            customer_user=customer_user,
            employee_user=employee_user,
            messages=messages
        )

        if success and message_id:
            event.mark_as_published(
                published_to=self.notification_service.topic_arn,
                message_id=message_id
            )
            logger.info(f"Appointment event {event.id} published successfully: {message_id}")
            return True
        else:
            error_msg = "Failed to send appointment notification"
            event.mark_as_failed(error_msg)
            logger.error(f"Appointment event {event.id} failed: {error_msg}")
            return False
    
    def _process_waitlist_event(self, event: OutboxEvent) -> bool:
        """Process waitlist-related events"""
        payload = event.payload
        
        # Extract customer information
        customer_data = payload.get('customer')
        business_data = payload.get('business')
        
        if not customer_data:
            logger.warning(f"Missing customer data in waitlist event {event.id}")
            event.mark_as_failed("Missing customer data")
            return False
        
        # Get customer user
        try:
            customer_user = User.objects.get(id=customer_data['user_id'])
        except User.DoesNotExist as e:
            logger.error(f"Customer user not found for event {event.id}: {e}")
            event.mark_as_failed(f"Customer user not found: {e}")
            return False
        
        # Generate notification message
        business_name = business_data.get('name', 'the business') if business_data else 'the business'
        customer_message = f"You've been added to the waitlist for {business_name}"
        
        # Send notification to customer
        success, message_ids = self._send_user_notification(customer_user, customer_message, event)
        if success and message_ids:
            # Use the first message ID as the primary reference
            primary_message_id = message_ids[0]
            event.mark_as_published(
                published_to=self.notification_service.topic_arn,
                message_id=primary_message_id
            )
            logger.info(f"Waitlist event {event.id} published successfully with {len(message_ids)} SNS messages")
            return True
        else:
            error_msg = "Failed to send waitlist notification"
            if not success:
                error_msg += " - user notification failed"
            elif not message_ids:
                error_msg += " - no SNS messages were sent"

            event.mark_as_failed(error_msg)
            logger.error(f"Waitlist event {event.id} failed: {error_msg}")
            return False
    
    def _process_notification_event(self, event: OutboxEvent) -> bool:
        """Process direct notification events"""
        payload = event.payload
        
        # Extract recipient information
        recipient_data = payload.get('recipient')
        message = payload.get('message')
        
        if not recipient_data or not message:
            logger.warning(f"Missing recipient or message data in notification event {event.id}")
            event.mark_as_failed("Missing recipient or message data")
            return False
        
        # Get recipient user
        try:
            recipient_user = User.objects.get(id=recipient_data['user_id'])
        except User.DoesNotExist as e:
            logger.error(f"Recipient user not found for event {event.id}: {e}")
            event.mark_as_failed(f"Recipient user not found: {e}")
            return False
        
        # Send notification
        success, message_ids = self._send_user_notification(recipient_user, message, event)
        if success and message_ids:
            # Use the first message ID as the primary reference
            primary_message_id = message_ids[0]
            event.mark_as_published(
                published_to=self.notification_service.topic_arn,
                message_id=primary_message_id
            )
            logger.info(f"Direct notification event {event.id} published successfully with {len(message_ids)} SNS messages")
            return True
        else:
            error_msg = "Failed to send direct notification"
            if not success:
                error_msg += " - user notification failed"
            elif not message_ids:
                error_msg += " - no SNS messages were sent"

            event.mark_as_failed(error_msg)
            logger.error(f"Direct notification event {event.id} failed: {error_msg}")
            return False
    
    def _send_user_notification(self, user: User, message: str, event: OutboxEvent) -> tuple[bool, list[str]]:
        """
        Send multi-channel notification to a user.

        Supports multiple notification channels based on business customer preferences:
        - Mobile push notifications (via device tokens)
        - Email notifications (via user email + business customer email_reminders preference)
        - SMS notifications (via user phone + business customer sms_reminders preference)

        The SNS topic receives the notification with all user contact information and preferences,
        and downstream services (Lambda, SQS consumers) handle channel-specific delivery.

        Args:
            user: User to notify
            message: Notification message
            event: OutboxEvent being processed

        Returns:
            Tuple of (success: bool, message_ids: list[str])
        """
        try:
            # Get business customer preferences from event payload
            business_id = event.payload.get('business_id')
            notification_preferences = self._get_notification_preferences(user, business_id)

            # Get active device tokens for mobile push notifications
            device_tokens = DeviceToken.objects.filter(
                user=user,
                is_active=True
            ).values_list('token', flat=True)

            # Determine user type and role based on user relationships
            role = self._determine_user_role(user, business_id)
            user_type = self._map_role_to_user_type(role)

            # Prepare comprehensive user contact information for multi-channel delivery
            # Handle phone_number safely - it might be None or empty
            phone_number = None
            if hasattr(user, 'phone_number') and user.phone_number:
                try:
                    phone_number = str(user.phone_number)
                except Exception as e:
                    logger.warning(f"Error converting phone number to string for user {user.email}: {e}")
                    phone_number = None

            user_contact_info = {
                'user_id': str(user.id),
                'email': user.email or '',  # Ensure email is never None
                'first_name': user.first_name or '',
                'last_name': user.last_name or '',
                'phone_number': phone_number,
                'user_type': user_type,  # 'business' or 'customer'
                'role': role,  # 'customer', 'employee', 'business_owner', 'admin'
                'email_template_key': get_email_template_key(event.event_type, user_type),
                # Business customer notification preferences
                'email_reminders_enabled': notification_preferences.get('email_reminders', True),
                'sms_reminders_enabled': notification_preferences.get('sms_reminders', True),
                'push_notifications_enabled': len(device_tokens) > 0,  # Based on device token availability
                'business_id': business_id,
            }

            # Always send a single multi-channel notification to SNS
            # Include device tokens in the message so downstream services can handle push notifications
            user_contact_info['device_tokens'] = list(device_tokens) if device_tokens else []

            multi_channel_result = self._publish_to_sns_multi_channel(message, event, user_contact_info)

            if multi_channel_result['success'] and multi_channel_result['message_id']:
                message_ids = [multi_channel_result['message_id']]

                # Log what channels are available
                available_channels = []
                if user_contact_info.get('email_reminders_enabled'):
                    available_channels.append('email')
                if user_contact_info.get('sms_reminders_enabled'):
                    available_channels.append('sms')
                if device_tokens:
                    available_channels.append(f'push({len(device_tokens)} tokens)')

                logger.info(f"Multi-channel notification sent to SNS for user {user.email}: {multi_channel_result['message_id']} (channels: {', '.join(available_channels)})")

                return True, message_ids
            else:
                error_msg = multi_channel_result.get('error', 'Unknown error')
                logger.error(f"Failed to send multi-channel notification for user {user.email}: {error_msg}")
                return False, []

        except Exception as e:
            logger.error(f"Error sending notification to user {user.email} for event {event.id}: {e}", exc_info=True)
            return False, []

    def _send_appointment_notification(self, event: OutboxEvent, customer_user: User, employee_user: User, messages: dict) -> tuple:
        """
        Send a single multi-recipient notification for appointment events.

        Args:
            event: OutboxEvent being processed
            customer_user: Customer User instance
            employee_user: Employee User instance
            messages: Dict with 'customer' and 'employee' message text

        Returns:
            Tuple of (success: bool, message_id: str)
        """
        try:
            # Prepare recipient information based on event type configuration
            recipients = []

            # Add customer recipient if allowed for this event type
            if should_notify_user_type(event.event_type, USER_TYPES['CUSTOMER']):
                customer_info = self._prepare_recipient_info(customer_user, event.payload.get('business_id'))
                customer_info['message'] = messages['customer']
                customer_info['user_type'] = USER_TYPES['CUSTOMER']  # 'customer'
                customer_info['role'] = ROLES['CUSTOMER']  # 'customer'
                customer_info['email_template_key'] = get_email_template_key(event.event_type, USER_TYPES['CUSTOMER'])
                recipients.append(customer_info)
                logger.debug(f"Added customer recipient for {event.event_type}")
            else:
                logger.debug(f"Skipping customer recipient for {event.event_type} (not configured)")

            # Add employee recipient if allowed for this event type
            if should_notify_user_type(event.event_type, USER_TYPES['BUSINESS']):
                employee_info = self._prepare_recipient_info(employee_user, event.payload.get('business_id'))
                employee_info['message'] = messages['employee']
                employee_info['user_type'] = USER_TYPES['BUSINESS']  # 'business'
                employee_info['role'] = ROLES['EMPLOYEE']  # 'employee'
                employee_info['email_template_key'] = get_email_template_key(event.event_type, USER_TYPES['BUSINESS'])
                recipients.append(employee_info)
                logger.debug(f"Added employee recipient for {event.event_type}")
            else:
                logger.debug(f"Skipping employee recipient for {event.event_type} (not configured)")

            # Ensure we have at least one recipient
            if not recipients:
                logger.warning(f"No recipients configured for event type {event.event_type}")
                return False, None

            # Send single SNS message with multiple recipients
            return self._publish_multi_recipient_notification(event, recipients)

        except Exception as e:
            logger.error(f"Error sending appointment notification for event {event.id}: {e}", exc_info=True)
            return False, None

    def _prepare_recipient_info(self, user: User, business_id: int = None) -> dict:
        """
        Prepare recipient information for a user.

        Args:
            user: User instance
            business_id: Business context ID

        Returns:
            Dict with recipient information
        """
        # Get device tokens
        device_tokens = list(DeviceToken.objects.filter(
            user=user,
            is_active=True
        ).values_list('token', flat=True))

        # Get notification preferences
        notification_preferences = self._get_notification_preferences(user, business_id)

        # Handle phone_number safely
        phone_number = None
        if hasattr(user, 'phone_number') and user.phone_number:
            try:
                phone_number = str(user.phone_number)
            except Exception as e:
                logger.warning(f"Error converting phone number to string for user {user.email}: {e}")
                phone_number = None

        return {
            'user_id': str(user.id),
            'email': user.email or '',
            'first_name': user.first_name or '',
            'last_name': user.last_name or '',
            'phone_number': phone_number,
            'device_tokens': device_tokens,
            # user_type will be set contextually by the caller
            'email_enabled': notification_preferences.get('email_reminders', True),
            'sms_enabled': notification_preferences.get('sms_reminders', True),
            'push_enabled': len(device_tokens) > 0,
            'business_id': business_id,
        }

    def _publish_to_sns(self, device_token: str, message: str, event: OutboxEvent) -> dict:
        """
        Publish notification to SNS topic.

        Args:
            device_token: Device token to send to
            message: Notification message
            event: OutboxEvent being processed

        Returns:
            Dict with 'success': bool and 'message_id': str
        """
        try:
            # Prepare SNS message
            sns_message = {
                'device_token': device_token,
                'alert': message,
                'event_id': str(event.id),
                'event_type': event.event_type,
                'timestamp': timezone.now().isoformat()
            }

            # Add event payload data
            sns_message.update(event.payload)

            # Publish to SNS and get the actual response
            result = self.notification_service._send_notification_to_token(
                device_token=device_token,
                alert_text=message,
                extra_data=sns_message
            )

            # The service now returns a dict with success, message_id, and error
            return {
                'success': result['success'],
                'message_id': result['message_id'],
                'error': result.get('error')
            }

        except Exception as e:
            logger.error(f"Error publishing to SNS for token {device_token[:20]}...: {e}")
            return {
                'success': False,
                'message_id': None
            }

    def _get_notification_preferences(self, user: User, business_id: int) -> dict:
        """
        Get notification preferences for a user from their business customer relationship.

        Args:
            user: User to get preferences for
            business_id: Business ID to look up relationship

        Returns:
            Dict with notification preferences
        """
        try:
            if not business_id:
                logger.warning(f"No business_id provided for user {user.email}, using default preferences")
                return {
                    'email_reminders': True,
                    'sms_reminders': True,
                    'push_notifications': True
                }

            # Import here to avoid circular imports
            from business.models import BusinessCustomer

            # Get business customer relationship
            business_customer = BusinessCustomer.objects.select_related('customer').filter(
                business_id=business_id,
                customer__user=user
            ).first()

            if business_customer:
                return {
                    'email_reminders': business_customer.email_reminders,
                    'sms_reminders': business_customer.sms_reminders,
                    'push_notifications': True  # Always allow push if device tokens exist
                }
            else:
                logger.warning(f"No business customer relationship found for user {user.email} and business {business_id}, using default preferences")
                return {
                    'email_reminders': True,
                    'sms_reminders': True,
                    'push_notifications': True
                }

        except Exception as e:
            logger.error(f"Error getting notification preferences for user {user.email}: {e}")
            # Return safe defaults
            return {
                'email_reminders': True,
                'sms_reminders': True,
                'push_notifications': True
            }

    def _determine_user_role(self, user: User, business_id: int = None) -> str:
        """
        Determine the user role for notification routing.

        Args:
            user: User instance
            business_id: Business context ID

        Returns:
            String indicating user role: 'customer', 'employee', 'business_owner', 'admin'
        """
        try:
            # Check if user is a superuser/admin
            if user.is_superuser or user.is_staff:
                return ROLES['ADMIN']

            # Check if user is a business owner
            if hasattr(user, 'owned_businesses') and user.owned_businesses.exists():
                return ROLES['BUSINESS_OWNER']

            # Check if user is an employee in the specific business context
            if business_id:
                from employees.models import Employee
                if Employee.objects.filter(user=user, business_id=business_id, is_active=True).exists():
                    return ROLES['EMPLOYEE']

            # Check if user is an employee in any business
            if hasattr(user, 'employee_profiles') and user.employee_profiles.filter(is_active=True).exists():
                return ROLES['EMPLOYEE']

            # Default to customer
            return ROLES['CUSTOMER']

        except Exception as e:
            logger.warning(f"Error determining user role for {user.email}: {e}")
            return ROLES['CUSTOMER']  # Safe default

    def _build_appointment_data(self, event):
        """
        Build appointment_data section for SNS message, excluding null fields.

        Args:
            event: OutboxEvent instance

        Returns:
            dict: Clean appointment data without null fields
        """
        # Get services and addons, ensuring only id and name are included
        services = event.payload.get('appointment_services', [])
        addons = event.payload.get('appointment_addons', [])

        # Filter to only include id and name for services and addons
        filtered_services = []
        for service in services:
            if isinstance(service, dict):
                filtered_services.append({
                    'id': service.get('id'),
                    'name': service.get('name')
                })

        filtered_addons = []
        for addon in addons:
            if isinstance(addon, dict):
                filtered_addons.append({
                    'id': addon.get('id'),
                    'name': addon.get('name')
                })

        appointment_data = {
            'appointment_id': event.payload.get('appointment_id'),
            'start_time': event.payload.get('start_time'),
            'end_time': event.payload.get('end_time'),
            'total_duration': event.payload.get('total_duration'),
            'status': event.payload.get('status'),
            'customer': event.payload.get('customer', {}),
            'employee': event.payload.get('employee', {}),
            'appointment_services': filtered_services,
            'appointment_addons': filtered_addons,
        }

        # Only add change tracking fields for appointment.changed events
        # Booked and cancelled events don't need old/new comparison fields
        if event.event_type == 'appointment.changed':
            # Filter old/new services and addons to only include id and name
            old_services = event.payload.get('old_appointment_services', [])
            new_services = event.payload.get('new_appointment_services', [])
            old_addons = event.payload.get('old_appointment_addons', [])
            new_addons = event.payload.get('new_appointment_addons', [])

            # Filter old services
            filtered_old_services = []
            for service in old_services:
                if isinstance(service, dict):
                    filtered_old_services.append({
                        'id': service.get('id'),
                        'name': service.get('name')
                    })

            # Filter new services
            filtered_new_services = []
            for service in new_services:
                if isinstance(service, dict):
                    filtered_new_services.append({
                        'id': service.get('id'),
                        'name': service.get('name')
                    })

            # Filter old addons
            filtered_old_addons = []
            for addon in old_addons:
                if isinstance(addon, dict):
                    filtered_old_addons.append({
                        'id': addon.get('id'),
                        'name': addon.get('name')
                    })

            # Filter new addons
            filtered_new_addons = []
            for addon in new_addons:
                if isinstance(addon, dict):
                    filtered_new_addons.append({
                        'id': addon.get('id'),
                        'name': addon.get('name')
                    })

            change_fields = {
                'old_start_time': event.payload.get('old_start_time'),
                'new_start_time': event.payload.get('new_start_time'),
                'old_appointment_services': filtered_old_services,
                'new_appointment_services': filtered_new_services,
                'old_appointment_addons': filtered_old_addons,
                'new_appointment_addons': filtered_new_addons
            }

            # Only include non-null fields
            for field, value in change_fields.items():
                if value is not None:
                    appointment_data[field] = value

        return appointment_data

    def _map_role_to_user_type(self, role: str) -> str:
        """
        Map user role to user type for template selection.

        Args:
            role: User role ('customer', 'employee', 'business_owner', 'admin')

        Returns:
            User type ('business' or 'customer')
        """
        if role in [ROLES['EMPLOYEE'], ROLES['BUSINESS_OWNER'], ROLES['ADMIN']]:
            return USER_TYPES['BUSINESS']
        else:
            return USER_TYPES['CUSTOMER']

    def _get_business_context(self, business_id: int, event_type: str = None) -> dict:
        """
        Get business context information including business name and notification settings.

        Args:
            business_id: Business ID
            event_type: Event type to determine which custom messages to include

        Returns:
            Dict with business context information
        """
        if not business_id:
            notification_settings = self._get_business_notification_settings(None, event_type)
            context = {
                'business_id': None,
                'business_name': None,
                'notification_settings': notification_settings
            }
        else:
            try:
                # Import here to avoid circular imports
                from business.models import Business

                business = Business.objects.get(id=business_id)
                notification_settings = self._get_business_notification_settings(business_id, event_type)
                context = {
                    'business_id': business_id,
                    'business_name': business.name,
                    'notification_settings': notification_settings
                }
            except Business.DoesNotExist:
                logger.warning(f"Business with ID {business_id} not found")
                notification_settings = self._get_business_notification_settings(business_id, event_type)
                context = {
                    'business_id': business_id,
                    'business_name': None,
                    'notification_settings': notification_settings
                }
            except Exception as e:
                logger.error(f"Error getting business context for ID {business_id}: {e}")
                notification_settings = self._get_business_notification_settings(business_id, event_type)
                context = {
                    'business_id': business_id,
                    'business_name': None,
                    'notification_settings': notification_settings
                }

        return context

    def _get_business_notification_settings(self, business_id: int, event_type: str = None) -> dict:
        """
        Get business notification settings.

        Args:
            business_id: Business ID

        Returns:
            Dict with business notification settings
        """
        if not business_id:
            # Import here to avoid circular imports
            from business.models import BusinessNotificationSettings
            # Create a temporary instance to get the default policy
            temp_settings = BusinessNotificationSettings()
            result = {
                'appointment_detail_enabled': True,
                'confirmation_request_enabled': True,
                'appointment_reminder_enabled': True,
                'confirmation_hours_before': 72,
                'reminder_hours_before': 24,
            }

            # Only include relevant messages for specific event types
            if event_type == 'appointment.booked':
                result['new_appointment_message'] = ''
                result['cancellation_no_show_policy_message'] = temp_settings.get_required_cancellation_policy()
            elif event_type == 'appointment.cancelled':
                result['cancellation_no_show_policy_message'] = temp_settings.get_required_cancellation_policy()

            return result

        try:
            # Import here to avoid circular imports
            from business.models import BusinessNotificationSettings

            settings = BusinessNotificationSettings.objects.get(business_id=business_id)
            result = {
                'appointment_detail_enabled': settings.appointment_detail_enabled,
                'confirmation_request_enabled': settings.confirmation_request_enabled,
                'appointment_reminder_enabled': settings.appointment_reminder_enabled,
                'confirmation_hours_before': settings.confirmation_hours_before,
                'reminder_hours_before': settings.reminder_hours_before,
            }

            # Only include relevant messages for specific event types
            if event_type == 'appointment.booked':
                result['new_appointment_message'] = settings.new_appointment_message or ''
                result['cancellation_no_show_policy_message'] = settings.get_full_cancellation_policy()
            elif event_type == 'appointment.cancelled':
                result['cancellation_no_show_policy_message'] = settings.get_full_cancellation_policy()

            return result
        except BusinessNotificationSettings.DoesNotExist:
            logger.info(f"No notification settings found for business {business_id}, using defaults")
            temp_settings = BusinessNotificationSettings()
            result = {
                'appointment_detail_enabled': True,
                'confirmation_request_enabled': True,
                'appointment_reminder_enabled': True,
                'confirmation_hours_before': 72,
                'reminder_hours_before': 24,
            }

            # Only include relevant messages for specific event types
            if event_type == 'appointment.booked':
                result['new_appointment_message'] = ''
                result['cancellation_no_show_policy_message'] = temp_settings.get_required_cancellation_policy()
            elif event_type == 'appointment.cancelled':
                result['cancellation_no_show_policy_message'] = temp_settings.get_required_cancellation_policy()

            return result
        except Exception as e:
            logger.error(f"Error getting business notification settings for ID {business_id}: {e}")
            temp_settings = BusinessNotificationSettings()
            result = {
                'appointment_detail_enabled': True,
                'confirmation_request_enabled': True,
                'appointment_reminder_enabled': True,
                'confirmation_hours_before': 72,
                'reminder_hours_before': 24,
            }

            # Only include relevant messages for specific event types
            if event_type == 'appointment.booked':
                result['new_appointment_message'] = ''
                result['cancellation_no_show_policy_message'] = temp_settings.get_required_cancellation_policy()
            elif event_type == 'appointment.cancelled':
                result['cancellation_no_show_policy_message'] = temp_settings.get_required_cancellation_policy()

            return result

    def _should_send_notification(self, event_type: str, business_settings: dict) -> bool:
        """
        Check if notification should be sent based on business settings.

        Args:
            event_type: Type of event (e.g., 'appointment.booked')
            business_settings: Business notification settings

        Returns:
            Boolean indicating if notification should be sent
        """
        # Map event types to business settings
        event_setting_map = {
            'appointment.booked': 'appointment_detail_enabled',
            'appointment.changed': 'appointment_detail_enabled',
            'appointment.cancelled': 'appointment_detail_enabled',
            'appointment.confirmation_requested': 'confirmation_request_enabled',
            'appointment.reminder': 'appointment_reminder_enabled',
        }

        setting_key = event_setting_map.get(event_type)
        if setting_key:
            return business_settings.get(setting_key, True)

        # For unknown event types, default to sending
        return True

    def _publish_to_sns_multi_channel(self, message: str, event: OutboxEvent, user_contact_info: dict) -> dict:
        """
        Publish multi-channel notification to SNS topic for email/SMS handling.

        This method sends a structured notification that downstream services can use to deliver
        email and SMS notifications based on user preferences.

        Args:
            message: Notification message
            event: OutboxEvent being processed
            user_contact_info: User contact information and preferences

        Returns:
            Dict with 'success': bool and 'message_id': str
        """
        try:
            # Validate that we have essential contact information
            recipient_email = user_contact_info.get('email', '').strip()
            if not recipient_email:
                logger.error(f"Missing or empty email for user {user_contact_info.get('user_id')} in event {event.id}")
                return {'success': False, 'message_id': None, 'error': 'Missing recipient email'}

            # Prepare structured SNS message for multi-channel delivery
            sns_message = {
                'notification_type': 'multi_channel',
                'event_id': str(event.id),
                'event_type': event.event_type,
                'timestamp': timezone.now().isoformat(),
                'message': message,
                'recipient': {
                    'user_id': user_contact_info.get('user_id'),
                    'email': recipient_email,
                    'first_name': user_contact_info.get('first_name', ''),
                    'last_name': user_contact_info.get('last_name', ''),
                    'phone_number': user_contact_info.get('phone_number'),
                    'device_tokens': user_contact_info.get('device_tokens', []),
                    'user_type': user_contact_info.get('user_type', 'customer')
                },
                'preferences': {
                    'email_enabled': user_contact_info.get('email_reminders_enabled', True),
                    'sms_enabled': user_contact_info.get('sms_reminders_enabled', True),
                    'push_enabled': len(user_contact_info.get('device_tokens', [])) > 0
                },
                'business_context': self._get_business_context(user_contact_info.get('business_id'), event.event_type),
                'appointment_data': self._build_appointment_data(event)
            }

            # Publish directly to SNS topic with structured message
            try:
                import json
                response = self.notification_service.sns_client.publish(
                    TopicArn=self.notification_service.topic_arn,
                    Message=json.dumps(sns_message),
                    Subject=f"Chatbook Notification: {event.event_type}",
                    MessageAttributes={
                        'event_type': {
                            'DataType': 'String',
                            'StringValue': event.event_type
                        },
                        'notification_type': {
                            'DataType': 'String',
                            'StringValue': 'multi_channel'
                        },
                        'business_id': {
                            'DataType': 'String',
                            'StringValue': str(user_contact_info.get('business_id', ''))
                        }
                    }
                )

                message_id = response.get('MessageId')
                if message_id:
                    logger.debug(f"Multi-channel SNS notification sent successfully: {message_id}")
                    return {'success': True, 'message_id': message_id, 'error': None}
                else:
                    logger.error("SNS publish returned no MessageId for multi-channel notification")
                    return {'success': False, 'message_id': None, 'error': 'No MessageId returned from SNS'}

            except Exception as sns_error:
                logger.error(f"SNS publish error for multi-channel notification: {sns_error}")
                return {'success': False, 'message_id': None, 'error': f'SNS error: {sns_error}'}

        except Exception as e:
            logger.error(f"Error publishing multi-channel notification to SNS: {e}")
            return {
                'success': False,
                'message_id': None,
                'error': str(e)
            }

    def _publish_multi_recipient_notification(self, event: OutboxEvent, recipients: list) -> tuple:
        """
        Publish a single SNS message with multiple recipients.

        Args:
            event: OutboxEvent being processed
            recipients: List of recipient info dicts

        Returns:
            Tuple of (success: bool, message_id: str)
        """
        try:
            # Prepare structured SNS message for multi-recipient delivery
            sns_message = {
                'notification_type': 'multi_recipient',
                'event_id': str(event.id),
                'event_type': event.event_type,
                'timestamp': timezone.now().isoformat(),
                'recipients': recipients,
                'business_context': self._get_business_context(event.payload.get('business_id'), event.event_type),
                'appointment_data': self._build_appointment_data(event)
            }

            # Publish directly to SNS topic with structured message
            try:
                import json
                response = self.notification_service.sns_client.publish(
                    TopicArn=self.notification_service.topic_arn,
                    Message=json.dumps(sns_message),
                    Subject=f"Chatbook Notification: {event.event_type}",
                    MessageAttributes={
                        'event_type': {
                            'DataType': 'String',
                            'StringValue': event.event_type
                        },
                        'notification_type': {
                            'DataType': 'String',
                            'StringValue': 'multi_recipient'
                        },
                        'business_id': {
                            'DataType': 'String',
                            'StringValue': str(event.payload.get('business_id', ''))
                        }
                    }
                )

                message_id = response.get('MessageId')
                if message_id:
                    logger.debug(f"Multi-recipient SNS notification sent successfully: {message_id}")
                    logger.info(f"Sent notification for {len(recipients)} recipients: {[r['email'] for r in recipients]}")
                    return True, message_id
                else:
                    logger.error("SNS publish returned no MessageId for multi-recipient notification")
                    return False, None

            except Exception as sns_error:
                logger.error(f"SNS publish error for multi-recipient notification: {sns_error}")
                return False, None

        except Exception as e:
            logger.error(f"Error publishing multi-recipient notification to SNS: {e}")
            return False, None
    
    def _generate_appointment_messages(self, event: OutboxEvent, customer_data: Dict, employee_data: Dict) -> Dict[str, str]:
        """Generate notification messages for appointment events"""
        payload = event.payload
        appointment_time = payload.get('start_time', 'unknown time')
        
        # Format time for display
        try:
            from datetime import datetime
            dt = datetime.fromisoformat(appointment_time.replace('Z', '+00:00'))
            formatted_time = dt.strftime("%B %d, %Y at %I:%M %p")
        except:
            formatted_time = appointment_time
        
        customer_name = f"{customer_data.get('first_name', '')} {customer_data.get('last_name', '')}".strip()
        employee_name = f"{employee_data.get('first_name', '')} {employee_data.get('last_name', '')}".strip()
        
        if event.event_type == 'appointment.booked':
            return {
                'customer': f"Your appointment has been scheduled for {formatted_time}",
                'employee': f"New appointment scheduled with {customer_name} on {formatted_time}"
            }
        elif event.event_type == 'appointment.changed':
            # Determine what changed based on payload data
            has_status_change = 'old_status' in payload and 'new_status' in payload
            has_time_change = 'old_start_time' in payload and 'new_start_time' in payload

            if has_time_change:
                # Time change is the most significant, prioritize rescheduling message
                return {
                    'customer': f"Your appointment has been rescheduled to {formatted_time}",
                    'employee': f"Appointment with {customer_name} has been rescheduled to {formatted_time}"
                }
            elif has_status_change:
                new_status = payload.get('new_status', 'updated')
                return {
                    'customer': f"Your appointment on {formatted_time} status changed to {new_status}",
                    'employee': f"Appointment with {customer_name} on {formatted_time} status changed to {new_status}"
                }
            else:
                return {
                    'customer': f"Your appointment on {formatted_time} has been updated",
                    'employee': f"Appointment with {customer_name} on {formatted_time} has been updated"
                }
        elif event.event_type == 'appointment.confirmation_requested':
            return {
                'customer': f"Please confirm your appointment scheduled for {formatted_time}",
                'employee': f"Confirmation requested for appointment with {customer_name} on {formatted_time}"
            }
        else:
            return {
                'customer': f"Your appointment on {formatted_time} has been updated",
                'employee': f"Appointment with {customer_name} on {formatted_time} has been updated"
            }
