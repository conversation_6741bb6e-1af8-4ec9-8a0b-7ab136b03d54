from django.db.models.signals import post_save, pre_save, post_delete
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.db import transaction
from appointments.models import Appointment, AppointmentService, AppointmentAddOn
from waitlist.models import WaitlistEntry
from .outbox import OutboxService
from utils.timezone_utils import get_business_timezone
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

# Track appointments that need events created (to avoid duplicates)
_pending_appointment_events = set()
_scheduled_callbacks = {}
_newly_created_appointments = set()  # Track appointments that were just created


def create_consolidated_appointment_event(appointment_id):
    """
    Create a consolidated appointment.changed event that captures all changes
    made during the transaction. This is called at transaction end.
    """
    # Prevent duplicate events for the same appointment
    if appointment_id in _pending_appointment_events:
        return

    _pending_appointment_events.add(appointment_id)

    try:
        appointment = Appointment.objects.get(pk=appointment_id)

        # Get the original state from the tracker
        original_state = AppointmentChangeTracker.get_original_state(appointment_id)

        if not original_state:
            # No original state tracked, this might be a direct service/addon change
            # Use current state as both old and new (no time change)
            from django.utils import timezone as django_timezone
            current_time = appointment.start_time.astimezone(django_timezone.get_current_timezone()).isoformat()
            current_services = [s.service.name for s in appointment.appointment_services.all()]
            current_addons = [a.add_on.name for a in appointment.appointment_add_ons.all()]

            additional_data = {
                'old_start_time': current_time,
                'new_start_time': current_time,
                'old_appointment_services': current_services,
                'new_appointment_services': current_services,
                'old_appointment_addons': current_addons,
                'new_appointment_addons': current_addons
            }
        else:
            # Compare current state with original state
            from django.utils import timezone as django_timezone

            current_services = [s.service.name for s in appointment.appointment_services.all()]
            current_addons = [a.add_on.name for a in appointment.appointment_add_ons.all()]

            additional_data = {
                'old_start_time': original_state['original_start_time'].astimezone(django_timezone.get_current_timezone()).isoformat(),
                'new_start_time': appointment.start_time.astimezone(django_timezone.get_current_timezone()).isoformat(),
                'old_appointment_services': original_state['original_services'],
                'new_appointment_services': current_services,
                'old_appointment_addons': original_state['original_addons'],
                'new_appointment_addons': current_addons
            }

        # Create the consolidated event
        OutboxService.create_appointment_event(
            appointment=appointment,
            event_type='appointment.changed',
            additional_data=additional_data
        )

        logger.info(f"Created consolidated appointment.changed event for appointment {appointment_id}")

    except Appointment.DoesNotExist:
        logger.warning(f"Appointment {appointment_id} not found when creating consolidated event")
    except Exception as e:
        logger.error(f"Error creating consolidated appointment event for {appointment_id}: {e}", exc_info=True)
    finally:
        # Clean up tracking
        _pending_appointment_events.discard(appointment_id)
        _scheduled_callbacks.pop(appointment_id, None)
        AppointmentChangeTracker.finish_tracking(appointment_id)


def cleanup_new_appointment_tracking(appointment_id):
    """Clean up tracking for newly created appointments"""
    _newly_created_appointments.discard(appointment_id)
    logger.debug(f"Cleaned up new appointment tracking for {appointment_id}")

def create_appointment_booked_event(appointment_id):
    """Create outbox event for appointment.booked after transaction commits"""
    try:
        from appointments.models import Appointment
        appointment = Appointment.objects.get(id=appointment_id)
        OutboxService.create_appointment_event(
            appointment=appointment,
            event_type='appointment.booked'
        )
        logger.info(f"Created outbox event for new appointment {appointment_id}")
    except Appointment.DoesNotExist:
        logger.error(f"Appointment {appointment_id} not found when creating outbox event")
    except Exception as e:
        logger.error(f"Error creating outbox event for appointment {appointment_id}: {e}", exc_info=True)


def schedule_appointment_event(appointment_id):
    """
    Schedule an appointment event to be created at transaction end.
    Ensures only one callback is scheduled per appointment per transaction.
    Skips newly created appointments to avoid duplicate events.
    """
    # Don't create change events for appointments that were just created
    if appointment_id in _newly_created_appointments:
        logger.debug(f"Skipping event for newly created appointment {appointment_id}")
        return

    if appointment_id not in _scheduled_callbacks:
        # Schedule the callback only once per appointment per transaction
        callback_id = f"appointment_{appointment_id}_{id(transaction.get_connection())}"
        _scheduled_callbacks[appointment_id] = callback_id

        transaction.on_commit(lambda: create_consolidated_appointment_event(appointment_id))
        logger.debug(f"Scheduled consolidated event creation for appointment {appointment_id}")
    else:
        logger.debug(f"Event already scheduled for appointment {appointment_id}, skipping")


class AppointmentChangeTracker:
    """
    Track all changes to an appointment within a transaction.
    This ensures we create only one event per edit session.
    """
    _tracked_appointments = {}

    @classmethod
    def start_tracking(cls, appointment_id):
        """Start tracking changes for an appointment"""
        if appointment_id not in cls._tracked_appointments:
            try:
                appointment = Appointment.objects.get(pk=appointment_id)
                cls._tracked_appointments[appointment_id] = {
                    'original_start_time': appointment.start_time,
                    'original_status': appointment.status,
                    'original_services': [s.service.name for s in appointment.appointment_services.all()],
                    'original_addons': [a.add_on.name for a in appointment.appointment_add_ons.all()],
                    'changes_made': False
                }
            except Appointment.DoesNotExist:
                pass

    @classmethod
    def mark_changed(cls, appointment_id):
        """Mark that changes were made to this appointment"""
        if appointment_id in cls._tracked_appointments:
            cls._tracked_appointments[appointment_id]['changes_made'] = True

    @classmethod
    def get_original_state(cls, appointment_id):
        """Get the original state of the appointment"""
        return cls._tracked_appointments.get(appointment_id)

    @classmethod
    def finish_tracking(cls, appointment_id):
        """Finish tracking and clean up"""
        return cls._tracked_appointments.pop(appointment_id, None)


@receiver(pre_save, sender=Appointment)
def track_appointment_changes(sender, instance, **kwargs):
    """
    Track the original state of appointment before any changes.
    """
    if instance.pk:  # Only for existing appointments
        AppointmentChangeTracker.start_tracking(instance.pk)

        # Store old values on instance for immediate comparison
        try:
            old_appointment = Appointment.objects.get(pk=instance.pk)
            instance._old_status = old_appointment.status
            instance._old_start_time = old_appointment.start_time
        except Appointment.DoesNotExist:
            instance._old_status = None
            instance._old_start_time = None
    else:
        instance._old_status = None
        instance._old_start_time = None


@receiver(post_save, sender=Appointment)
def appointment_notification_handler(sender, instance, created, **kwargs):
    """
    Create outbox events for appointment notifications.
    Uses the outbox pattern for reliable event publishing.
    """
    try:
        if created:
            # Track this as a newly created appointment to prevent service/addon signals
            # from creating duplicate change events
            _newly_created_appointments.add(instance.pk)

            # Schedule outbox event creation after transaction commits
            # This ensures services and addons are saved before creating the event
            from django.db import transaction
            transaction.on_commit(lambda: create_appointment_booked_event(instance.pk))

            # Schedule cleanup of the tracking after a short delay
            transaction.on_commit(lambda: cleanup_new_appointment_tracking(instance.pk))

            logger.info(f"Scheduled outbox event for new appointment {instance.id}")

        else:
            # Existing appointment updated
            old_status = getattr(instance, '_old_status', None)
            old_start_time = getattr(instance, '_old_start_time', None)

            # Check for significant changes (time changes and cancellations)
            has_time_change = old_start_time and old_start_time != instance.start_time
            has_cancellation = (old_status and old_status != 'cancelled' and
                              instance.status == 'cancelled')

            if has_time_change or has_cancellation:
                # Determine event type
                if has_cancellation:
                    event_type = 'appointment.cancelled'
                else:
                    event_type = 'appointment.changed'

                # Build additional data for the change
                additional_data = {}

                if has_time_change:
                    # Ensure consistent timezone formatting (use local timezone)
                    from django.utils import timezone as django_timezone

                    # Convert old_start_time to local timezone if it's not already
                    if old_start_time.tzinfo is None:
                        old_start_time = django_timezone.make_aware(old_start_time)

                    # Format both times consistently in local timezone
                    additional_data.update({
                        'old_start_time': old_start_time.astimezone(django_timezone.get_current_timezone()).isoformat(),
                        'new_start_time': instance.start_time.astimezone(django_timezone.get_current_timezone()).isoformat()
                    })

                # Only add change tracking fields for appointment.changed events
                if not has_cancellation:
                    # Add service and addon tracking for changes
                    current_services = [s.service.name for s in instance.appointment_services.all()]
                    current_addons = [a.add_on.name for a in instance.appointment_add_ons.all()]

                    additional_data.update({
                        'old_appointment_services': current_services,
                        'new_appointment_services': current_services,
                        'old_appointment_addons': current_addons,
                        'new_appointment_addons': current_addons
                    })

                # For cancellations, we don't need any old/new comparison fields
                # The base appointment data (services, addons, etc.) is already in the payload

                # Create the appropriate event
                if has_cancellation:
                    # Create cancellation event directly (no need for consolidation)
                    OutboxService.create_appointment_event(
                        appointment=instance,
                        event_type=event_type,
                        additional_data=additional_data
                    )
                    logger.info(f"Created outbox event for appointment {instance.id} cancellation")
                else:
                    # Schedule consolidated event creation for time changes
                    schedule_appointment_event(instance.pk)
                    logger.info(f"Scheduled consolidated event for appointment {instance.id} time change")

    except Exception as e:
        logger.error(f"Error creating outbox event for appointment {instance.id}: {e}", exc_info=True)


# Note: The old helper functions have been removed as we now use the outbox pattern
# All notification logic is handled by the outbox publisher worker


@receiver(post_save, sender=WaitlistEntry)
def waitlist_notification_handler(sender, instance, created, **kwargs):
    """
    Create outbox events for waitlist notifications.
    Uses the outbox pattern for reliable event publishing.
    """
    if not created:
        return

    try:
        OutboxService.create_waitlist_event(
            waitlist_entry=instance,
            event_type='waitlist.entry_created'
        )
        logger.info(f"Created outbox event for new waitlist entry {instance.id}")

    except Exception as e:
        logger.error(f"Error creating outbox event for waitlist entry {instance.id}: {e}", exc_info=True)


@receiver(post_save, sender=AppointmentService)
def appointment_service_changed_handler(sender, instance, created, **kwargs):
    """
    Handle changes to appointment services.
    Defers event creation until transaction completes.
    """
    try:
        # Schedule consolidated event creation at transaction end
        schedule_appointment_event(instance.appointment.pk)

        action = "added" if created else "modified"
        logger.info(f"Scheduled outbox event for appointment {instance.appointment.id} - service {action}: {instance.service.name}")

    except Exception as e:
        logger.error(f"Error scheduling outbox event for appointment service change {instance.id}: {e}", exc_info=True)


@receiver(post_delete, sender=AppointmentService)
def appointment_service_deleted_handler(sender, instance, **kwargs):
    """
    Handle deletion of appointment services.
    Defers event creation until transaction completes.
    """
    try:
        # Schedule consolidated event creation at transaction end
        schedule_appointment_event(instance.appointment.pk)

        logger.info(f"Scheduled outbox event for appointment {instance.appointment.id} - service removed: {instance.service.name}")

    except Exception as e:
        logger.error(f"Error scheduling outbox event for appointment service deletion {instance.id}: {e}", exc_info=True)


@receiver(post_save, sender=AppointmentAddOn)
def appointment_addon_changed_handler(sender, instance, created, **kwargs):
    """
    Handle changes to appointment addons.
    Defers event creation until transaction completes.
    """
    try:
        # Schedule consolidated event creation at transaction end
        schedule_appointment_event(instance.appointment.pk)

        action = "added" if created else "modified"
        logger.info(f"Scheduled outbox event for appointment {instance.appointment.id} - addon {action}: {instance.add_on.name}")

    except Exception as e:
        logger.error(f"Error scheduling outbox event for appointment addon change {instance.id}: {e}", exc_info=True)


@receiver(post_delete, sender=AppointmentAddOn)
def appointment_addon_deleted_handler(sender, instance, **kwargs):
    """
    Handle deletion of appointment addons.
    Defers event creation until transaction completes.
    """
    try:
        # Schedule consolidated event creation at transaction end
        schedule_appointment_event(instance.appointment.pk)

        logger.info(f"Scheduled outbox event for appointment {instance.appointment.id} - addon removed: {instance.add_on.name}")

    except Exception as e:
        logger.error(f"Error scheduling outbox event for appointment addon deletion {instance.id}: {e}", exc_info=True)
