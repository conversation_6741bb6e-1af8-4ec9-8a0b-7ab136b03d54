"""
Django management command to process outbox events.
Can be run as a one-time job or continuously as a daemon.
"""

import time
import signal
import sys
import logging
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from notifications.publisher import OutboxPublisher
from notifications.outbox import OutboxService

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Process outbox events and publish them to SNS'
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.running = True
        self.publisher = OutboxPublisher()
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--daemon',
            action='store_true',
            help='Run continuously as a daemon process'
        )
        parser.add_argument(
            '--interval',
            type=int,
            default=10,
            help='Polling interval in seconds (default: 10)'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=50,
            help='Number of events to process per batch (default: 50)'
        )
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up old processed events before processing'
        )
        parser.add_argument(
            '--cleanup-days',
            type=int,
            default=7,
            help='Delete processed events older than this many days (default: 7)'
        )
        parser.add_argument(
            '--max-iterations',
            type=int,
            help='Maximum number of processing iterations (for testing)'
        )
    
    def handle(self, *args, **options):
        """Main command handler"""
        self.setup_signal_handlers()
        
        daemon_mode = options['daemon']
        interval = options['interval']
        batch_size = options['batch_size']
        cleanup = options['cleanup']
        cleanup_days = options['cleanup_days']
        max_iterations = options['max_iterations']
        
        self.stdout.write(
            self.style.SUCCESS(
                f"Starting outbox processor "
                f"(daemon={daemon_mode}, interval={interval}s, batch_size={batch_size})"
            )
        )
        
        # Perform cleanup if requested
        if cleanup:
            self.cleanup_old_events(cleanup_days)
        
        iteration_count = 0
        
        try:
            while self.running:
                iteration_count += 1
                
                # Check max iterations limit
                if max_iterations and iteration_count > max_iterations:
                    self.stdout.write(
                        self.style.WARNING(f"Reached max iterations limit ({max_iterations})")
                    )
                    break
                
                # Process events
                stats = self.process_events(batch_size)
                
                # Log statistics
                if stats['processed'] > 0:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Iteration {iteration_count}: "
                            f"Processed {stats['processed']}, "
                            f"Published {stats['published']}, "
                            f"Failed {stats['failed']}"
                        )
                    )
                else:
                    self.stdout.write(
                        self.style.HTTP_INFO(f"Iteration {iteration_count}: No events to process")
                    )
                
                # If not in daemon mode, exit after one iteration
                if not daemon_mode:
                    break
                
                # Sleep before next iteration
                if self.running:
                    time.sleep(interval)
                    
        except KeyboardInterrupt:
            self.stdout.write(self.style.WARNING("Received interrupt signal"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Unexpected error: {e}"))
            logger.error(f"Unexpected error in outbox processor: {e}", exc_info=True)
            raise CommandError(f"Outbox processor failed: {e}")
        finally:
            self.stdout.write(self.style.SUCCESS("Outbox processor stopped"))
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            self.stdout.write(self.style.WARNING(f"Received signal {signum}, shutting down gracefully..."))
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def process_events(self, batch_size):
        """Process a batch of outbox events"""
        try:
            return self.publisher.process_pending_events(batch_size=batch_size)
        except Exception as e:
            logger.error(f"Error processing outbox events: {e}", exc_info=True)
            return {'processed': 0, 'published': 0, 'failed': 0, 'skipped': 0}
    
    def cleanup_old_events(self, days):
        """Clean up old processed events"""
        try:
            self.stdout.write(f"Cleaning up processed events older than {days} days...")
            deleted_count = OutboxService.cleanup_processed_events(older_than_days=days)
            self.stdout.write(
                self.style.SUCCESS(f"Cleaned up {deleted_count} old processed events")
            )
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error during cleanup: {e}"))
            logger.error(f"Error cleaning up old events: {e}", exc_info=True)
