from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid
import json


class OutboxEvent(models.Model):
    """
    Outbox pattern implementation for reliable event publishing.
    Ensures transactional consistency between domain operations and event publishing.
    """

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('published', 'Published'),
        ('failed', 'Failed'),
        ('dead_letter', 'Dead Letter'),
    ]

    EVENT_TYPE_CHOICES = [
        ('appointment.booked', 'Appointment Booked'),
        ('appointment.changed', 'Appointment Changed'),
        ('appointment.cancelled', 'Appointment Cancelled'),
        ('appointment.confirmation_requested', 'Appointment Confirmation Requested'),
        ('appointment.reminder', 'Appointment Reminder'),
        ('waitlist.entry_created', 'Waitlist Entry Created'),
        ('notification.direct', 'Direct Notification'),
    ]

    # Primary key and ordering
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    sequence_number = models.BigIntegerField(unique=True, db_index=True, editable=False)

    # Event metadata
    event_type = models.CharField(max_length=50, choices=EVENT_TYPE_CHOICES, db_index=True)
    aggregate_type = models.CharField(max_length=50, db_index=True)  # e.g., 'appointment', 'waitlist'
    aggregate_id = models.CharField(max_length=100, db_index=True)  # ID of the domain object

    # Event payload and context
    payload = models.JSONField(help_text="Event data as JSON")
    metadata = models.JSONField(default=dict, help_text="Additional metadata (user_id, correlation_id, etc.)")

    # Processing status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', db_index=True)
    retry_count = models.PositiveIntegerField(default=0)
    max_retries = models.PositiveIntegerField(default=3)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    scheduled_at = models.DateTimeField(default=timezone.now, db_index=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    # Error tracking
    last_error = models.TextField(blank=True, help_text="Last error message if processing failed")
    error_details = models.JSONField(default=dict, help_text="Detailed error information")

    # Publishing details
    published_to = models.CharField(max_length=200, blank=True, help_text="SNS topic ARN or destination")
    message_id = models.CharField(max_length=100, blank=True, help_text="External message ID (e.g., SNS MessageId)")

    class Meta:
        verbose_name = 'Outbox Event'
        verbose_name_plural = 'Outbox Events'
        ordering = ['sequence_number']
        indexes = [
            models.Index(fields=['status', 'scheduled_at'], name='outbox_status_scheduled_idx'),
            models.Index(fields=['event_type', 'created_at'], name='outbox_type_created_idx'),
            models.Index(fields=['aggregate_type', 'aggregate_id'], name='outbox_aggregate_idx'),
            models.Index(fields=['created_at'], name='outbox_created_idx'),
        ]

    def __str__(self):
        return f"{self.event_type} - {self.aggregate_type}:{self.aggregate_id} ({self.status})"

    def save(self, *args, **kwargs):
        """Auto-generate sequence number if not set"""
        if not self.sequence_number:
            # Get the next sequence number
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT COALESCE(MAX(sequence_number), 0) + 1 FROM notifications_outboxevent")
                self.sequence_number = cursor.fetchone()[0]
        super().save(*args, **kwargs)

    def mark_as_processing(self):
        """Mark event as being processed"""
        self.status = 'processing'
        self.updated_at = timezone.now()
        self.save(update_fields=['status', 'updated_at'])

    def mark_as_published(self, message_id=None, published_to=None):
        """Mark event as successfully published"""
        self.status = 'published'
        self.processed_at = timezone.now()
        self.updated_at = timezone.now()
        if message_id:
            self.message_id = message_id
        if published_to:
            self.published_to = published_to
        self.save(update_fields=['status', 'processed_at', 'updated_at', 'message_id', 'published_to'])

    def mark_as_failed(self, error_message, error_details=None):
        """Mark event as failed and increment retry count"""
        self.retry_count += 1
        self.last_error = str(error_message)[:1000]  # Truncate long error messages
        self.updated_at = timezone.now()

        if error_details:
            self.error_details = error_details

        # Check if we should move to dead letter
        if self.retry_count >= self.max_retries:
            self.status = 'dead_letter'
        else:
            self.status = 'failed'
            # Schedule retry with exponential backoff
            backoff_seconds = min(300, 2 ** self.retry_count * 10)  # Max 5 minutes
            self.scheduled_at = timezone.now() + timezone.timedelta(seconds=backoff_seconds)

        self.save(update_fields=[
            'retry_count', 'last_error', 'error_details', 'status',
            'scheduled_at', 'updated_at'
        ])

    def reset_for_retry(self):
        """Reset event status to pending for manual retry"""
        self.status = 'pending'
        self.scheduled_at = timezone.now()
        self.updated_at = timezone.now()
        self.save(update_fields=['status', 'scheduled_at', 'updated_at'])

    @property
    def is_ready_for_processing(self):
        """Check if event is ready to be processed"""
        return (
            self.status in ['pending', 'failed'] and
            self.scheduled_at <= timezone.now()
        )

    @property
    def payload_json(self):
        """Get payload as formatted JSON string"""
        return json.dumps(self.payload, indent=2)


class DeviceToken(models.Model):
    """
    Model to store device tokens for push notifications.
    Based on the django-backend-implementation.md specification.
    """
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='device_tokens'
    )
    token = models.CharField(max_length=200, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    last_active = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = 'Device Token'
        verbose_name_plural = 'Device Tokens'
        ordering = ['-last_active']

    def __str__(self):
        return f"{self.user.email} - {self.token[:20]}..."
