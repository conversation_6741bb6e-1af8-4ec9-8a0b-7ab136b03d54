from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import DeviceToken, OutboxEvent


@admin.register(OutboxEvent)
class OutboxEventAdmin(admin.ModelAdmin):
    list_display = [
        'sequence_number', 'event_type', 'aggregate_info', 'status',
        'message_id_display', 'retry_count', 'created_at', 'scheduled_at', 'processed_at'
    ]
    list_filter = [
        'status', 'event_type', 'aggregate_type', 'created_at',
        'scheduled_at', 'retry_count'
    ]
    search_fields = [
        'id', 'event_type', 'aggregate_type', 'aggregate_id', 'message_id', 'last_error'
    ]
    readonly_fields = [
        'id', 'sequence_number', 'created_at', 'updated_at',
        'processed_at', 'payload_formatted', 'metadata_formatted'
    ]
    ordering = ['-sequence_number']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Event Information', {
            'fields': (
                'id', 'sequence_number', 'event_type', 'aggregate_type',
                'aggregate_id', 'status'
            )
        }),
        ('Processing Details', {
            'fields': (
                'retry_count', 'max_retries', 'scheduled_at', 'processed_at',
                'published_to', 'message_id'
            )
        }),
        ('Error Information', {
            'fields': ('last_error', 'error_details'),
            'classes': ('collapse',)
        }),
        ('Event Data', {
            'fields': ('payload_formatted', 'metadata_formatted'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    actions = ['retry_failed_events', 'mark_as_dead_letter', 'reset_for_retry']

    def aggregate_info(self, obj):
        """Display aggregate type and ID together"""
        return f"{obj.aggregate_type}:{obj.aggregate_id}"
    aggregate_info.short_description = 'Aggregate'

    def message_id_display(self, obj):
        """Display MessageId with visual indicators for real vs fake IDs"""
        if not obj.message_id:
            return format_html('<span style="color: #999;">No MessageId</span>')

        message_id = obj.message_id

        # Check if it's a fake MessageId (contains batch_, direct_, waitlist_)
        if any(fake_prefix in message_id for fake_prefix in ['batch_', 'direct_', 'waitlist_']):
            return format_html(
                '<span style="color: #d63384; font-weight: bold;" title="Fake MessageId - not from SNS">❌ {}</span>',
                message_id[:20] + '...' if len(message_id) > 20 else message_id
            )

        # Check if it looks like a real SNS MessageId (UUID format with dashes)
        elif '-' in message_id and len(message_id) > 30:
            return format_html(
                '<span style="color: #198754; font-weight: bold;" title="Real SNS MessageId">✅ {}</span>',
                message_id[:20] + '...' if len(message_id) > 20 else message_id
            )

        # Unknown format
        else:
            return format_html(
                '<span style="color: #fd7e14;" title="Unknown MessageId format">⚠️ {}</span>',
                message_id[:20] + '...' if len(message_id) > 20 else message_id
            )

    message_id_display.short_description = 'SNS MessageId'

    def payload_formatted(self, obj):
        """Display formatted JSON payload"""
        return format_html('<pre>{}</pre>', obj.payload_json)
    payload_formatted.short_description = 'Payload (JSON)'

    def metadata_formatted(self, obj):
        """Display formatted JSON metadata"""
        import json
        return format_html('<pre>{}</pre>', json.dumps(obj.metadata, indent=2))
    metadata_formatted.short_description = 'Metadata (JSON)'

    def retry_failed_events(self, request, queryset):
        """Admin action to retry failed events"""
        failed_events = queryset.filter(status__in=['failed', 'dead_letter'])
        count = 0
        for event in failed_events:
            event.reset_for_retry()
            count += 1

        self.message_user(
            request,
            f"Reset {count} events for retry."
        )
    retry_failed_events.short_description = "Retry selected failed events"

    def mark_as_dead_letter(self, request, queryset):
        """Admin action to mark events as dead letter"""
        count = queryset.exclude(status='dead_letter').update(
            status='dead_letter',
            updated_at=timezone.now()
        )

        self.message_user(
            request,
            f"Marked {count} events as dead letter."
        )
    mark_as_dead_letter.short_description = "Mark as dead letter"

    def reset_for_retry(self, request, queryset):
        """Admin action to reset events for retry"""
        count = 0
        for event in queryset:
            if event.status in ['failed', 'dead_letter']:
                event.reset_for_retry()
                count += 1

        self.message_user(
            request,
            f"Reset {count} events for retry."
        )
    reset_for_retry.short_description = "Reset for retry"

    def get_queryset(self, request):
        return super().get_queryset(request)


@admin.register(DeviceToken)
class DeviceTokenAdmin(admin.ModelAdmin):
    list_display = ['user', 'token_preview', 'is_active', 'created_at', 'last_active']
    list_filter = ['is_active', 'created_at', 'last_active']
    search_fields = ['user__email', 'user__first_name', 'user__last_name', 'token']
    readonly_fields = ['created_at', 'last_active']
    ordering = ['-last_active']

    def token_preview(self, obj):
        """Show a preview of the token for security"""
        return f"{obj.token[:20]}..." if obj.token else ""
    token_preview.short_description = 'Token Preview'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')
