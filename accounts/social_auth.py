from django.core.exceptions import ValidationError
from django.utils import timezone
from django.conf import settings
from django.db import transaction, IntegrityError, OperationalError
import requests
import json
import logging
from http import HTTPStatus
from functools import wraps
from requests.adapters import <PERSON>TT<PERSON><PERSON>pter
from urllib3.util.retry import Retry
import random
import time
import socket
from json.decoder import JSONDecodeError

# Import models at module level to avoid circular dependencies
from accounts.models.user import User
from accounts.models.social_account import SocialAccount

# Setup logging
logger = logging.getLogger(__name__)

# Constants
CONNECT_TIMEOUT = 3.05  # seconds - slightly longer than a multiple of 3 to avoid thundering herd
READ_TIMEOUT = 10  # seconds
DEFAULT_RETRIES = 3  # Number of retries for HTTP requests


def create_retry_session(
    retries=DEFAULT_RETRIES,
    backoff_factor=0.5,
    status_forcelist=(429, 500, 502, 503, 504),
    allowed_methods=("GET", "POST"),
    connect_timeout=CONNECT_TIMEOUT,
    read_timeout=READ_TIMEOUT,
):
    """
    Create a requests Session with retry capabilities for transient errors
    
    Parameters:
    - retries: How many times to retry the request
    - backoff_factor: Exponential backoff factor (with jitter)
    - status_forcelist: HTTP status codes that should trigger a retry
    - allowed_methods: HTTP methods that should be retried
    - connect_timeout: Timeout for establishing connection (seconds)
    - read_timeout: Timeout for reading response (seconds)
    """
    session = requests.Session()
    
    retry = Retry(
        total=retries,
        read=retries,
        connect=retries,
        backoff_factor=backoff_factor,
        status_forcelist=status_forcelist,
        allowed_methods=allowed_methods,
        respect_retry_after_header=True,
        # Add jitter to avoid thundering herd
        backoff_jitter=random.uniform(0, 0.1)
    )
    
    adapter = HTTPAdapter(max_retries=retry)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    
    # Explicitly set both connect and read timeouts
    session.request = lambda method, url, **kwargs: super(requests.Session, session).request(
        method=method,
        url=url,
        timeout=(connect_timeout, read_timeout),
        **kwargs
    )
    
    return session


def transaction_atomic_with_retry(max_attempts=3, retry_exceptions=(OperationalError, IntegrityError)):
    """
    Decorator for database transactions that should be retried on specific errors
    
    Parameters:
    - max_attempts: Maximum number of retry attempts
    - retry_exceptions: Tuple of exception types that should trigger a retry
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            attempts = 0
            last_error = None
            
            while attempts < max_attempts:
                try:
                    with transaction.atomic():
                        return func(*args, **kwargs)
                except retry_exceptions as e:
                    attempts += 1
                    last_error = e
                    logger.warning(
                        f"Transaction failed (attempt {attempts}/{max_attempts}): {str(e)}",
                        exc_info=True
                    )
                    if attempts >= max_attempts:
                        logger.error(
                            f"Transaction failed after {max_attempts} attempts: {str(e)}",
                            exc_info=True
                        )
                        raise
                    
                    # Add jitter to avoid thundering herd
                    sleep_time = 0.1 * (2 ** attempts) + random.uniform(0, 0.1)
                    time.sleep(sleep_time)
                except (User.DoesNotExist, SocialAccount.DoesNotExist, ValidationError) as e:
                    # Don't retry for these specific exceptions - just raise them
                    raise
                except Exception as e:
                    logger.error(f"Unexpected error in transaction: {str(e)}", exc_info=True)
                    raise ValidationError("An error occurred during database operation")
            
            # This code should never be reached, but just in case
            if last_error:
                raise last_error
            raise Exception("Transaction failed for unknown reason")
        
        return wrapper
    return decorator


def check_supported_provider(provider):
    """Validate that the provider is supported"""
    if provider not in [choice[0] for choice in SocialAccount.ProviderChoices.choices]:
        logger.warning(f"Attempt to use unsupported provider: {provider}")
        raise ValidationError(f"Unsupported authentication provider: {provider}")


@transaction_atomic_with_retry()
def get_or_create_social_user(provider, token):
    """Get or create user from social login."""
    check_supported_provider(provider)
    
    try:
        # Verify token first - outside of transaction to avoid long-running transactions
        social_data = verify_social_token(provider, token)
        
        # First part: Find existing account
        social_account = SocialAccount.objects.get_by_provider_id(
            provider=provider,
            provider_id=social_data['social_id']
        )
        
        if social_account:
            # Update token information if found - in separate transaction
            with transaction.atomic():
                social_account.update_token(
                    access_token=social_data.get('access_token', ''),
                    refresh_token=social_data.get('refresh_token', ''),
                    token_expiry=social_data.get('token_expiry')
                )
            return social_account.user, False
            
        # Try to find user by email
        try:
            # Second part: Get user by email
            user = User.objects.get(email=social_data['email'])
            
            # Third part: Link social account in separate transaction
            link_social_account(user, provider, social_data['social_id'], social_data)
            return user, False
        except User.DoesNotExist:
            # Fourth part: Create new user and link account
            with transaction.atomic():
                user = User.objects.create_user(
                    username=social_data['email'],
                    email=social_data['email'],
                    first_name=social_data.get('first_name', ''),
                    last_name=social_data.get('last_name', ''),
                    phone_number=social_data.get('phone_number', '')
                )
            
            # Fifth part: Link account in separate transaction
            link_social_account(user, provider, social_data['social_id'], social_data)
            return user, True
            
    except ValidationError:
        # Re-raise validation errors for domain rule violations
        raise
    except requests.Timeout as e:
        logger.error(f"Timeout connecting to {provider} API: {str(e)}", exc_info=True)
        raise ValidationError(f"Connection to {provider} authentication service timed out")
    except requests.ConnectionError as e:
        logger.error(f"Connection error with {provider} API: {str(e)}", exc_info=True)
        raise ValidationError(f"Cannot connect to {provider} authentication service")
    except requests.RequestException as e:
        # More general request exceptions
        logger.error(f"Request error with {provider} API: {str(e)}", exc_info=True)
        raise ValidationError(f"Error communicating with {provider} authentication service")
    except JSONDecodeError as e:
        logger.error(f"Invalid JSON response from {provider} API: {str(e)}", exc_info=True)
        raise ValidationError(f"Received invalid response from {provider} authentication service")
    except socket.gaierror as e:
        logger.error(f"DNS resolution error for {provider} API: {str(e)}", exc_info=True)
        raise ValidationError(f"Cannot resolve {provider} authentication service")
    except Exception as e:
        # Log unexpected errors but raise a generic message to avoid leaking implementation details
        logger.exception(f"Unexpected error in social authentication: {str(e)}")
        raise ValidationError("An error occurred during social authentication")


@transaction_atomic_with_retry()
def link_social_account(user, provider, social_id, extra_data):
    """Link social account to existing user."""
    check_supported_provider(provider)
    
    # Check if social account already exists - keep transaction scope small
    if SocialAccount.objects.filter(provider=provider, provider_id=social_id).exists():
        raise ValidationError('This social account is already linked to another user.')
    
    # Convert token_expiry to proper timezone-aware datetime
    token_expiry = extra_data.get('token_expiry')
    if isinstance(token_expiry, str):
        try:
            # Handle ISO format strings
            token_expiry = timezone.datetime.fromisoformat(token_expiry.replace('Z', '+00:00'))
        except (ValueError, TypeError):
            # Try parsing other datetime formats if ISO fails
            try:
                from dateutil import parser
                token_expiry = parser.parse(token_expiry)
            except (ImportError, ValueError, TypeError):
                token_expiry = None
    
    # Make token_expiry timezone-aware if it's naive
    if token_expiry and timezone.is_naive(token_expiry):
        token_expiry = timezone.make_aware(token_expiry)
    
    # Create social account
    social_account = SocialAccount.objects.create(
        user=user,
        provider=provider,
        provider_id=social_id,
        provider_email=extra_data.get('email', ''),
        access_token=extra_data.get('access_token', ''),
        refresh_token=extra_data.get('refresh_token', ''),
        token_expiry=token_expiry,
        is_active=True
    )
    return social_account


@transaction_atomic_with_retry(retry_exceptions=(OperationalError,))
def unlink_social_account(user, provider):
    """Unlink social account from user."""
    check_supported_provider(provider)
    user.social_auth_accounts.filter(provider=provider).delete()


def get_social_user_data(provider, token):
    """
    Get user data from social provider.
    
    This function makes actual API calls to provider-specific endpoints
    to retrieve user information based on the authentication token.
    """
    check_supported_provider(provider)
    
    # Create a session with retry capabilities and specific timeouts
    session = create_retry_session()
    
    # Provider-specific API endpoints and parameters
    provider_apis = {
        SocialAccount.ProviderChoices.GOOGLE: {
            'url': 'https://www.googleapis.com/oauth2/v3/userinfo',
            'headers': {'Authorization': f'Bearer {token}'},
            'params': {},
            'mapping': lambda data: {
                'social_id': data.get('sub'),
                'email': data.get('email'),
                'first_name': data.get('given_name', ''),
                'last_name': data.get('family_name', ''),
                'profile_picture': data.get('picture', ''),
                'access_token': token,
                'refresh_token': '',  # Google doesn't return refresh token here
                'token_expiry': None  # Set based on verify_token response
            }
        },
        SocialAccount.ProviderChoices.FACEBOOK: {
            'url': 'https://graph.facebook.com/me',
            'headers': {},
            'params': {'access_token': token, 'fields': 'id,email,first_name,last_name,picture'},
            'mapping': lambda data: {
                'social_id': data.get('id'),
                'email': data.get('email'),
                'first_name': data.get('first_name', ''),
                'last_name': data.get('last_name', ''),
                'profile_picture': data.get('picture', {}).get('data', {}).get('url', ''),
                'access_token': token,
                'refresh_token': '',  # Facebook doesn't use refresh tokens in the same way
                'token_expiry': None  # Set based on verify_token response
            }
        },
        SocialAccount.ProviderChoices.APPLE: {
            'url': 'https://appleid.apple.com/auth/userinfo',
            'headers': {'Authorization': f'Bearer {token}'},
            'params': {},
            'mapping': lambda data: {
                'social_id': data.get('sub'),
                'email': data.get('email'),
                'first_name': data.get('name', {}).get('firstName', ''),
                'last_name': data.get('name', {}).get('lastName', ''),
                'profile_picture': '',  # Apple doesn't provide profile pictures
                'access_token': token,
                'refresh_token': '',
                'token_expiry': None  # Set based on verify_token response
            }
        }
    }
    
    # Implement real API calls with proper error handling
    if provider in provider_apis:
        api_config = provider_apis[provider]
        try:
            # Make the API call with explicit timeouts
            response = session.get(
                api_config['url'],
                headers=api_config['headers'],
                params=api_config['params']
            )
            response.raise_for_status()
            
            # Parse the JSON response with explicit error handling
            try:
                data = response.json()
            except JSONDecodeError as e:
                logger.error(f"Invalid JSON from {provider} API: {str(e)}", exc_info=True)
                raise ValidationError(f"Received invalid response from {provider}")
            
            # Apply the provider-specific mapping function
            return api_config['mapping'](data)
            
        except requests.Timeout as e:
            logger.error(f"Timeout getting user data from {provider}: {str(e)}", exc_info=True)
            raise ValidationError(f"Connection to {provider} timed out")
        except requests.ConnectionError as e:
            logger.error(f"Connection error with {provider}: {str(e)}", exc_info=True)
            raise ValidationError(f"Cannot connect to {provider}")
        except requests.HTTPError as e:
            status_code = e.response.status_code if hasattr(e, 'response') else 'unknown'
            logger.error(f"HTTP error from {provider} (status {status_code}): {str(e)}", exc_info=True)
            raise ValidationError(f"Error communicating with {provider} (HTTP {status_code})")
        except requests.RequestException as e:
            logger.error(f"Request error with {provider}: {str(e)}", exc_info=True)
            raise ValidationError(f"Error connecting to {provider}")
    else:
        logger.warning(f"Attempt to use unsupported provider: {provider}")
        raise ValidationError(f"Unsupported authentication provider: {provider}")
    
    # This code should never be reached with the validation at the start
    # but keeping it for development/testing if needed
    return {
        'social_id': '12345',
        'email': '<EMAIL>',
        'first_name': 'Test',
        'last_name': 'User',
        'profile_picture': 'https://example.com/pic.jpg',
        'access_token': token,
        'refresh_token': 'refresh_dummy',
        'token_expiry': timezone.now() + timezone.timedelta(hours=1)
    }


def verify_social_token(provider, token):
    """
    Verify social token with provider.
    
    This function verifies the authentication token with the provider's API
    and extracts token expiry information.
    """
    check_supported_provider(provider)
    
    # Setup provider-specific verification endpoints
    verification_endpoints = {
        SocialAccount.ProviderChoices.GOOGLE: {
            'url': 'https://oauth2.googleapis.com/tokeninfo',
            'params': {'id_token': token},
            'headers': {},
            'expiry_key': 'exp',  # Google returns expiry as a timestamp
            'processor': lambda data, token: {
                # Get basic user data
                **get_social_user_data(provider, token),
                # Add token expiry
                'token_expiry': timezone.datetime.fromtimestamp(
                    int(data.get('exp', 0)), 
                    tz=timezone.utc
                ) if 'exp' in data else None
            }
        },
        SocialAccount.ProviderChoices.FACEBOOK: {
            'url': 'https://graph.facebook.com/debug_token',
            'params': {
                'input_token': token,
                'access_token': f"{settings.FACEBOOK_APP_ID}|{settings.FACEBOOK_APP_SECRET}"
            },
            'headers': {},
            'expiry_key': 'data.expires_at',  # Facebook nests the response
            'processor': lambda data, token: {
                # Get basic user data
                **get_social_user_data(provider, token),
                # Add token expiry
                'token_expiry': timezone.datetime.fromtimestamp(
                    int(data.get('data', {}).get('expires_at', 0)),
                    tz=timezone.utc
                ) if data.get('data', {}).get('expires_at') else None
            }
        },
        SocialAccount.ProviderChoices.APPLE: {
            'url': 'https://appleid.apple.com/auth/token',
            'params': {
                'client_id': settings.APPLE_CLIENT_ID,
                'client_secret': settings.APPLE_CLIENT_SECRET
            },
            'headers': {'Authorization': f'Bearer {token}'},
            'expiry_key': 'exp',
            'processor': lambda data, token: {
                # Get basic user data
                **get_social_user_data(provider, token),
                # Add token expiry (Apple uses JWT tokens)
                'token_expiry': timezone.datetime.fromtimestamp(
                    int(data.get('exp', 0)),
                    tz=timezone.utc
                ) if 'exp' in data else None
            }
        }
    }
    
    # Create a session with retry capabilities and specific timeouts
    session = create_retry_session()
    
    # Implement real API calls with proper error handling
    if provider in verification_endpoints:
        config = verification_endpoints[provider]
        try:
            # Make the API call with explicit timeouts
            response = session.get(
                config['url'],
                params=config['params'],
                headers=config['headers']
            )
            response.raise_for_status()
            
            # Parse the JSON response with explicit error handling
            try:
                data = response.json()
            except JSONDecodeError as e:
                logger.error(f"Invalid JSON from {provider} token verification: {str(e)}", exc_info=True)
                raise ValidationError(f"Received invalid response from {provider}")
            
            # Check for error responses
            if provider == SocialAccount.ProviderChoices.GOOGLE and 'error' in data:
                logger.warning(f"Google token error: {data.get('error_description', 'Unknown error')}")
                raise ValidationError("Invalid or expired Google token")
                
            elif provider == SocialAccount.ProviderChoices.FACEBOOK:
                fb_data = data.get('data', {})
                if not fb_data.get('is_valid', False):
                    logger.warning(f"Facebook token error: {fb_data.get('error', {}).get('message', 'Invalid token')}")
                    raise ValidationError("Invalid or expired Facebook token")
                    
            # Apply the provider-specific processor
            return config['processor'](data, token)
            
        except requests.Timeout as e:
            logger.error(f"Timeout verifying {provider} token: {str(e)}", exc_info=True)
            raise ValidationError(f"Connection to {provider} timed out during authentication")
        except requests.ConnectionError as e:
            logger.error(f"Connection error with {provider}: {str(e)}", exc_info=True)
            raise ValidationError(f"Cannot connect to {provider} for authentication")
        except requests.HTTPError as e:
            status_code = e.response.status_code if hasattr(e, 'response') else 'unknown'
            logger.error(f"HTTP error from {provider} (status {status_code}): {str(e)}", exc_info=True)
            raise ValidationError(f"Error during {provider} authentication (HTTP {status_code})")
        except requests.RequestException as e:
            logger.error(f"Request error with {provider}: {str(e)}", exc_info=True)
            raise ValidationError(f"Error connecting to {provider} authentication service")
    else:
        logger.warning(f"Attempt to use unsupported provider: {provider}")
        raise ValidationError(f"Unsupported authentication provider: {provider}")
    
    # This code should never be reached with the validation at the start
    # but keeping it for development/testing if needed
    return get_social_user_data(provider, token) 