from django.urls import path
from . import views
from .views import LoginView, LogoutView, HomeView, BusinessSignupView, CustomerSignupView

urlpatterns = [
    # Authentication
    path('login/', LoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('signup/', views.signup, name='signup'),
    path('signup/business/', BusinessSignupView.as_view(), name='business_signup'),
    path('signup/customer/', CustomerSignupView.as_view(), name='customer_signup'),
    path('mfa/verify/', views.mfa_verify, name='mfa_verify'),
    path('mfa/setup/', views.mfa_setup, name='mfa_setup'),
    path('profile/', views.profile, name='profile'),
    path('social/disconnect/<str:provider>/', views.social_disconnect, name='social_disconnect'),
    # Home/Dashboard
    path('home/', HomeView.as_view(), name='home'),
] 