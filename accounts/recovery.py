from django.utils import timezone
from django.core.exceptions import ValidationError
from django.conf import settings
from django.db import transaction, IntegrityError
import secrets
from datetime import timedelta
import hashlib
import hmac
import logging
import os
from .models.recovery_token import RecoveryToken, recovery_token_generator

# Set up logging
logger = logging.getLogger(__name__)

# Load HMAC key from secure source
def get_secure_hmac_key():
    """Get the HMAC key from environment or settings"""
    # First check environment (highest priority)
    env_key = os.environ.get('RECOVERY_TOKEN_HMAC_KEY')
    if env_key:
        return env_key
    
    # Then check settings
    if hasattr(settings, 'RECOVERY_TOKEN_HMAC_KEY'):
        if not settings.DEBUG:
            logger.warning("Using RECOVERY_TOKEN_HMAC_KEY from settings in production")
        return settings.RECOVERY_TOKEN_HMAC_KEY
    
    # Last resort fallback - ONLY for development
    if settings.DEBUG:
        return settings.SECRET_KEY
    
    # In production, don't fall back to SECRET_KEY
    logger.error("RECOVERY_TOKEN_HMAC_KEY not configured in production")
    raise RuntimeError("RECOVERY_TOKEN_HMAC_KEY must be configured in production")

# Get expiry hours from settings
RECOVERY_TOKEN_EXPIRY_HOURS = getattr(settings, 'RECOVERY_TOKEN_EXPIRY_HOURS', 24)

def generate_recovery_token(user, expiry_hours=None):
    """
    Generate a secure recovery token for password reset.
    
    Args:
        user: The user account to generate a token for
        expiry_hours: Hours until the token expires, defaults to settings value
        
    Returns:
        The plaintext token to be sent to the user
    """
    # Use provided expiry_hours or fall back to settings value
    expiry_hours = expiry_hours or RECOVERY_TOKEN_EXPIRY_HOURS
    
    # Consolidate on Django's built-in token generator
    raw_token = recovery_token_generator.make_token(user)
    
    # Create a more secure HMAC-based hash for storage
    hash_key = get_secure_hmac_key().encode()
    token_hash = hmac.new(
        hash_key,
        raw_token.encode(),
        digestmod=hashlib.sha256
    ).hexdigest()
    
    # Calculate expiry time
    expires_at = timezone.now() + timedelta(hours=expiry_hours)
    
    # Create and save the token with retry for handling hash collisions
    max_attempts = 3
    attempt = 0
    
    while attempt < max_attempts:
        try:
            with transaction.atomic():
                RecoveryToken.objects.create(
                    user=user,
                    token_hash=token_hash,
                    expires_at=expires_at
                )
            logger.info(f"Recovery token generated for user {user.id}")
            return raw_token
        except IntegrityError as e:
            attempt += 1
            if attempt >= max_attempts:
                logger.error(f"Failed to create recovery token after {max_attempts} attempts: {str(e)}")
                raise
            # Generate a new token hash for retry
            raw_token = recovery_token_generator.make_token(user)
            token_hash = hmac.new(
                hash_key,
                raw_token.encode(),
                digestmod=hashlib.sha256
            ).hexdigest()
            logger.warning(f"Token hash collision, retrying (attempt {attempt}/{max_attempts})")

def verify_recovery_token(raw_token, user=None):
    """
    Verify a recovery token and return the user if valid.
    Token is immediately invalidated upon successful verification to enforce one-time use.
    
    Args:
        raw_token: The plaintext token provided by the user
        user: Optional user object if already known
        
    Returns:
        The user associated with the token if valid, None otherwise
    """
    if not raw_token:
        return None

    # If user is provided, verify using Django's token generator
    if user and not recovery_token_generator.check_token(user, raw_token):
        return None
        
    # Create HMAC hash to compare with stored value
    hash_key = get_secure_hmac_key().encode()
    token_hash = hmac.new(
        hash_key,
        raw_token.encode(),
        digestmod=hashlib.sha256
    ).hexdigest()
    
    # Find and validate token using transaction to ensure atomicity
    try:
        with transaction.atomic():
            # Use our custom manager to find active tokens
            if user:
                token_obj = RecoveryToken.objects.for_user_and_token(user, token_hash)
            else:
                token_obj = RecoveryToken.objects.active().get(token_hash=token_hash)
            
            if not token_obj:
                return None
            
            # Immediately invalidate the token to enforce one-time use
            token_obj.invalidate()
            logger.info(f"Recovery token validated and invalidated for user {token_obj.user.id}")
            
            return token_obj.user
    except RecoveryToken.DoesNotExist:
        logger.warning(f"Recovery token not found or invalid")
        return None
    except IntegrityError as e:
        logger.error(f"Database integrity error during token verification: {str(e)}")
        return None

def initiate_recovery_process(email):
    """Start the account recovery process."""
    from .models.user import User
    try:
        user = User.objects.get(email=email)
        token = generate_recovery_token(user)
        return {
            'user': user,
            'token': token,
            'message': 'Recovery instructions have been sent.'
        }
    except User.DoesNotExist:
        logger.info(f"Recovery attempt for non-existent email: {email}")
        return {
            'message': 'If an account exists, recovery instructions have been sent.'
        }
    except IntegrityError as e:
        logger.error(f"Database integrity error during recovery initiation for {email}: {str(e)}")
        return {
            'message': 'If an account exists, recovery instructions have been sent.'
        }

def complete_recovery_process(user, method, **kwargs):
    """Complete the account recovery process."""
    try:
        if method == 'token':
            token = kwargs.get('token')
            
            # The verify_recovery_token function now handles invalidation
            if not verify_recovery_token(token, user):
                raise ValidationError('Invalid or expired recovery token.')
                
            # Generate new password
            new_password = secrets.token_urlsafe(12)
            
            with transaction.atomic():
                user.set_password(new_password)
                user.save()
            
            logger.info(f"Password reset completed for user {user.id}")
            return {
                'success': True,
                'new_password': new_password
            }
        
        elif method == 'social':
            # Handle social recovery
            provider = kwargs.get('provider')
            social_token = kwargs.get('token')
            
            if verify_social_identity(user, provider, social_token):
                new_password = secrets.token_urlsafe(12)
                
                with transaction.atomic():
                    user.set_password(new_password)
                    user.save()
                
                logger.info(f"Social recovery completed for user {user.id} using {provider}")
                return {
                    'success': True,
                    'new_password': new_password
                }
            raise ValidationError('Social identity verification failed.')
        
        raise ValidationError('Invalid recovery method.')
    except ValidationError:
        # Re-raise validation errors
        raise
    except IntegrityError as e:
        logger.error(f"Database integrity error during recovery completion: {str(e)}")
        raise ValidationError('Database error during account recovery.')
    except Exception as e:
        logger.error(f"Error in recovery completion: {str(e)}")
        raise ValidationError('An unexpected error occurred during account recovery.')

def get_recovery_methods(user):
    """Get available recovery methods for a user."""
    methods = ['email']
    
    if user.phone_number:
        methods.append('phone')
    
    if user.social_auth_accounts.exists():
        methods.append('social')
    
    return methods

def verify_social_identity(user, provider, token):
    """Verify user's identity using social provider."""
    # This would integrate with social auth providers
    raise NotImplementedError("Social recovery is not yet supported") 