from django.core.management.base import BaseCommand
from accounts.models.user import User

class Command(BaseCommand):
    help = 'Makes a user a superuser and staff member'

    def add_arguments(self, parser):
        parser.add_argument('email', type=str, help='The email of the user')

    def handle(self, *args, **options):
        email = options['email']
        try:
            user = User.objects.get(email=email)
            user.is_staff = True
            user.is_superuser = True
            user.save()
            self.stdout.write(self.style.SUCCESS(f'Successfully made {email} a superuser and staff member'))
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'User with email {email} does not exist')) 