from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.db.models import Q
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.utils import timezone
from django.conf import settings
from django.core.cache import cache
from ipware import get_client_ip
from django.db import transaction
from phonenumber_field.phonenumber import PhoneNumber
import phonenumbers
import logging
from functools import wraps

# Setup logging
logger = logging.getLogger(__name__)

User = get_user_model()

# Common helper functions for DRY implementation
def get_user_by_id(user_id):
    """Common implementation for get_user across all backends."""
    try:
        return User.objects.get(pk=user_id)
    except User.DoesNotExist:
        logger.warning(f"Failed user lookup for ID: {user_id}")
        return None

def normalize_phone_number(phone_number):
    """Normalize phone number to E164 format."""
    if not phone_number:
        return None
        
    try:
        # Handle various phone number formats
        if not phone_number.startswith('+'):
            # Try to parse assuming US format if no country code
            try:
                parsed = phonenumbers.parse(phone_number, "US")
                if phonenumbers.is_valid_number(parsed):
                    return PhoneNumber.from_string(
                        phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)
                    )
                return None
            except Exception as e:
                logger.debug(f"Error parsing phone number without country code: {e}")
                return None
        else:
            # Already has + prefix, create PhoneNumber directly
            phone = PhoneNumber.from_string(phone_number)
            if not phone.is_valid():
                return None
            return phone
    except Exception as e:
        logger.debug(f"Error normalizing phone number: {e}")
        return None

def is_valid_email(email):
    """Validate email format."""
    try:
        validate_email(email)
        return True
    except ValidationError:
        return False

def rate_limit_by_ip(request, max_attempts=10, timeout=3600):
    """Apply rate limiting by IP address."""
    if not request:
        return False
        
    client_ip, _ = get_client_ip(request)
    if not client_ip:
        return False
        
    key = f'login_attempts_{client_ip}'
    attempts = cache.get(key, 0)
    
    if attempts >= max_attempts:
        logger.warning(f"Rate limit exceeded for IP: {client_ip}")
        return True
        
    # Atomically increment the counter
    cache.set(key, attempts + 1, timeout)
    return False

class SocialAuthBackend(ModelBackend):
    """
    Authentication backend for social login providers.
    
    Supported kwargs:
    - provider: Social provider name (e.g., 'google', 'facebook')
    - uid: Unique ID from the provider
    - email: User's email from the provider
    """
    def authenticate(self, request, provider=None, uid=None, email=None, **kwargs):
        if not provider or not uid:
            logger.debug(f"Missing provider or UID for social auth: {provider}")
            return None
            
        try:
            # First try to find a dedicated SocialAccount model instance
            from accounts.models import SocialAccount
            
            try:
                # Try to find user through the proper relationship
                social_account = SocialAccount.objects.active().get(
                    provider=provider, 
                    provider_id=uid
                )
                logger.info(f"Found existing social account for {provider}:{uid}")
                return social_account.user
            except (SocialAccount.DoesNotExist, ImportError):
                logger.debug(f"No SocialAccount found, falling back to legacy method")
                # Fall back to the JSONField method if SocialAccount model not found
                # or if the account doesn't exist
                pass
                
            # Legacy: Try to find user by social account in JSONField
            user = User.objects.get(social_accounts__contains={provider: uid})
            logger.info(f"Found user with legacy social account data for {provider}:{uid}")
            return user
        except User.DoesNotExist:
            if email and is_valid_email(email):
                # Try to find user by email and link the account
                try:
                    with transaction.atomic():
                        user = User.objects.get(email=email)
                        # Link social account
                        if hasattr(user, 'social_accounts'):
                            user.social_accounts[provider] = uid
                            user.save(update_fields=['social_accounts'])
                            logger.info(f"Linked {provider} account to existing user: {email}")
                        return user
                except User.DoesNotExist:
                    logger.info(f"No user found with email from social provider: {email}")
                    return None
            logger.debug(f"Social auth failed for {provider}:{uid}")
            return None
    
    def get_user(self, user_id):
        return get_user_by_id(user_id)

class EmailOrPhoneBackend(ModelBackend):
    """
    Authentication backend that allows login with either email or phone number.
    
    Supported kwargs:
    - username: Email address or phone number
    - password: User's password
    """
    def authenticate(self, request, username=None, password=None, **kwargs):
        if not username or not password:
            return None
            
        # Determine if username is an email or phone number
        if '@' in username and is_valid_email(username):
            # Username is an email
            try:
                user = User.objects.get(email=username)
                logger.debug(f"Found user by email: {username}")
            except User.DoesNotExist:
                logger.info(f"No user found with email: {username}")
                return None
        else:
            # Username is a phone number
            normalized_phone = normalize_phone_number(username)
            if not normalized_phone:
                logger.debug(f"Invalid phone number format: {username}")
                return None
                
            try:
                user = User.objects.get(phone_number=normalized_phone)
                logger.debug(f"Found user by phone: {username}")
            except User.DoesNotExist:
                logger.info(f"No user found with phone number: {username}")
                return None
                
        if user.check_password(password):
            logger.info(f"Successful authentication for: {username}")
            return user
            
        logger.info(f"Failed password check for: {username}")
        return None
            
    def get_user(self, user_id):
        return get_user_by_id(user_id) 