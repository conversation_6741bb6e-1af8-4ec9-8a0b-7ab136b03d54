import { createContext, useContext, useState, useEffect } from 'react'
import axios from 'axios';

// Using direct connection to Django backend
const api = axios.create({
  baseURL: 'http://localhost:8000',
  withCredentials: true,
});

const FormsContext = createContext()

export function FormsProvider({ children }) {
  const [forms, setForms] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  const fetchTemplates = async () => {
    try {
      setLoading(true)
      console.log('Fetching templates from API...');
      const res = await api.get('/api/forms/templates/');
      console.log('API response:', res.data);
      
      // Transform the data to match our frontend format
      const transformedForms = res.data.results ? res.data.results.map(template => ({
        id: template.id,
        name: template.name,
        documentType: template.document_type,
        createdDate: template.created_at,
        status: template.status.charAt(0).toUpperCase() + template.status.slice(1), // Capitalize status
        createdBy: 'Admin', // Default value as the API doesn't provide this
        questions: template.content || [], // Parse content field as questions
      })) : [];
      
      setForms(transformedForms);
      setLoading(false)
    } catch (err) {
      console.error("Error fetching templates:", err);
      console.error("Error details:", err.response ? err.response.data : 'No response data');
      console.error("Error status:", err.response ? err.response.status : 'No status code');
      setError(`Failed to load templates: ${err.message}`);
      setLoading(false)
    }
  };

  const createTemplate = async (data) => {
    try {
      await api.post('/api/forms/templates/', data);
      fetchTemplates(); // Refresh the list
    } catch (err) {
      console.error("Error creating template:", err);
      throw err;
    }
  };

  const updateTemplate = async (id, data) => {
    try {
      await api.put(`/api/forms/templates/${id}/`, data);
      fetchTemplates(); // Refresh the list
    } catch (err) {
      console.error("Error updating template:", err);
      throw err;
    }
  };

  const deleteTemplate = async (id) => {
    try {
      await api.delete(`/api/forms/templates/${id}/`);
      fetchTemplates(); // Refresh the list
    } catch (err) {
      console.error("Error deleting template:", err);
      throw err;
    }
  };

  // Load templates when component mounts
  useEffect(() => {
    fetchTemplates();
  }, []);

  const addForm = (form) => {
    createTemplate({
      name: form.name,
      document_type: form.documentType,
      status: form.status.toLowerCase(),
      content: form.questions || {},
      business: 1 // Assuming business ID 1 for now
    });
  }

  const updateForm = (id, updated) => {
    updateTemplate(id, {
      name: updated.name,
      document_type: updated.documentType,
      status: updated.status.toLowerCase(),
      content: updated.questions || {},
      business: 1 // Assuming business ID 1 for now
    });
  }

  const deleteForm = (id) => {
    deleteTemplate(id);
  }

  return (
    <FormsContext.Provider value={{ 
      forms, 
      loading, 
      error, 
      setForms, 
      addForm, 
      updateForm, 
      deleteForm,
      fetchTemplates 
    }}>
      {children}
    </FormsContext.Provider>
  )
}

export function useForms() {
  return useContext(FormsContext)
} 